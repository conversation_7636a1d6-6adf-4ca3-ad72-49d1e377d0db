head	1.3;
access;
symbols;
locks; strict;
comment	@# @;


1.3
date	2010.05.26.14.29.52;	author dv1;	state Exp;
branches;
next	1.2;

1.2
date	2010.05.19.13.02.56;	author dv1;	state Exp;
branches;
next	1.1;

1.1
date	2008.01.30.03.01.55;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.3
log
@'vcavalcante - adding stop bar light control variables'
@@: 'fix FlyRight DR# 309.136
@@: .'
@
text
@LCA187CB50                       EQU LCBEXTRA                        +00000002 LB00001       LAV SMOKE DETECT
LCA187CB51                       EQU LCBEXTRA                        +00000003 LB00001       LEFT ITT GAGE 
LCA187CB52                       EQU LCBEXTRA                        +00000004 LB00001       RIGHT ITT GAGE 
LCA187CB61                       EQU LCBEXTRA                        +00000005 LB00001       PILOT CLOCK 
LCA187CB62                       EQU LCBEXTRA                        +00000006 LB00001       COPILOT CLOCK 
LCA187CB50                       EQU LCBEXTRA                        +00000002 LB00001       LAV SMOKE DETECT
LCA187CB51                       EQU LCBEXTRA                        +00000003 LB00001       LEFT ITT GAGE 
LCA187CB52                       EQU LCBEXTRA                        +00000004 LB00001       RIGHT ITT GAGE 
LCA187CB61                       EQU LCBEXTRA                        +00000005 LB00001       PILOT CLOCK 
LCA187CB62                       EQU LCBEXTRA                        +00000006 LB00001       COPILOT CLOCK 
LCA187CB50                       EQU LCBEXTRA                        +00000002 LB00001       LAV SMOKE DETECT
LCA187CB51                       EQU LCBEXTRA                        +00000003 LB00001       LEFT ITT GAGE 
LCA187CB52                       EQU LCBEXTRA                        +00000004 LB00001       RIGHT ITT GAGE 
LCA187CB61                       EQU LCBEXTRA                        +00000005 LB00001       PILOT CLOCK 
LCA187CB62                       EQU LCBEXTRA                        +00000006 LB00001       COPILOT CLOCK 
ioiolicelightsw                  EQU X_QDI                           +00000384 LB00001       Interbus DI-ICE LIGHT OVHD SWITCH
iolnavlightsw                    EQU X_QDI                           +00000385 LB00001       Interbus DI-NAV LIGHT OVHD SWITCH
ioltailfloodlightsw              EQU X_QDI                           +00000386 LB00001       Interbus DI-TAIL FLOOD LIGHT SWITCH
iolcabinlightswoff               EQU X_QDI                           +00000387 LB00001       Interbus DI-CABIN LIGHT SWITCH OFF
iolreadinglights                 EQU X_QDI                           +00000388 LB00001       Interbus DI-READING LIGHT SWITCH
iolcabinlightswfull              EQU X_QDI                           +00000389 LB00001       Interbus DI-CABIN LIGHT SWITCH FULL
XKPROPAUTOFEATHERARM             EQU X_EBB1                          +00000092 LB00001       Prop auto feather arm switch                         
XKPROPAUTOFEATHERTEST            EQU X_EBB1                          +00000093 LB00001       Prop auto feather test switch                        
XLLAFXREP                        EQU X_EQIVDO                        +00000112 LB00001       L AUTO FEATHER REPEATER ANNUN
XLRAFXREP                        EQU X_EQIVDO                        +00000113 LB00001       R AUTO FEATHER REPEATER ANNUN
ioiolicelightsw                 -EQU X_QDI                           +00000384 LB00001       Interbus DI-ICE LIGHT OVHD SWITCH        
iolicelightsw                    EQU X_QDI                           +00000384 LB00001       Interbus DI-ICE LIGHT OVHD SWITCH       
XKPROPTESTOVERSPEED              EQU X_EBB1                          +00000094 LB00001       Prop test overspeed switch                           
XKPROPTESTLOWPITCH               EQU X_EBB1                          +00000095 LB00001       Prop test low pitch switch                           

XLA224S4OFF                     -EQU                                 +00695290 LB00001       VENT BLOWER CNTL SW OFF (S4)
XLA224S4LO                       EQU                                 +00695290 LB00001       VENT BLOWER CNTL SW LO (S4)
gpsshm                           EQU                                 +00695308 IW00150STRUCT gpsshmtyp,../../../../shared/tst/nav/npgps_kln90.h   
gpsshm                          +EQU                                 +00695308 IW00150STRUCT gpsshmtyp,../../../../shared/tst/nav/npgps_kln90.h   
gpsshm                          +EQU                                 +00695308 EW00100STRUCT gpsshmtyp,../../../../shared/tst/nav/npgps kln90.h   
gpsshm                          +EQU                                 +00695308 IW00100STRUCT gpsshmtyp,../../../../shared/tst/nav/npgps kln90.h   
RLENGSTATE                       EQU X_LBW1                          +00000188 EW00001       L ENGINE STATE
RRENGSTATE                       EQU X_LBW1                          +00000192 EW00001       R ENGINE STATE
XNCBMF                          -EQU X_QDO1                          +00000505 LB00001       CAPT BOOM MIC
XNCBME                           EQU X_QDO1                          +00000505 LB00001       CAPT BOOM MIC ENBL
XNFBMF                          -EQU X_QDO1                          +00000510 LB00001       FO   BOOM MIC 
XNFBME                           EQU X_QDO1                          +00000510 LB00001       FO   BOOM MIC ENBL
XNCMMF                          -EQU X_QDO1                          +00000367 LB00001       CPT MASK MIC 
XNCMME                           EQU X_QDO1                          +00000367 LB00001       CPT MASK MIC ENBL
XNFOMMF                         -EQU X_QDO1                          +00000360 LB00001       F/O MASK MIC 
XNFOMME                          EQU X_QDO1                          +00000360 LB00001       F/O MASK MIC ENBL
XLEMRGLT                         EQU X_EQIVDI                        +00000112 LB00001       EMERGENCY COCKPIT LIGHTS SW DI
RCETDISC                         EQU R16EWBS                         +00000041 LB00001R2     ELEVATOR TRIM DISCONNECT FLAG 
lbaf_gs_gn_prog_active           EQU X_ABB1                          +00000426 LB00002       FD Glideslope gain program active
rwaf_gs_prog_gn                  EQU X_ABW1                          +00001032 EW00002       FD Glideslope gain program
rwaf_fd_gs_altrate_wo_gn         EQU X_ABW1                          +00001028 EW00001       FD GS altrate washout gain
TCDWASH                         -EQU                                 +******** IW00001       LFI Table                                            ************ ************ ************
TCDWASHZ                        -EQU                                 +00695236 IW00001       LFI Table                                            ************ 000000695240 000000695240
RC_DWASH                        -EQU                                 +00695240 EW00001       Dn Wash Ang                                          000000695240 000000695244 000000695244
RC_DWASH_ZERO_AOA               -EQU                                 +00695244 EW00001       Dn Wash Ang at zero AOA                              000000695244 000000695248 000000695248
RC_DELTA_AOA                    -EQU                                 +00695248 EW00001       Delta AOA                                            000000695248 000000695252 000000695252
RC_HORZ_STAB_AOA                -EQU                                 +00695252 EW00001       Horizontal Stab AOA                                  000000695252 000000695256 000000695256
RC_VERT_STAB_AOA                -EQU                                 +00695256 EW00001       Vertical Stab AOA                                    000000695256 000000695260 000000695260
RFGGEAR                         -EQU                                 +00695260 EW00001       Avg Gear Position (0-1)                              000000695260 000000695264 000000695264
RNBCNTIMER                      -EQU                                 +00695264 EW00001       ANTI_COLLISON BEACON CLK                             000000695264 000000695268 000000695268
RNBCNON                         -EQU                                 +00695268 EW00001       ANTI_COLLISON BEACON TIME ON                         000000695268 000000695272 000000695272
RNBCNOFF                        -EQU                                 +00695272 EW00001       ANTI_COLLISON BEACON TIME OFF                        000000695272 000000695276 000000695276
RNSTBTIMER                      -EQU                                 +00695276 EW00001       ANTI_COLLISON STROBE CLK                             000000695276 000000695280 000000695280
RNSTBON                         -EQU                                 +00695280 EW00001       ANTI_COLLISON STROBE TIME ON                         000000695280 000000695284 000000695284
RNSTBOFF                        -EQU                                 +00695284 EW00001       ANTI_COLLISON STROBE TIME OFF                        000000695284 000000695288 000000695288
XPTMPCNTLOFF                    -EQU                                 +00695288 LB00001       TEMP MODE CNTL OFF POSITION (S6)                     000000695288 000000695289 000000695289
XLA224S4HI                      -EQU                                 +00695289 LB00001       VENT BLOWER CNTL SW HIGH (S4)                        000000695289 000000695290 000000695290
XLA224S4LO                      -EQU                                 +00695290 LB00001       VENT BLOWER CNTL SW LO (S4)                          000000695290 000000695291 000000695291
XLA224S4AUTO                    -EQU                                 +00695291 LB00001       VENT BLOWER CNTL SW AUTO (S4)                        000000695291 000000695292 000000695292
LA224K141                       -EQU                                 +00695292 LB00001       FWD VENT BLOWER LOW SPEED RELAY                      000000695292 000000695293 000000695293
LA224K143                       -EQU                                 +00695293 LB00001       AFT VENT BLOWER LOW SPEED RELAY                      000000695293 000000695294 000000695294
LA224K203                       -EQU                                 +00695294 LB00001       FWD VENT BLOWER HIGH SPEED RELAY                     000000695294 000000695295 000000695295
LA224K204                       -EQU                                 +00695295 LB00001       AFT VENT BLOWER HIGH SPEED RELAY                     000000695295 000000695296 000000695296
LA185K3                         -EQU                                 +00695296 LB00001       CONDENSOR BLOWER MOTOR RELAY                         000000695296 000000695297 000000695297
LPRBAPR                         -EQU                                 +00695297 LB00001       RIGHT BLEED AIR PRESS REGULATOR                      000000695297 000000695298 000000695298
LPLBAPR                         -EQU                                 +00695298 LB00001       LEFT BLEED AIR PRESS REGULATOR                       000000695298 000000695299 000000695299
RC_DELTA_CAL                    -EQU                                 +00695300 EW00001       Delta Elev AOA Cal                                   000000695300 000000695304 000000695304
LVBEACONON                      -EQU                                 +00695304 LB00001       BEACON LIGHT ON STATE                                000000695304 000000695305 000000695305
LVBEACONOFF                     -EQU                                 +00695305 LB00001       BEACON LIGHT OFF STATE                               000000695305 000000695306 000000695306
TLVBEACONON                     -EQU                                 +00695306 LB00001       BEACON LIGHT ON TEMP STATE                           000000695306 000000695307 000000695307
TLVBEACONOFF                    -EQU                                 +00695307 LB00001       BEACON LIGHT OFF TEMP STATE                          000000695307 000000695308 000000695308
gpsshm                          -EQU                                 +00695308 IW00100STRUCT gpsshmtyp,../../../../shared/tst/nav/npgps kln90.h 
FLY_MISC_BASE                    EQU                                 +******** IW00001       LFI Table                                            ************ ************ ************
TCDWASH                          EQU FLY_MISC_BASE                   +******** IW00001       LFI Table                                            ************ ************ ************
TCDWASHZ                         EQU FLY_MISC_BASE                   +00000004 IW00001       LFI Table                                            ************ 000000695240 000000695240
RC_DWASH                         EQU FLY_MISC_BASE                   +00000008 EW00001       Dn Wash Ang                                          000000695240 000000695244 000000695244
RC_DWASH_ZERO_AOA                EQU FLY_MISC_BASE                   +00000012 EW00001       Dn Wash Ang at zero AOA                              000000695244 000000695248 000000695248
RC_DELTA_AOA                     EQU FLY_MISC_BASE                   +00000016 EW00001       Delta AOA                                            000000695248 000000695252 000000695252
RC_HORZ_STAB_AOA                 EQU FLY_MISC_BASE                   +00000020 EW00001       Horizontal Stab AOA                                  000000695252 000000695256 000000695256
RC_VERT_STAB_AOA                 EQU FLY_MISC_BASE                   +00000024 EW00001       Vertical Stab AOA                                    000000695256 000000695260 000000695260
RFGGEAR                          EQU FLY_MISC_BASE                   +00000028 EW00001       Avg Gear Position (0-1)                              000000695260 000000695264 000000695264
RNBCNTIMER                       EQU FLY_MISC_BASE                   +00000032 EW00001       ANTI_COLLISON BEACON CLK                             000000695264 000000695268 000000695268
RNBCNON                          EQU FLY_MISC_BASE                   +00000036 EW00001       ANTI_COLLISON BEACON TIME ON                         000000695268 000000695272 000000695272
RNBCNOFF                         EQU FLY_MISC_BASE                   +00000040 EW00001       ANTI_COLLISON BEACON TIME OFF                        000000695272 000000695276 000000695276
RNSTBTIMER                       EQU FLY_MISC_BASE                   +00000044 EW00001       ANTI_COLLISON STROBE CLK                             000000695276 000000695280 000000695280
RNSTBON                          EQU FLY_MISC_BASE                   +00000048 EW00001       ANTI_COLLISON STROBE TIME ON                         000000695280 000000695284 000000695284
RNSTBOFF                         EQU FLY_MISC_BASE                   +******** EW00001       ANTI_COLLISON STROBE TIME OFF                        000000695284 000000695288 000000695288
XPTMPCNTLOFF                     EQU FLY_MISC_BASE                   +00000056 LB00001       TEMP MODE CNTL OFF POSITION (S6)                     000000695288 000000695289 000000695289
XLA224S4HI                       EQU FLY_MISC_BASE                   +00000057 LB00001       VENT BLOWER CNTL SW HIGH (S4)                        000000695289 000000695290 000000695290
XLA224S4LO                       EQU FLY_MISC_BASE                   +00000058 LB00001       VENT BLOWER CNTL SW LO (S4)                          000000695290 000000695291 000000695291
XLA224S4AUTO                     EQU FLY_MISC_BASE                   +00000059 LB00001       VENT BLOWER CNTL SW AUTO (S4)                        000000695291 000000695292 000000695292
LA224K141                        EQU FLY_MISC_BASE                   +00000060 LB00001       FWD VENT BLOWER LOW SPEED RELAY                      000000695292 000000695293 000000695293
LA224K143                        EQU FLY_MISC_BASE                   +00000061 LB00001       AFT VENT BLOWER LOW SPEED RELAY                      000000695293 000000695294 000000695294
LA224K203                        EQU FLY_MISC_BASE                   +00000062 LB00001       FWD VENT BLOWER HIGH SPEED RELAY                     000000695294 000000695295 000000695295
LA224K204                        EQU FLY_MISC_BASE                   +00000063 LB00001       AFT VENT BLOWER HIGH SPEED RELAY                     000000695295 000000695296 000000695296
LA185K3                          EQU FLY_MISC_BASE                   +00000064 LB00001       CONDENSOR BLOWER MOTOR RELAY                         000000695296 000000695297 000000695297
LPRBAPR                          EQU FLY_MISC_BASE                   +00000065 LB00001       RIGHT BLEED AIR PRESS REGULATOR                      000000695297 000000695298 000000695298
LPLBAPR                          EQU FLY_MISC_BASE                   +00000066 LB00001       LEFT BLEED AIR PRESS REGULATOR                       000000695298 000000695299 000000695299
RC_DELTA_CAL                     EQU FLY_MISC_BASE                   +00000068 EW00001       Delta Elev AOA Cal                                   000000695300 000000695304 000000695304
LVBEACONON                       EQU FLY_MISC_BASE                   +00000072 LB00001       BEACON LIGHT ON STATE                                000000695304 000000695305 000000695305
LVBEACONOFF                      EQU FLY_MISC_BASE                   +00000073 LB00001       BEACON LIGHT OFF STATE                               000000695305 000000695306 000000695306
TLVBEACONON                      EQU FLY_MISC_BASE                   +00000074 LB00001       BEACON LIGHT ON TEMP STATE                           000000695306 000000695307 000000695307
TLVBEACONOFF                     EQU FLY_MISC_BASE                   +00000075 LB00001       BEACON LIGHT OFF TEMP STATE                          000000695307 000000695308 000000695308
gpsshm                           EQU FLY_MISC_BASE                   +00000076 IW00100STRUCT gpsshmtyp,../../../../shared/tst/nav/npgps kln90.h 
rx_ao                           -EQU                                 +00694720 EW00088       Interbus AO Base                                     000000694720 000000695072 000000695072
rx_ao_01                        -EQU rx_ao                           +******** EW00004       Interbus AO- 1 Array                                 000000694720 000000694736 000000695072
rx_ao_02                        -EQU rx_ao                           +00000016 EW00004       Interbus AO- 2 Array                                 000000694736 000000694752 000000695072
rx_ao_03                        -EQU rx_ao                           +00000032 EW00004       Interbus AO- 3 Array                                 000000694752 000000694768 000000695072
rx_ao_04                        -EQU rx_ao                           +00000048 EW00004       Interbus AO- 4 Array                                 000000694768 000000694784 000000695072
rx_ao_05                        -EQU rx_ao                           +00000064 EW00004       Interbus AO- 5 Array                                 000000694784 000000694800 000000695072
rx_ao_06                        -EQU rx_ao                           +00000080 EW00004       Interbus AO- 6 Array                                 000000694800 000000694816 000000695072
rx_ao_07                        -EQU rx_ao                           +******** EW00004       Interbus AO- 7 Array                                 000000694816 000000694832 000000695072
rx_ao_08                        -EQU rx_ao                           +00000112 EW00004       Interbus AO- 8 Array                                 000000694832 000000694848 000000695072
rx_ao_09                        -EQU rx_ao                           +00000128 EW00004       Interbus AO- 9 Array                                 000000694848 000000694864 000000695072
rx_ao_10                        -EQU rx_ao                           +00000144 EW00004       Interbus AO-10 Array                                 000000694864 000000694880 000000695072
rx_ao_11                        -EQU rx_ao                           +00000160 EW00004       Interbus AO-11 Array                                 000000694880 000000694896 000000695072
rx_ao_12                        -EQU rx_ao                           +00000176 EW00004       Interbus AO-12 Array                                 000000694896 000000694912 000000695072
rx_ao_13                        -EQU rx_ao                           +00000192 EW00004       Interbus AO-13 Array                                 000000694912 000000694928 000000695072
rx_ao_14                        -EQU rx_ao                           +00000208 EW00004       Interbus AO-14 Array                                 000000694928 000000694944 000000695072
rx_ao_15                        -EQU rx_ao                           +00000224 EW00004       Interbus AO-15 Array                                 000000694944 000000694960 000000695072
rx_ao_16                        -EQU rx_ao                           +00000240 EW00004       Interbus AO-16 Array                                 000000694960 000000694976 000000695072
rx_ao_17                        -EQU rx_ao                           +00000256 EW00004       Interbus AO-17 Array                                 000000694976 000000694992 000000695072
rx_ao_18                        -EQU rx_ao                           +00000272 EW00004       Interbus AO-18 Array                                 000000694992 000000695008 000000695072
rx_ao_19                        -EQU rx_ao                           +00000288 EW00004       Interbus AO-19 Array                                 000000695008 000000695024 000000695072
rx_ao_20                        -EQU rx_ao                           +00000304 EW00004       Interbus AO-20 Array                                 000000695024 000000695040 000000695072
rx_aosf_01                      -EQU rx_ao                           +00000320 EW00004       Interbus AO/SF- 1 Array                              000000695040 000000695056 000000695072
rx_aosf_02                      -EQU rx_ao                           +00000336 EW00004       Interbus AO/SF- 2 Array                              000000695056 000000695072 000000695072
rx_ai                           -EQU                                 +00695072 EW00040       Interbus AI Base                                     000000695072 ************ ************
rx_ai_01                        -EQU rx_ai                           +******** EW00004       Interbus AI- 1 Array                                 000000695072 000000695088 ************
rx_ai_02                        -EQU rx_ai                           +00000016 EW00004       Interbus AI- 2 Array                                 000000695088 000000695104 ************
rx_ai_03                        -EQU rx_ai                           +00000032 EW00004       Interbus AI- 3 Array                                 000000695104 000000695120 ************
rx_ai_04                        -EQU rx_ai                           +00000048 EW00004       Interbus AI- 4 Array                                 000000695120 000000695136 ************
rx_ai_05                        -EQU rx_ai                           +00000064 EW00004       Interbus AI- 5 Array                                 000000695136 000000695152 ************
rx_ai_06                        -EQU rx_ai                           +00000080 EW00004       Interbus AI- 6 Array                                 000000695152 000000695168 ************
rx_ai_07                        -EQU rx_ai                           +******** EW00004       Interbus AI- 7 Array                                 000000695168 000000695184 ************
rx_ai_08                        -EQU rx_ai                           +00000112 EW00004       Interbus AI- 8 Array                                 000000695184 000000695200 ************
rx_ai_09                        -EQU rx_ai                           +00000128 EW00004       Interbus AI- 9 Array                                 000000695200 000000695216 ************
rx_ai_10                        -EQU rx_ai                           +00000144 EW00004       Interbus AI-10 Array             
rx_ao                            EQU                                 +00694720 EW00100       Interbus AO Base                                     000000694720 000000695072 000000695072
rx_ao_01                         EQU rx_ao                           +******** EW00004       Interbus AO- 1 Array                                 000000694720 000000694736 000000695072
rx_ao_02                         EQU rx_ao                           +00000016 EW00004       Interbus AO- 2 Array                                 000000694736 000000694752 000000695072
rx_ao_03                         EQU rx_ao                           +00000032 EW00004       Interbus AO- 3 Array                                 000000694752 000000694768 000000695072
rx_ao_04                         EQU rx_ao                           +00000048 EW00004       Interbus AO- 4 Array                                 000000694768 000000694784 000000695072
rx_ao_05                         EQU rx_ao                           +00000064 EW00004       Interbus AO- 5 Array                                 000000694784 000000694800 000000695072
rx_ao_06                         EQU rx_ao                           +00000080 EW00004       Interbus AO- 6 Array                                 000000694800 000000694816 000000695072
rx_ao_07                         EQU rx_ao                           +******** EW00004       Interbus AO- 7 Array                                 000000694816 000000694832 000000695072
rx_ao_08                         EQU rx_ao                           +00000112 EW00004       Interbus AO- 8 Array                                 000000694832 000000694848 000000695072
rx_ao_09                         EQU rx_ao                           +00000128 EW00004       Interbus AO- 9 Array                                 000000694848 000000694864 000000695072
rx_ao_10                         EQU rx_ao                           +00000144 EW00004       Interbus AO-10 Array                                 000000694864 000000694880 000000695072
rx_ao_11                         EQU rx_ao                           +00000160 EW00004       Interbus AO-11 Array                                 000000694880 000000694896 000000695072
rx_ao_12                         EQU rx_ao                           +00000176 EW00004       Interbus AO-12 Array                                 000000694896 000000694912 000000695072
rx_ao_13                         EQU rx_ao                           +00000192 EW00004       Interbus AO-13 Array                                 000000694912 000000694928 000000695072
rx_ao_14                         EQU rx_ao                           +00000208 EW00004       Interbus AO-14 Array                                 000000694928 000000694944 000000695072
rx_ao_15                         EQU rx_ao                           +00000224 EW00004       Interbus AO-15 Array                                 000000694944 000000694960 000000695072
rx_ao_16                         EQU rx_ao                           +00000240 EW00004       Interbus AO-16 Array                                 000000694960 000000694976 000000695072
rx_ao_17                         EQU rx_ao                           +00000256 EW00004       Interbus AO-17 Array                                 000000694976 000000694992 000000695072
rx_ao_18                         EQU rx_ao                           +00000272 EW00004       Interbus AO-18 Array                                 000000694992 000000695008 000000695072
rx_ao_19                         EQU rx_ao                           +00000288 EW00004       Interbus AO-19 Array                                 000000695008 000000695024 000000695072
rx_ao_20                         EQU rx_ao                           +00000304 EW00004       Interbus AO-20 Array                                 000000695024 000000695040 000000695072
rx_ao_21                         EQU rx_ao                           +00000320 EW00004       Interbus AO-21 Array                                 000000695024 000000695040 000000695072
rx_ao_22                         EQU rx_ao                           +00000336 EW00004       Interbus AO-22 Array                                 000000695024 000000695040 000000695072
rx_aosf_01                       EQU rx_ao                           +00000352 EW00004       Interbus AO/SF- 1 Array                              000000695040 000000695056 000000695072
rx_aosf_02                       EQU rx_ao                           +00000368 EW00004       Interbus AO/SF- 2 Array                              000000695056 000000695072 000000695072
rx_ai                            EQU                                 +00695120 EW00052       Interbus AI Base                                     000000695072 ************ ************
rx_ai_01                         EQU rx_ai                           +******** EW00004       Interbus AI- 1 Array                                 000000695072 000000695088 ************
rx_ai_02                         EQU rx_ai                           +00000016 EW00004       Interbus AI- 2 Array                                 000000695088 000000695104 ************
rx_ai_03                         EQU rx_ai                           +00000032 EW00004       Interbus AI- 3 Array                                 000000695104 000000695120 ************
rx_ai_04                         EQU rx_ai                           +00000048 EW00004       Interbus AI- 4 Array                                 000000695120 000000695136 ************
rx_ai_05                         EQU rx_ai                           +00000064 EW00004       Interbus AI- 5 Array                                 000000695136 000000695152 ************
rx_ai_06                         EQU rx_ai                           +00000080 EW00004       Interbus AI- 6 Array                                 000000695152 000000695168 ************
rx_ai_07                         EQU rx_ai                           +******** EW00004       Interbus AI- 7 Array                                 000000695168 000000695184 ************
rx_ai_08                         EQU rx_ai                           +00000112 EW00004       Interbus AI- 8 Array                                 000000695184 000000695200 ************
rx_ai_09                         EQU rx_ai                           +00000128 EW00004       Interbus AI- 9 Array                                 000000695200 000000695216 ************
rx_ai_10                         EQU rx_ai                           +00000144 EW00004       Interbus AI-10 Array   
rx_ai_11                         EQU rx_ai                           +00000160 EW00004       Interbus AI-11 Array   
rx_ai_12                         EQU rx_ai                           +00000176 EW00004       Interbus AI-12 Array   
FLY_MISC_BASE                    EQU                                 +******** IB00100       LFI Table                                            ************ ************ ************

FLY_MISC_BASE                   -EQU                                 +******** IW00001       LFI Table     
FLY_MISC_BASE                    EQU                                 +******** IW00200       Misc Var Base
LCFLDIRPWR                       EQU LCBEXTRA                        +******** LB00002       FL DIR PWR 
rwaf_turnknob_leftbank           EQU X_ABW1                          +******** EW00001       AP turnknob left bank angle (Deg)
rwaf_turnknob_rightbank          EQU X_ABW1                          +******** EW00001       AP turnknob right bank angle (Deg)
io_ap_turnknob_leftbank          EQU X_ABW1                          +******** EW00001       AP turnknob left bank AI
io_ap_turnknob_rightbank         EQU X_ABW1                          +******** EW00001       AP turnknob right bank AI
iwaf_turnknob_leftbank           EQU X_ABW1                          +******** IW00001       AP turnknob left bank AI scaling table
iwaf_turnknob_rightbank          EQU X_ABW1                          +******** IW00001       AP turnknob right bank AI scaling table
lbe_atg_ignitionsw2              EQU X_EBB1                          +******** LB00002       ATG engine starter only switch hook
lbe_atg_ignitionsw1              EQU X_EBB1                          +******** LB00002       ATG engine starter/ignition switch hook
EITTSS                           EQU X_EBW1                          +******** EW00002       ITT Steady State
EITTDT                           EQU X_EBW1                          +******** EW00002       ITT Dot
lbe_high_itt_start_effect        EQU X_EBB1                          +******** LB00002       High ITT engine start effect flag 
lbe_starter_ovrht                EQU X_EBB1                          +******** LB00002       Eng starter overheat flag
rwe_eng_starter_on_timer         EQU X_EBW1                          +******** EW00002       Eng starter on timer
rwe_eng_starter_off_timer        EQU X_EBW1                          +******** EW00002       Eng starter off timer
rwe_eng_strt_ovrht               EQU X_EBW1                          +00000944 EW00002       Eng starter overheat value
rwe_eng_starter_heat             EQU X_EBW1                          +00000952 EW00002       Eng starter acculated heat percentage
rwe_starter_heatup_rate          EQU X_EBW1                          +00000960 EW00001       Eng starter heat accumulation rate  
rwe_starter_cooldn_rate          EQU X_EBW1                          +00000964 EW00001       Eng starter cool down rate  
rwaf_rbeng_trq_diff              EQU X_ABW1                          +00001064 EW00001       Rudder Boost eng torque difference
rwaf_rudbst_yawrate_ign          EQU X_ABW1                          +00001068 EW00001       Rudder Boost yaw rate integral gain
rwaf_rudbst_int_in               EQU X_ABW1                          +00001072 EW00001       Rudder Boost integrator input
rwaf_rudbst_int_term             EQU X_ABW1                          +00001076 EW00001       Rudder Boost integrator output term
rwaf_rudbst_int_lim              EQU X_ABW1                          +00001080 EW00001       Rudder Boost integrator output limit
rwaf_rudbst_yawrate_pgn          EQU X_ABW1                          +00001084 EW00001       Rudder Boost yaw rate proportional gain
rwaf_rudbst_pro_term             EQU X_ABW1                          +00001088 EW00001       Rudder Boost proportional term
rwaf_rudboost_ctl_cmd            EQU X_ABW1                          +00001092 EW00001       Rudder Boost control command
rwaf_rudboost_ctl_lim            EQU X_ABW1                          +00001096 EW00001       Rudder Boost control command limit
lbaf_rudboost_pwr_ok             EQU X_ABB1                          +00000428 LB00001       Rudder Boost power OK flag
lbaf_rudboost_armed              EQU X_ABB1                          +00000429 LB00001       Rudder Boost armed flag
lbaf_rudboost_engaged            EQU X_ABB1                          +00000430 LB00001       Rudder Boost engaged flag
RUDBST_ENG                       EQU X_ABB1                          +00000431 LB00001       Rudder Boost engaged flag      
lbaf_fltdir_pwr                  EQU X_ABB1                          +00000432 LB00002       Flight Director Power OK Flag
lbaf_fltdir_cb1pwr               EQU X_ABB1                          +00000432 LB00001       Flight Director CB 1 Power Flag
lbaf_fltdir_cb2pwr               EQU X_ABB1                          +00000433 LB00001       Flight Director CB 2 Power Flag
lbaf_yawservo_cbpwr              EQU X_ABB1                          +00000434 LB00001       AP Yaw Servo CB Power Flag
lbhy_ldg_gear_cbpwr              EQU lochg_gear_fly                  +00000134 LB00001       LANDING GEAR POWER CIRCUIT BREAKER PWR
lbhy_wrnhrn_silence_cbpwr        EQU lochg_gear_fly                  +00000135 LB00001       WARNING HORN SILENCE PWR
lbhy_wrnhrn_cbpwr                EQU lochg_gear_fly                  +00000136 LB00001       WARNING HORN PWR
lbhy_squatsw_cbpwr               EQU lochg_gear_fly                  +00000137 LB00001       SQUAT SWITCH PWR
lbhy_grposlgts_cbpwr             EQU lochg_gear_fly                  +00000138 LB00001       GEAR POSITION LIGHTS PWR
lbm_annun_cbpwr                  EQU lochg_gear_fly                  +00000139 LB00001       ANNUCIATOR INDICATOR PWR
iwaf_gs_radalt_gn                EQU X_ABW1                          +00001100 IW00001       FD GS Radio Alt gain table
rwaf_gs_radalt_gn                EQU X_ABW1                          +00001104 EW00002       FD GS Radio Alt gain 
lbaf_radalt_valid                EQU X_ABB1                          +00000435 LB00002       AP Radio Alt Valid Flag
iwaf_fd_vs_hld_gn                EQU X_ABW1                          +00001112 IW00001       FD VS Hold gain table
LCBB2                           -EQU X_LBW1                          +00000200 LB00001        PSEUDO SUB-BASE
LC0001                          -EQU LCBB2                           +00000001 LB00001L6      L ENGINE IGNITION
LC0002                          -EQU LCBB2                           +00000002 LB00001L6      R ENGINE IGNITION
LC0003                          -EQU LCBB2                           +00000003 LB00001L6      L CONTINUOUS IGNITION
LC0004                          -EQU LCBB2                           +00000004 LB00001L6      R CONTINUOUS IGNITION
LC0005                          -EQU LCBB2                           +00000005 LB00001L6      L ENGINE OIL PRESSURE
LC0006                          -EQU LCBB2                           +00000006 LB00001L6      R ENGINE OIL PRESSURE
LC0008                          -EQU LCBB2                           +00000008 LB00001L6      CALL SYSTEM
LC0009                          -EQU LCBB2                           +00000009 LB00001L6      L ENGINE OIL QUANTITY
LC0010                          -EQU LCBB2                           +00000010 LB00001L6      R ENGINE OIL QUANTITY
LC0011                          -EQU LCBB2                           +00000011 LB00001L6      L ENGINE OIL TEMPERATURE
LC0012                          -EQU LCBB2                           +00000012 LB00001L6      R ENGINE OIL TEMPERATURE
LC0013                          -EQU LCBB2                           +00000013 LB00001L6      L WING LANDING LIGHTS
LC0014                          -EQU LCBB2                           +00000014 LB00001L6      L WING LANDING LIGHT CONTROL
LC0015                          -EQU LCBB2                           +00000015 LB00001L6      R WING LANDING LIGHTS
LC0016                          -EQU LCBB2                           +00000016 LB00001L6      R WING LANDING LIGHT CONTROL
LC0017                          -EQU LCBB2                           +00000017 LB00001L6      L GROUND FLOOD LIGHTS
LC0018                          -EQU LCBB2                           +00000018 LB00001L6      R GROUND FLOOD LIGHTS
LC0019                          -EQU LCBB2                           +00000019 LB00001L6      L NOSE GEAR LANDING & TAXI LT
LC0020                          -EQU LCBB2                           +00000020 LB00001L6      NOSE GEAR LANDING & TAXI LTS
LC0021                          -EQU LCBB2                           +00000021 LB00001L6      WING & NACELLE FLOOD LTS
LC0022                          -EQU LCBB2                           +00000022 LB00001L6      MASTER WARNING
LC0023                          -EQU LCBB2                           +00000023 LB00001L6      GROUND CONTROL RELAY
LC0024                          -EQU LCBB2                           +00000024 LB00001L6      GROUND CONTROL RELAY
LC0025                          -EQU LCBB2                           +00000025 LB00001L6      L STALL WARN & VANE HEATER
LC0026                          -EQU LCBB2                           +00000026 LB00001L6      R STALL WARN STICK SHAKER
LC0027                          -EQU LCBB2                           +00000027 LB00001L6      PITOT HEATER ADVISORY
LC0028                          -EQU LCBB2                           +00000028 LB00001L6      L AFT FUEL TNK BOOST PMP A
LC0029                          -EQU LCBB2                           +00000029 LB00001L6      L AFT FUEL TNK BOOST PMP B
LC0030                          -EQU LCBB2                           +00000030 LB00001L6      L AFT FUEL TNK BOOST PMP C
LC0031                          -EQU LCBB2                           +00000031 LB00001L6      R AFT FUEL TANK BOOST PMP A
LC0032                          -EQU LCBB2                           +00000032 LB00001L6      R AFT FUEL TANK BOOST PMP B
LC0033                          -EQU LCBB2                           +00000033 LB00001L6      R AFT FUEL TANK BOOST PMP C
LC0034                          -EQU LCBB2                           +00000034 LB00001L6      L FWD FUEL TNK BOOST PMP A
LC0035                          -EQU LCBB2                           +00000035 LB00001L6      L FWD FUEL TNK BOOST PMP B
LC0036                          -EQU LCBB2                           +00000036 LB00001L6      L FWD FUEL TNK BOOST PMP C
LC0037                          -EQU LCBB2                           +00000037 LB00001L6      R FWD FUEL TANK BOOST PMP A
LC0038                          -EQU LCBB2                           +00000038 LB00001L6      R FWD FUEL TANK BOOST PMP B
LC0039                          -EQU LCBB2                           +00000039 LB00001L6      R FWD FUEL TANK BOOST PMP C
LC0040                          -EQU LCBB2                           +00000040 LB00001L6      ENGINE START PUMP
LC0041                          -EQU LCBB2                           +00000041 LB00001L6      EMER LTS ARM & CHARGE
LC0042                          -EQU LCBB2                           +00000042 LB00001L6      CABIN STANDBY LIGHTS
LC0043                          -EQU LCBB2                           +00000043 LB00001L6      L ENGINE COWL A/I VALVE
LC0044                          -EQU LCBB2                           +00000044 LB00001L6      R ENGINE COWL A/I VALVE
LC0045                          -EQU LCBB2                           +00000045 LB00001L6      L ANTI-ICE VALVE CAUTION
LC0046                          -EQU LCBB2                           +00000046 LB00001L6      R ANTI-ICE VALVE CAUTION
LC0047                          -EQU LCBB2                           +00000047 LB00001L6      L FUEL HEAT CONTROL
LC0048                          -EQU LCBB2                           +00000048 LB00001L6      R FUEL HEAT CONTROL
LC0049                          -EQU LCBB2                           +00000049 LB00001L6      L FUEL HEAT ON ADVISORY
LC0050                          -EQU LCBB2                           +00000050 LB00001L6      R FUEL HEAT ON ADVISORY
LC0051                          -EQU LCBB2                           +00000051 LB00001L6      L HYD PUMP CONTROL
LC0052                          -EQU LCBB2                           +******** LB00001L6      R HYD PUMP CONTROL
LC0053                          -EQU LCBB2                           +******** LB00001L6      L ENGINE  L A/I VALVE
LC0054                          -EQU LCBB2                           +******** LB00001L6      R ENGINE  L A/I VALVE
LC0055                          -EQU LCBB2                           +******** LB00001L6      L ENGINE  R A/I VALVE
LC0056                          -EQU LCBB2                           +00000056 LB00001L6      R ENGINE  R A/I VALVE
LC0057                          -EQU LCBB2                           +00000057 LB00001L6      CABIN TEMP
LC0058                          -EQU LCBB2                           +00000058 LB00001L6      115VAC UTILITY OUTLETS
LC0059                          -EQU LCBB2                           +00000059 LB00001L6      R ENG LOOP A FIRE DET
LC0060                          -EQU LCBB2                           +00000060 LB00001L6      L EPR INDICATOR
LC0061                          -EQU LCBB2                           +00000061 LB00001L6      R EPR INDICATOR
LC0062                          -EQU LCBB2                           +00000062 LB00001L6      R.A.T. & PROBE HEATER
LC0063                          -EQU LCBB2                           +00000063 LB00001L6      L HYDRAULIC OIL QUANTITY
LC0064                          -EQU LCBB2                           +00000064 LB00001L6      R HYDRAULIC OIL QUANTITY
LC0065                          -EQU LCBB2                           +00000065 LB00001L6      CTR FWD FUEL TANK BOOST PMP C
LC0066                          -EQU LCBB2                           +00000066 LB00001L6      CTR FWD FUEL TANK BOOST PMP B
LC0067                          -EQU LCBB2                           +00000067 LB00001L6      CTR FWD FUEL TANK BOOST PMP A
LC0068                          -EQU LCBB2                           +00000068 LB00001L6      CTR AFT FUEL TANK BOOST PMP C
LC0069                          -EQU LCBB2                           +00000069 LB00001L6      CTR AFT FUEL TANK BOOST PMP B
LC0070                          -EQU LCBB2                           +00000070 LB00001L6      CTR AFT FUEL TANK BOOST PMP A
LC0071                          -EQU LCBB2                           +00000071 LB00001L6      FIRE AGENT LOW PRES CAUTION
LC0072                          -EQU LCBB2                           +00000072 LB00001L6      R RADIO DC BUS FEED
LC0073                          -EQU LCBB2                           +00000073 LB00001L6      L REVERSER UNLOCK ADVISORY
LC0074                          -EQU LCBB2                           +00000074 LB00001L6      R REVERSER UNLOCK ADVISORY
LC0075                          -EQU LCBB2                           +00000075 LB00001L6      L FUEL FLOW INDICATOR
LC0076                          -EQU LCBB2                           +00000076 LB00001L6      R FUEL FLOW INDICATOR
LC0077                          -EQU LCBB2                           +00000077 LB00001L6      L RADIO DC BUS FEED
LC0078                          -EQU LCBB2                           +00000078 LB00001L6      RADIO RACK FAN A
LC0079                          -EQU LCBB2                           +00000079 LB00001L6      RADIO RACK FAN B
LC0080                          -EQU LCBB2                           +00000080 LB00001L6      RADIO RACK FAN C
LC0082                          -EQU LCBB2                           +00000082 LB00001L6      RADIO RACK FAN CAUTION
LC0083                          -EQU LCBB2                           +00000083 LB00001L6      R AUX HYD PUMP-A
LC0084                          -EQU LCBB2                           +00000084 LB00001L6      R AUX HYD PUMP-B
LC0085                          -EQU LCBB2                           +00000085 LB00001L6      R AUX HYD PUMP-C
LC0089                          -EQU LCBB2                           +00000089 LB00001L6      L FLAP POSITION IND
LC0090                          -EQU LCBB2                           +00000090 LB00001L6      R FLAP POSITION
LC0095                          -EQU LCBB2                           +00000095 LB00001L6      FIRE EXTINGUISHING CONT BOTL
LC0096                          -EQU LCBB2                           +******** LB00001L6      FIRE EXTINGUISHING CONT BOTL
LC0097                          -EQU LCBB2                           +00000097 LB00001L6      MAX AIRSPEED WARNING
LC0098                          -EQU LCBB2                           +******** LB00001L6      LOWER ANTI-COLLISION
LC0099                          -EQU LCBB2                           +00000099 LB00001L6      UPPER ANTI-COLLISION
LC0102                          -EQU LCBB2                           +******** LB00001L6      L HYD SYSTEM PRESSURE
LC0103                          -EQU LCBB2                           +00000103 LB00001L6      R HYD SYSTEM PRESSURE
LC0104                          -EQU LCBB2                           +00000104 LB00001L6      L HYD BRAKE PRESSURE
LC0105                          -EQU LCBB2                           +00000105 LB00001L6      R HYD BRAKE PRESSURE
LC0106                          -EQU LCBB2                           +00000106 LB00001L6      GROUND REFUELING
LC0107                          -EQU LCBB2                           +00000107 LB00001L6      GROUND REFUEL
LC0108                          -EQU LCBB2                           +00000108 LB00001L6      DC TRANSFER BUS SENSING
LC0109                          -EQU LCBB2                           +00000109 LB00001L6      L SUPPLY AIR PRESSURE
LC0110                          -EQU LCBB2                           +00000110 LB00001L6      R SUPPLY AIR PRESSURE
LC0111                          -EQU LCBB2                           +00000111 LB00001L6      L TEMP CONTROL VALVE POSN
LC0112                          -EQU LCBB2                           +00000112 LB00001L6      R TEMP CONTROL VALVE POSN
LC0113                          -EQU LCBB2                           +00000113 LB00001L6      PNEUMATIC PRESSURE
LC0114                          -EQU LCBB2                           +00000114 LB00001L6      L INSTRUMENT TRANSFORMER
LC0115                          -EQU LCBB2                           +00000115 LB00001L6      R INSTRUMENT TRANSFORMER
LC0121                          -EQU LCBB2                           +00000121 LB00001L6      GALLEY CONTROL
LC0122                          -EQU LCBB2                           +00000122 LB00001L6      L FUEL FILTER PRESS DROP CAUT
LC0123                          -EQU LCBB2                           +00000123 LB00001L6      R FUEL FILTER PRESS DROP CAUT
LC0124                          -EQU LCBB2                           +00000124 LB00001L6      DOOR WARNING
LC0125                          -EQU LCBB2                           +00000125 LB00001L6      L XFMR RECTIFIER-2
LC0126                          -EQU LCBB2                           +00000126 LB00001L6      L XFMR RECTIFIER-2
LC0127                          -EQU LCBB2                           +00000127 LB00001L6      L XFMR RECTIFIER-2
LC0128                          -EQU LCBB2                           +00000128 LB00001L6      L XFMR RECTIFIER-1
LC0129                          -EQU LCBB2                           +00000129 LB00001L6      L XFMR RECTIFIER-1
LC0130                          -EQU LCBB2                           +00000130 LB00001L6      L XFMR RECTIFIER-1
LC0131                          -EQU LCBB2                           +00000131 LB00001L6      R XFMR RECTIFIER
LC0132                          -EQU LCBB2                           +00000132 LB00001L6      R XFMR RECTIFIER
LC0133                          -EQU LCBB2                           +00000133 LB00001L6      R XFMR RECTIFIER
LC0134                          -EQU LCBB2                           +00000134 LB00001L6      XFMR RECTIFIER POWER
LC0135                          -EQU LCBB2                           +00000135 LB00001L6      XFMR RECTIFIER POWER
LC0136                          -EQU LCBB2                           +00000136 LB00001L6      XFMR RECTIFIER POWER
LC0137                          -EQU LCBB2                           +00000137 LB00001L6      L OIL STRAINER CLOGGING CAUT
LC0138                          -EQU LCBB2                           +00000138 LB00001L6      R OIL STRAINER CLOGGING CAUT
LC0139                          -EQU LCBB2                           +00000139 LB00001L6      L OIL PRESS LOW CAUTION
LC0140                          -EQU LCBB2                           +00000140 LB00001L6      R OIL PRESS LOW CAUTION
LC0141                          -EQU LCBB2                           +00000141 LB00001L6      L INLET FUEL PRESS LO CAUT
LC0142                          -EQU LCBB2                           +00000142 LB00001L6      R INLET FUEL PRESS LO CAUT
LC0146                          -EQU LCBB2                           +00000146 LB00001L6      BATTERY CHARGE (ALL 3 PHASES)
LC0149                          -EQU LCBB2                           +00000149 LB00001L6      R CSD DISCONNECT
LC0150                          -EQU LCBB2                           +00000150 LB00001L6      L CSD DISCONNECT
LC0151                          -EQU LCBB2                           +00000151 LB00001L6      L CSD OIL PRESS LOW CAUT
LC0152                          -EQU LCBB2                           +00000152 LB00001L6      R CSD OIL PRESS LOW CAUT
LC0153                          -EQU LCBB2                           +00000153 LB00001L6      L FUEL QUANTITY IND
LC0154                          -EQU LCBB2                           +00000154 LB00001L6      R FUEL QUANTITY IND
LC0155                          -EQU LCBB2                           +00000155 LB00001L6      CTR FUEL QUANTITY IND
LC0156                          -EQU LCBB2                           +00000156 LB00001L6      FUEL QTY XFER RELAY
LC0157                          -EQU LCBB2                           +00000157 LB00001L6      APU OIL PRESS & TEMP CAUT
LC0158                          -EQU LCBB2                           +00000158 LB00001L6      L DC VOLTMETER
LC0159                          -EQU LCBB2                           +00000159 LB00001L6      EMERGENCY BUS FEED
LC0160                          -EQU LCBB2                           +00000160 LB00001L6      R DC VOLTMETER
LC0161                          -EQU LCBB2                           +00000161 LB00001L6      DC BUS OFF SENSING
LC0162                          -EQU LCBB2                           +00000162 LB00001L6      DC TRANSFER BUS FEED
LC0163                          -EQU LCBB2                           +00000163 LB00001L6      DC BUS OFF CAUTION
LC0164                          -EQU LCBB2                           +00000164 LB00001L6      EMER DC BUS SENSING
LC0165                          -EQU LCBB2                           +00000165 LB00001L6      EMER POWER IN USE LIGHTS
LC0166                          -EQU LCBB2                           +00000166 LB00001L6      DC BUS CROSSTIE CONTROL
LC0167                          -EQU LCBB2                           +00000167 LB00001L6      CHARGER & TRANSFER RLY CONT
LC0173                          -EQU LCBB2                           +00000173 LB00001L6      BATTERY RELAY
LC0174                          -EQU LCBB2                           +00000174 LB00001L6      AUX PITOT HEATER
LC0175                          -EQU LCBB2                           +00000175 LB00001L6      BUS OUT LTS DC TRANSFER
LC0176                          -EQU LCBB2                           +00000176 LB00001L6      L WINDSHIELD WIPER
LC0177                          -EQU LCBB2                           +00000177 LB00001L6      R WINDSHIELD WIPER
LC0178                          -EQU LCBB2                           +00000178 LB00001L6      FUEL QUANTITY TOTALIZER
LC0179                          -EQU LCBB2                           +00000179 LB00001L6      L CSD OIL TEMPERATURE
LC0180                          -EQU LCBB2                           +00000180 LB00001L6      R CSD OIL TEMPERATURE
LC0181                          -EQU LCBB2                           +00000181 LB00001L6      L FUEL TEMPERATURE
LC0182                          -EQU LCBB2                           +00000182 LB00001L6      R FUEL TEMPERATURE
LC0183                          -EQU LCBB2                           +00000183 LB00001L6      EMER AC BUS SENSING
LC0184                          -EQU LCBB2                           +00000184 LB00001L6      EMERGENCY INVERTER
LC0185                          -EQU LCBB2                           +00000185 LB00001L6      EMERGENCY AC BUS FEED
LC0186                          -EQU LCBB2                           +00000186 LB00001L6      ALTERNATE EMER AC BUS FEED
LC0187                          -EQU LCBB2                           +00000187 LB00001L6      LANDING GEAR WARNING
LC0188                          -EQU LCBB2                           +00000188 LB00001L6      WARNING LIGHT DIMMING
LC0189                          -EQU LCBB2                           +00000189 LB00001L6      L AIR CONDITION CONTROL VALVE
LC0190                          -EQU LCBB2                           +00000190 LB00001L6      R AIR CONDITION CONTROL VALVE
LC0191                          -EQU LCBB2                           +00000191 LB00001L6      R ENG LOOP B FIRE DET
LC0192                          -EQU LCBB2                           +00000192 LB00001L6      FIRE WARNING LIGHTS
LC0193                          -EQU LCBB2                           +00000193 LB00001L6      L GENERATOR CONTROL
LC0194                          -EQU LCBB2                           +00000194 LB00001L6      R GENERATOR CONTROL
LC0195                          -EQU LCBB2                           +00000195 LB00001L6      APU GENERATOR CONTROL
LC0196                          -EQU LCBB2                           +00000196 LB00001L6      L DEAD BUS SLV RYS & AC BUS
LC0197                          -EQU LCBB2                           +00000197 LB00001L6      R DEAD BUS SLV RYS & AC BUS
LC0198                          -EQU LCBB2                           +00000198 LB00001L6      L AC BUS SENSING
LC0199                          -EQU LCBB2                           +00000199 LB00001L6      L AC BUS SENSING
LC0200                          -EQU LCBB2                           +00000200 LB00001L6      L AC BUS SENSING
LC0201                          -EQU LCBB2                           +00000201 LB00001L6      R AC BUS SENSING
LC0202                          -EQU LCBB2                           +00000202 LB00001L6      R AC BUS SENSING
LC0203                          -EQU LCBB2                           +00000203 LB00001L6      R AC BUS SENSING
LC0207                          -EQU LCBB2                           +00000207 LB00001L6      RAM AIR VALVE
LC0208                          -EQU LCBB2                           +00000208 LB00001L6      L HYD PRESS LOW CAUTION
LC0209                          -EQU LCBB2                           +00000209 LB00001L6      R HYD PRESS LOW CAUTION
LC0210                          -EQU LCBB2                           +00000210 LB00001L6      L HYD TEMP HI CAUTION
LC0211                          -EQU LCBB2                           +00000211 LB00001L6      R HYD TEMP HI CAUTION
LC0212                          -EQU LCBB2                           +00000212 LB00001L6      ANTI-SKID OUTBOARD POWER
LC0213                          -EQU LCBB2                           +00000213 LB00001L6      ANTI-SKID INBOARD POWER
LC0214                          -EQU LCBB2                           +00000214 LB00001L6      L ANTI-SKID TEST
LC0215                          -EQU LCBB2                           +00000215 LB00001L6      R ANTI-SKID TEST
LC0216                          -EQU LCBB2                           +00000216 LB00001L6      ANTI-SKID PRKING BRAKE CONT
LC0217                          -EQU LCBB2                           +00000217 LB00001L6      TAIL COMPTMNT TEMP HI WARNING
LC0218                          -EQU LCBB2                           +00000218 LB00001L6      L REVERSER ACCUM LOW CAUT
LC0219                          -EQU LCBB2                           +00000219 LB00001L6      R REVERSER ACCUM LOW CAUT
LC0220                          -EQU LCBB2                           +00000220 LB00001L6      AIRFOIL ADVISORY PRES ABNORM
LC0221                          -EQU LCBB2                           +00000221 LB00001L6      SUPPLY AIR TMP CAUT & PRES HI
LC0222                          -EQU LCBB2                           +00000222 LB00001L6      L AIR SUPPLY TEMP CAUTION
LC0223                          -EQU LCBB2                           +00000223 LB00001L6      WING AND TAIL VALVE
LC0224                          -EQU LCBB2                           +00000224 LB00001L6      SUPPLY AIR PRESS REG VALVE
LC0225                          -EQU LCBB2                           +00000225 LB00001L6      L SUPPLY AIR PRESS AUG VALVE
LC0226                          -EQU LCBB2                           +00000226 LB00001L6      R SUPPLY AIR PRESS AUG VALVE
LC0227                          -EQU LCBB2                           +00000227 LB00001L6      MASTER CAUTION
LC0228                          -EQU LCBB2                           +00000228 LB00001L6      RUDD TRAVEL UNRESTRICTED ADV
LC0229                          -EQU LCBB2                           +00000229 LB00001L6      RUDD CONT MANUAL ADVISORY
LC0239                          -EQU LCBB2                           +00000239 LB00001L6      GROUND SERVICE BUS POWER
LC0240                          -EQU LCBB2                           +00000240 LB00001L6      GROUND SERVICE BUS POWER
LC0241                          -EQU LCBB2                           +00000241 LB00001L6      GROUND SERVICE BUS POWER
LC0242                          -EQU LCBB2                           +00000242 LB00001L6      GROUND SERVICE BUS CONTROL
LC0243                          -EQU LCBB2                           +00000243 LB00001L6      SPOILER CONTROL
LC0244                          -EQU LCBB2                           +00000244 LB00001L6      SPOILER CONTROL
LC0245                          -EQU LCBB2                           +00000245 LB00001L6      MANUAL TEMP CONT CABIN
LC0246                          -EQU LCBB2                           +00000246 LB00001L6      CABIN TEMP CONTROL
LC0247                          -EQU LCBB2                           +00000247 LB00001L6      MANUAL TEMP CONT COCKPIT
LC0248                          -EQU LCBB2                           +00000248 LB00001L6      COCKPIT TEMP CONTROL
LC0250                          -EQU LCBB2                           +00000250 LB00001L6      EXTERNAL POWER RELAYS
LC0251                          -EQU LCBB2                           +00000251 LB00001L6      L SUPPLY AIR TMP HI CAUTION
LC0252                          -EQU LCBB2                           +00000252 LB00001L6      R SUPPLY AIR TEMP HI CAUTION
LC0253                          -EQU LCBB2                           +00000253 LB00001L6      L HEAT EXCHANGER COOL FAN
LC0254                          -EQU LCBB2                           +00000254 LB00001L6      R HEAT EXCHANGER COOL FAN
LC0255                          -EQU LCBB2                           +00000255 LB00001L6      L HEAT EXCHANGER COOL FAN
LC0256                          -EQU LCBB2                           +00000256 LB00001L6      R HEAT EXCHANGER COOL FAN
LC0257                          -EQU LCBB2                           +00000257 LB00001L6      L HEAT EXCHANGER COOL FAN
LC0258                          -EQU LCBB2                           +00000258 LB00001L6      R HEAT EXCHANGER COOL FAN
LC0259                          -EQU LCBB2                           +00000259 LB00001L6      A/C AUTO SHUT-OFF & PURGING
LC0260                          -EQU LCBB2                           +00000260 LB00001L6      CABIN PRESSURE CONTROL-A
LC0261                          -EQU LCBB2                           +00000261 LB00001L6      CAB PRESS MAN/AUTO CONT-A
LC0262                          -EQU LCBB2                           +00000262 LB00001L6      L REVERSER ACCUM SHUT-OFF
LC0263                          -EQU LCBB2                           +00000263 LB00001L6      R REVERSER ACCUM SHUT-OFF
LC0264                          -EQU LCBB2                           +00000264 LB00001L6      TAKEOFF WARNING
LC0265                          -EQU LCBB2                           +00000265 LB00001L6      CAPTAIN'S PITOT HEATER
LC0266                          -EQU LCBB2                           +00000266 LB00001L6      F/O'S PITOT HEATER
LC0267                          -EQU LCBB2                           +00000267 LB00001L6      MAP & BRIEFCASE
LC0269                          -EQU LCBB2                           +00000269 LB00001L6      CAPT'S RED FILL LIGHTS
LC0270                          -EQU LCBB2                           +00000270 LB00001L6      F/O'S RED FILL LIGHTS
LC0271                          -EQU LCBB2                           +00000271 LB00001L6      CTR PED RED FILL LIGHTS
LC0272                          -EQU LCBB2                           +00000272 LB00001L6      COCKPIT OVHD RED FILL
LC0273                          -EQU LCBB2                           +00000273 LB00001L6      L AC BUS PHA A
LC0274                          -EQU LCBB2                           +00000274 LB00001L6      L AC BUS PHA B
LC0275                          -EQU LCBB2                           +00000275 LB00001L6      L AC BUS PHA C
LC0276                          -EQU LCBB2                           +00000276 LB00001L6      R AC BUS PHA A
LC0277                          -EQU LCBB2                           +00000277 LB00001L6      R AC BUS PHA B
LC0278                          -EQU LCBB2                           +00000278 LB00001L6      R AC BUS PHA C
LC0280                          -EQU LCBB2                           +00000280 LB00001L6      FIRE WARNING BELL
LC0281                          -EQU LCBB2                           +00000281 LB00001L6      L ENG LOOP B FIRE DET
LC0282                          -EQU LCBB2                           +00000282 LB00001L6      L ENG LOOP A FIRE DET
LC0288                          -EQU LCBB2                           +00000288 LB00001L6      L START VALVE OPEN ADVISORY
LC0289                          -EQU LCBB2                           +00000289 LB00001L6      R START VALVE OPEN ADVISORY
LC0290                          -EQU LCBB2                           +00000290 LB00001L6      APU DOOR CONTROL
LC0291                          -EQU LCBB2                           +00000291 LB00001L6      APU CONTROL
LC0292                          -EQU LCBB2                           +00000292 LB00001L6      COCKPIT WHITE FLOOD LIGHTS
LC0293                          -EQU LCBB2                           +00000293 LB00001L6      COCKPIT OVERHEAD WHITE FLOOD
LC0294                          -EQU LCBB2                           +00000294 LB00001L6      L FUEL HEAT TIMER
LC0295                          -EQU LCBB2                           +00000295 LB00001L6      R FUEL HEAT TIMER
LC0297                          -EQU LCBB2                           +00000297 LB00001L6      L RAIN REPELLENT
LC0298                          -EQU LCBB2                           +00000298 LB00001L6      R RAIN REPELLENT
LC0299                          -EQU LCBB2                           +00000299 LB00001L6      INST PANEL FLOURESCENT
LC0300                          -EQU LCBB2                           +00000300 LB00001L6      CAP'S INST PNL WITE INTEGRL
LC0303                          -EQU LCBB2                           +00000303 LB00001L6      F/O'S INST PNL INTEGRAL LTS
LC0306                          -EQU LCBB2                           +00000306 LB00001L6      CENTER INST PNL INTEGRAL LTS
LC0309                          -EQU LCBB2                           +00000309 LB00001L6      PEDESTAL INTEGRAL LTS
LC0312                          -EQU LCBB2                           +00000312 LB00001L6      OVHD PNL FWD INTEGRAL LTS
LC0315                          -EQU LCBB2                           +00000315 LB00001L6      OVHD PNL AFT INTEGRAL LTS
LC0319                          -EQU LCBB2                           +00000319 LB00001L6      WHEEL WELL SERV LTS & 28B UTI
LC0321                          -EQU LCBB2                           +00000321 LB00001L6      INSTRUMENT VIBRATOR
LC0322                          -EQU LCBB2                           +00000322 LB00001L6      BUS OUT LTS EMERGENCY AC
LC0323                          -EQU LCBB2                           +00000323 LB00001L6      APU LOOP A FIRE DET
LC0324                          -EQU LCBB2                           +00000324 LB00001L6      APU LOOP B FIRE DET
LC0325                          -EQU LCBB2                           +00000325 LB00001L6      CHRGR & TRANSFER RELAY
LC0326                          -EQU LCBB2                           +00000326 LB00001L6      BUS OUT LTS EMERGENCY DC
LC0327                          -EQU LCBB2                           +00000327 LB00001L6      EMER BUS WARN LTS PROTECT RLY
LC0328                          -EQU LCBB2                           +00000328 LB00001L6      RADIO RACK VENTURI
LC0329                          -EQU LCBB2                           +00000329 LB00001L6      CHGR & XFR BUS GRD SER INTRLK
LC0330                          -EQU LCBB2                           +00000330 LB00001L6      L ICE PROTECT AUG VALVE
LC0331                          -EQU LCBB2                           +00000331 LB00001L6      R ICE PROTECT AUG VALVE
LC0332                          -EQU LCBB2                           +00000332 LB00001L6      L WINDSHIELD ANTI-ICE
LC0333                          -EQU LCBB2                           +00000333 LB00001L6      CNTR WINDSHIELD ANTI-ICE
LC0334                          -EQU LCBB2                           +00000334 LB00001L6      R WINDSHIELD ANTI-ICE
LC0335                          -EQU LCBB2                           +00000335 LB00001L6      L AIR CONDITION REG VALVE
LC0336                          -EQU LCBB2                           +00000336 LB00001L6      R AIR CONDITION REG VALVE
LC0337                          -EQU LCBB2                           +00000337 LB00001L6      CAP'S;F/O'S;CTR ANTI-FOG
LC0338                          -EQU LCBB2                           +00000338 LB00001L6      COCKPIT WINDOW ANTI-FOG CONT
LC0339                          -EQU LCBB2                           +00000339 LB00001L6      COCKPIT WNDW ANTIFOG CLE VIEW
LC0340                          -EQU LCBB2                           +00000340 LB00001L6      WATER SERV PANEL HEATER
LC0341                          -EQU LCBB2                           +00000341 LB00001L6      STALL WARNING & VANE HEATER
LC0364                          -EQU LCBB2                           +00000364 LB00001L6      CABIN LO PRESS WARNING
LC0367                          -EQU LCBB2                           +00000367 LB00001L6      SMOKE DETECTORS
LC0377                          -EQU LCBB2                           +00000377 LB00001L6      ELEVATOR POWER ON ADVISORY
LC0378                          -EQU LCBB2                           +00000378 LB00001L6      ALTERNATE THUNDERSTORM LTS
LC0379                          -EQU LCBB2                           +00000379 LB00001L6      FWD PASS ENTR STAIRS CONTROL
LC0381                          -EQU LCBB2                           +00000381 LB00001L6      FWD PASS ENTR STR CAR MOTORS
LC0382                          -EQU LCBB2                           +00000382 LB00001L6      FWD PASS ENTR STR CAR MOTORS
LC0387                          -EQU LCBB2                           +00000387 LB00001L6      EMERGENCY LTS CHARGING
LC0403                          -EQU LCBB2                           +00000400 LB00001L6      ALTERNATE GEAR POWER CONT
LC0405                          -EQU LCBB2                           +00000401 LB00001L6      APU FIRE WARNING HORN
LC0415                          -EQU LCBB2                           +00000402 LB00001L6      L STATIC PORT HEATER
LC0416                          -EQU LCBB2                           +00000403 LB00001L6      R STATIC PORT HEATER
LC0427                          -EQU LCBB2                           +00000404 LB00001L6      GALLEY POWER-1
LC0428                          -EQU LCBB2                           +00000405 LB00001L6      GALLEY POWER-1
LC0429                          -EQU LCBB2                           +00000406 LB00001L6      GALLEY POWER-1
LC0430                          -EQU LCBB2                           +00000407 LB00001L6      GALLEY POWER-2
LC0431                          -EQU LCBB2                           +00000408 LB00001L6      GALLEY POWER-2
LC0432                          -EQU LCBB2                           +00000409 LB00001L6      GALLEY POWER-2
LC0447                          -EQU LCBB2                           +00000410 LB00001L6      RADIO INSTRUMENT MONITOR
LC0452                          -EQU LCBB2                           +00000411 LB00001L6      L REVERSER THRUST ADVISORY
LC0453                          -EQU LCBB2                           +00000412 LB00001L6      R REVERSER THRUST ADVISORY
LC0454                          -EQU LCBB2                           +00000413 LB00001L6      COCKPIT DOOR INLOCK
LC0475                          -EQU LCBB2                           +00000414 LB00001L6      OVERHEAT WHEELWELL SENSOR
LC0476                          -EQU LCBB2                           +00000415 LB00001L6      RUDDER Q LIMITER
LC0479                          -EQU LCBB2                           +00000416 LB00001L6      CAPT & F/O'S WHITE FLOOD LTS
LC0480                          -EQU LCBB2                           +00000417 LB00001L6      SLAT DISAGREEMENT ADV
LC0487                          -EQU LCBB2                           +00000418 LB00001L6      L STALL WARN STICK SHAKER
LC0496                          -EQU LCBB2                           +00000419 LB00001L6      A/C TURBINE NOZZLE CONTROL
LC0503                          -EQU LCBB2                           +00000420 LB00001L6      R STALL WARN COMPUTER
LC0504                          -EQU LCBB2                           +00000421 LB00001L6      L STALL WARN COMPUTER
LC0505                          -EQU LCBB2                           +00000422 LB00001L6      BLEED AIR FILTER
LC0530                          -EQU LCBB2                           +00000423 LB00001L6      L SYS SLAT EXT'D RELAY
LC0531                          -EQU LCBB2                           +00000424 LB00001L6      R SYS SLAT EXT'D RELAY
LC0550                          -EQU LCBB2                           +00000425 LB00001L6      R STALL WARNING FAIL ADV
LC0551                          -EQU LCBB2                           +00000426 LB00001L6      L STALL WARN RECOGNITION
LC0552                          -EQU LCBB2                           +00000427 LB00001L6      R STALL WARN RECOGNITION
LC0574                          -EQU LCBB2                           +00000428 LB00001L6      SPARE ANNUN LIGHTS POWER
LC0588                          -EQU LCBB2                           +00000429 LB00001L6      FLAP POSN RUDDER STOP ADV
LC0589                          -EQU LCBB2                           +00000430 LB00001L6      CAP'S STALL WARNING
LC0590                          -EQU LCBB2                           +00000431 LB00001L6      F/O'S STALL WARNING
LC0655                          -EQU LCBB2                           +00000432 LB00001L6      CARGO COMPTMNT HEATER PHA A
LC0656                          -EQU LCBB2                           +00000433 LB00001L6      CARGO COMPTMNT HEATER PHA B
LC0657                          -EQU LCBB2                           +00000434 LB00001L6      CARGO COMPTMNT HEATER PHA C
LC0659                          -EQU LCBB2                           +00000435 LB00001L6      L HEAT EXCHGR COOL FAN CONT
LC0660                          -EQU LCBB2                           +00000436 LB00001L6      R HEAT EXCHGR COOL FAN CONT
LC0662                          -EQU LCBB2                           +00000437 LB00001L6      R AUX HYD PUMP CONTROL
LC0676                          -EQU LCBB2                           +00000438 LB00001L6      BATTERY FAN
LC0685                          -EQU LCBB2                           +00000440 LB00001L6      PROXIMITY SWITCH CONTROL
LC0687                          -EQU LCBB2                           +00000441 LB00001L6      AVIONICS COOLING FANS
LC0693                          -EQU LCBB2                           +00000442 LB00001L6      CABIN PRESS CONTROL-B
LC0694                          -EQU LCBB2                           +00000443 LB00001L6      CABIN PRESS MAN/AUTO-B
LC0715                          -EQU LCBB2                           +00000444 LB00001L6      INSTRUMENT COOLING FANS
LC1007                          -EQU LCBB2                           +00000451 LB00001L6      VHF COMM-1 TRANSCEIVER
LC1009                          -EQU LCBB2                           +00000452 LB00001L6      CAPTAIN'S COMPASS
LC1010                          -EQU LCBB2                           +00000453 LB00001L6      F/O'S COMPASS
LC1013                          -EQU LCBB2                           +00000454 LB00001L6      F/O HDG OUTPUT-B
LC1014                          -EQU LCBB2                           +00000455 LB00001L6      F/O'S HEADING OUTPUT A
LC1015                          -EQU LCBB2                           +00000456 LB00001L6      CAPTAIN'S HEADING OUTPUT A
LC1016                          -EQU LCBB2                           +00000457 LB00001L6      CAP'S HEADING OUTPUT B 28VAC
LC1017                          -EQU LCBB2                           +00000458 LB00001L6      ADC
LC1018                          -EQU LCBB2                           +00000459 LB00001L6      F/O'S TURN & SLIP
LC1019                          -EQU LCBB2                           +00000460 LB00001L6      VERTICAL GRO-1
LC1020                          -EQU LCBB2                           +00000461 LB00001L6      VERTICAL GYRO-2
LC1022                          -EQU LCBB2                           +00000462 LB00001L6      VHF NAV-1
LC1024                          -EQU LCBB2                           +00000463 LB00001L6      VHF NAV-2
LC1025                          -EQU LCBB2                           +00000464 LB00001L6      AUTO-THROTTLE OFF LIGHT
LC1026                          -EQU LCBB2                           +00000465 LB00001L6      GLIDE SLOPE 1
LC1028                          -EQU LCBB2                           +00000466 LB00001L6      GLIDE SLOPE-2
LC1029                          -EQU LCBB2                           +00000467 LB00001L6      ADF-1
LC1030                          -EQU LCBB2                           +00000468 LB00001L6      ADF-2
LC1032                          -EQU LCBB2                           +00000469 LB00001L6      MARKER BEACON
LC1033                          -EQU LCBB2                           +00000470 LB00001L6      ATC-1 PH A
LC1034                          -EQU LCBB2                           +00000471 LB00001L6      ATC-1
LC1035                          -EQU LCBB2                           +00000472 LB00001L6      ATC-2
LC1036                          -EQU LCBB2                           +00000473 LB00001L6      ATC-2
LC1037                          -EQU LCBB2                           +00000474 LB00001L6      DME-1
LC1038                          -EQU LCBB2                           +00000475 LB00001L6      DME-1
LC1039                          -EQU LCBB2                           +00000476 LB00001L6      DME-2
LC1040                          -EQU LCBB2                           +00000477 LB00001L6      DME-2
LC1044                          -EQU LCBB2                           +00000478 LB00001L6      VHF COMM-2
LC1045                          -EQU LCBB2                           +00000479 LB00001L6      FLIGHT RECORDER
LC1046                          -EQU LCBB2                           +00000480 LB00001L6      FLIGHT RECORDER
LC1047                          -EQU LCBB2                           +00000481 LB00001L6      FLIGHT INTERPHONE 1
LC1049                          -EQU LCBB2                           +00000482 LB00001L6      AIR DATA CMPTR-1
LC1050                          -EQU LCBB2                           +00000483 LB00001L6      COCKPIT VOICE RECORDER
LC1051                          -EQU LCBB2                           +00000484 LB00001L6      EMER NAV XFR(115 VAC)
LC1056                          -EQU LCBB2                           +00000485 LB00001L6      SPEED COMMAND CMPTR-1
LC1057                          -EQU LCBB2                           +00000486 LB00001L6      SPEED COMMAND CMPTR-2
LC1060                          -EQU LCBB2                           +00000487 LB00001L6      A/P & ALTERNATE LONG TRIM C
LC1061                          -EQU LCBB2                           +00000488 LB00001L6      A/P & ALTERNATE LONG TRIM B
LC1062                          -EQU LCBB2                           +00000489 LB00001L6      A/P & ALTERNATE LONG TRIM A
LC1064                          -EQU LCBB2                           +00000490 LB00001L6      MACH TRIM OVERRIDE
LC1065                          -EQU LCBB2                           +00000491 LB00001L6      SERVICE INTERPHONE
LC1066                          -EQU LCBB2                           +00000492 LB00001L6      STAB AUG CMPTR-1
LC1067                          -EQU LCBB2                           +00000493 LB00001L6      STAB AUG CMPTR-1
LC1068                          -EQU LCBB2                           +00000494 LB00001L6      A/P-1
LC1069                          -EQU LCBB2                           +00000495 LB00001L6      AUTO-PILOT OFF LIGHT
LC1070                          -EQU LCBB2                           +00000496 LB00001L6      A/P-1
LC1071                          -EQU LCBB2                           +00000497 LB00001L6      AIR DATA CMPTR-1
LC1072                          -EQU LCBB2                           +00000498 LB00001L6      FLIGHT INTERPHONE-2
LC1073                          -EQU LCBB2                           +00000499 LB00001L6      ADF-1
LC1074                          -EQU LCBB2                           +00000500 LB00001L6      ADF-2
LC1075                          -EQU LCBB2                           +00000501 LB00001L6      ALTITUDE ALERT
LC1077                          -EQU LCBB2                           +00000502 LB00001L6      VHF NAV-1 28 VAC
LC1078                          -EQU LCBB2                           +00000503 LB00001L6      VHF NAV-2 28 VAC
LC1079                          -EQU LCBB2                           +00000504 LB00001L6      COMPASS SINGLE POINTER
LC1080                          -EQU LCBB2                           +00000505 LB00001L6      COMPASS DOUBLE POINTER
LC1083                          -EQU LCBB2                           +00000506 LB00001L6      NAV INSTR XFMR-1
LC1084                          -EQU LCBB2                           +00000507 LB00001L6      NAV INST XFMR-2
LC1087                          -EQU LCBB2                           +00000508 LB00001L6      AIR DATA CMPTR-2
LC1088                          -EQU LCBB2                           +00000509 LB00001L6      CAPT HORIZON DISPLAY
LC1089                          -EQU LCBB2                           +00000510 LB00001L6      F/O HORIZON DISPLAY
LC1090                          -EQU LCBB2                           +00000511 LB00001L6      PRIMARY LONG TRIM CONT
LC1092                          -EQU LCBB2                           +00000512 LB00001L6      PRIMARY LONGITUDINAL TRIM
LC1093                          -EQU LCBB2                           +00000513 LB00001L6      PRIMARY LONGITUDINAL TRIM
LC1094                          -EQU LCBB2                           +00000514 LB00001L6      PRIMARY LONGITUDINAL TRIM
LC1095                          -EQU LCBB2                           +00000515 LB00001L6      PRIMARY LONG TRIM BRAKE
LC1099                          -EQU LCBB2                           +00000516 LB00001L6      FLT DIRECTOR-1 CMPTR
LC1100                          -EQU LCBB2                           +00000517 LB00001L6      FLIGHT DIRECTOR-1 CMPTR
LC1001                          -EQU LCBB2                           +00000518 LB00001L6      AUTOTHROTTLE CMPTR AMP
LC1103                          -EQU LCBB2                           +00000519 LB00001L6      FLIGHT DIRECTOR-2 CMPTR
LC1105                          -EQU LCBB2                           +00000520 LB00001L6      RADIO ALT -1
LC1106                          -EQU LCBB2                           +00000521 LB00001L6      RADIO ALTIMETER  2
LC1107                          -EQU LCBB2                           +00000522 LB00001L6      NAV COMPARATOR
LC1108                          -EQU LCBB2                           +00000523 LB00001L6      NAV COMPARATOR
LC1113                          -EQU LCBB2                           +00000524 LB00001L6      SELCAL-1
LC1114                          -EQU LCBB2                           +00000525 LB00001L6      SELCAL-2
LC1115                          -EQU LCBB2                           +00000526 LB00001L6      SELCAL-1
LC1116                          -EQU LCBB2                           +00000527 LB00001L6      SELCAL-2
LC1117                          -EQU LCBB2                           +00000528 LB00001L6      PASS. ADDRESS
LC1118                          -EQU LCBB2                           +00000529 LB00001L6      PASSENGER ADDRESS
LC1120                          -EQU LCBB2                           +00000530 LB00001L6      PASSENGER TAPE
LC1121                          -EQU LCBB2                           +00000531 LB00001L6      PASSENGER TAPE
LC1145                          -EQU LCBB2                           +00000532 LB00001L6      F/O'S RUNWAY DISPLAY
LC1150                          -EQU LCBB2                           +00000533 LB00001L6      CAP'S RUNWAY DISPLAY
LC1152                          -EQU LCBB2                           +00000534 LB00001L6      F/O HDG OUTPUT-C
LC1153                          -EQU LCBB2                           +00000535 LB00001L6      CAPT HDG OUTPUT-C
LC1158                          -EQU LCBB2                           +00000536 LB00001L6      NAV/INST FAILURE MONITOR-2
LC1159                          -EQU LCBB2                           +00000537 LB00001L6      NAV/INST FAILURE MONITOR-1
LC1171                          -EQU LCBB2                           +00000538 LB00001L6      FLT DIRECTOR-1 CONTROL
LC1172                          -EQU LCBB2                           +00000539 LB00001L6      FLT DIRECTOR-2 CONTROL
LC1194                          -EQU LCBB2                           +00000540 LB00001L6      STAB MOTION INDICATOR
LC1234                          -EQU LCBB2                           +00000541 LB00001L6      AUTO-THROTTLE INTERLOCK
LC1243                          -EQU LCBB2                           +00000542 LB00001L6      SPOILER CONTROL
LC1249                          -EQU LCBB2                           +00000543 LB00001L6      NAV INST XFMR-3
LC1250                          -EQU LCBB2                           +00000544 LB00001L6      NAV INST XFMR-3
LC1260                          -EQU LCBB2                           +00000545 LB00001L6      GPWS
LC1261                          -EQU LCBB2                           +00000546 LB00001L6      GPWS
LC1503                          -EQU LCBB2                           +00000547 LB00001L6      STBY HORIZON
LC1501                          -EQU LCBB2                           +00000548 LB00001L6      STBY HORIZON LIGHT CONTROL
LC1319                          -EQU LCBB2                           +00000549 LB00001L6      CAPT  ALTIM
LLGPO                           -EQU LCBB2                           +00000550 LB00001L6      L GEN CONTROL UNIT
LRGPO                           -EQU LCBB2                           +00000551 LB00001L6      R GEN CONTROL UNIT
LAGPO                           -EQU LCBB2                           +00000552 LB00001L6      APU GEN CONTROL UNIT
LC0RW4                          -EQU LCBB2                           +00000553 LB00001L6      AUX FUEL XFER RELAY
LC7042                          -EQU LCBB2                           +00000554 LB00001L6      CAPT ALT 28VAC
LCANNTST                        -EQU LCBB2                           +00000555 LB00001L6      ANN PNL NON-ACTIVE LTS
LC0755                          -EQU LCBB2                           +00000556 LB00001L6      SPOILER DEPLOYED ANNUN
LC9050                          -EQU LCBB2                           +00000557 LB00001L6      W/S CMPTR PWR
LC9051                          -EQU LCBB2                           +00000558 LB00001L6      W/S CMPTR 115V AC PWR
LC9052                          -EQU LCBB2                           +00000559 LB00001L6      W/S ANN 28V DC
LC9053                          -EQU LCBB2                           +00000560 LB00001L6      W/S SWITCHING
LC014006                        -EQU LCBB2                           +00000561 LB00001L6      CABIN OXYGEN ADVISORY
LC019061                        -EQU LCBB2                           +00000562 LB00001L6      ADC/BATT CHARGER FAILURE IND
LC0908                          -EQU LCBB2                           +00000563 LB00001L6      F/O IVSI 115 VAC CB
LC0907                          -EQU LCBB2                           +00000564 LB00001L6      CAPT IVSI 115 VAC C
LC0909                          -EQU LCBB2                           +00000565 LB00001L6      CAPT VERT SPEED REF 28VAC R RADIO BUS
LC014008                        -EQU LCBB2                           +00000566 LB00001L6     PASS OXY ALT CONT
LC014009                        -EQU LCBB2                           +00000567 LB00001L6     PASS OXYGEN CONT
LC014010                        -EQU LCBB2                           +******** LB00001L6     PASS OXYGEN REL ALT (L
LC014034                        -EQU LCBB2                           +00000569 LB00001L6     PASS OXYGEN REL     (K
LC0301                          -EQU LCBB2                           +00000570 LB00001       CAPT INST PANEL LTS  B1-301
LC0302                          -EQU LCBB2                           +00000571 LB00001       CAPT INST PANEL LTS  B1-302
LC0304                          -EQU LCBB2                           +******** LB00001       F/O INST PANEL LTS  B1-304
LC0305                          -EQU LCBB2                           +00000573 LB00001       F/O INST PANEL LTS  B1-305
LC0307                          -EQU LCBB2                           +00000574 LB00001       CTR INST PANEL LTS  B1-307
LC0308                          -EQU LCBB2                           +00000575 LB00001       CTR INST PANEL LTS  B1-308
LC0310                          -EQU LCBB2                           +******** LB00001       PEDESTAL INST PANEL LTS B1-310
LC0311                          -EQU LCBB2                           +00000577 LB00001       PEDESTAL INST PANEL LTS B1-311
LC0313                          -EQU LCBB2                           +00000578 LB00001       OVHD SW PNL FWD LTG B1-313
LC0314                          -EQU LCBB2                           +00000579 LB00001       OVHD SW PNL FWD LTG B1-314
LC0316                          -EQU LCBB2                           +******** LB00001       OVHD SW PNL AFT LTG B1-316
LC0317                          -EQU LCBB2                           +00000581 LB00001       OVHD SW PNL AFT LTG B1-317
LC1012                          -EQU LCBB2                           +00000582 LB00001       CABIN LIGHTS B1-1012
LC0100                          -EQU LCBB2                           +00000583 LB00001       WING POSITION LTS INBOARD
LC0101                          -EQU LCBB2                           +******** LB00001       WING POSITION LTS OUTBOARD
LC1330                          -EQU LCBB2                           +00000585 LB00001       FLIGHT RECORDER 28VAC
LC6003                          -EQU LCBB2                           +00000586 LB00001       IGNITION INVERTER
LC11003                         -EQU LCBB2                           +00000587 LB00001       WINDSHIELD ANTI-ICE OVHT LT
LC11010                         -EQU LCBB2                           +******** LB00001       MAIN GEAR DOWNLOCK PROX IND
LC1011                          -EQU LCBB2                           +00000589 LB00001       RAM AIR TEMP
LC0998                          -EQU LCBB2                           +00000590 LB00001       CCU A B1-998
LC0999                          -EQU LCBB2                           +00000591 LB00001       CCU B B1-999
LC0500                          -EQU LCBB2                           +******** LB00001       STANDBY COMPASS LT ENABLE B1-500
LC1098                          -EQU LCBB2                           +00000593 LB00001       CAPT COURSE HDG DISPLAY B10-98
LC1096                          -EQU LCBB2                           +00000594 LB00001       FO COURSE & HDG DISPLAY B10-96
LC1000                          -EQU LCBB2                           +00000595 LB00001       LOWER CARGO FIRE B1-1000
LC109002                        -EQU LCBB2                           +00000596 LB00001       TCAS POWER AC
LCBCB                            EQU X_LBW1                          +00000200 LB00200       B200 CB ARRAY
LLGPO                            EQU X_LBB1                          +00000320 LB00001       L GEN CONTROL UNIT
LRGPO                            EQU X_LBB1                          +00000321 LB00001       R GEN CONTROL UNIT
LAGPO                            EQU X_LBB1                          +00000322 LB00001       APU GEN CONTROL UNIT
LCB228CB5                        EQU LCBCB                           +******** LB00001       LEFT FIREWALL FUEL SHUTOFF VALVE CB PWR
XCBCB                            EQU DP                              +00037360 LB00200       B200 CB BUFFER 

XCB228CB5                        EQU XCBCB                           +******** LB00001       LT FW FUEL SHUTOFF VALVE CB 24-51-00-4
XCB228CB6                        EQU XCBCB                           +00000001 LB00001       RT FW FUEL SHUTOFF VALVE CB 24-51-00-4
XCB228CB13                       EQU XCBCB                           +00000002 LB00001       RT ENGINE FIRE EXT CB 24-51-00-4
XCB228CB12                       EQU XCBCB                           +00000003 LB00001       LT ENGINE FIRE EXT CB 24-51-00-4
XCB228CB11                       EQU XCBCB                           +00000004 LB00001       BATTERY RELAY CB 24-51-00-4
XCB228CB10                       EQU XCBCB                           +00000005 LB00001       ENTRY LIGHTS CB 24-51-00-4
XCB228CB9                        EQU XCBCB                           +00000006 LB00001       NAV MEMORY CB 24-51-00-4
XCB145CB153                      EQU XCBCB                           +00000007 LB00001       PILOT WINDSHIELD ANTI-ICE CB 24-52-00-3
XCB145CB224                      EQU XCBCB                           +00000008 LB00001       ELEC HEAT FWD CB 24-52-00-3
XCB145CB182                      EQU XCBCB                           +00000009 LB00001       AVIONICS BUS NO.3 FEEDER CB 24-52-00-3
XCB145CB181                      EQU XCBCB                           +00000010 LB00001       COPILOT WINDSHIELD ANTI-ICE 24-52-00-3
XCB145CB225                      EQU XCBCB                           +00000011 LB00001       ELEC HEAT AFT CB 24-52-00-3
XCB145CB195                      EQU XCBCB                           +00000012 LB00001       VENT BLOWER POWER CB 24-52-00-3
XCB145CB150                      EQU XCBCB                           +00000013 LB00001       AFT EVAPORATOR BLWR PWR CB 24-52-00-3
XCB145CB207                      EQU XCBCB                           +00000014 LB00001       DC TEST JACK CB 24-52-00-3
XCB145CB206                      EQU XCBCB                           +00000015 LB00001       AIR CONDITIONER CLUTCH CB 24-52-00-3
XCB145CB157                      EQU XCBCB                           +00000016 LB00001       ENGINE INSTRUMENTS CB 24-52-00-3
XCB223CB177                      EQU XCBCB                           +00000017 LB00001       LT LANDING LIGHT SW CB 24-53-01-5
XCB223CB111                      EQU XCBCB                           +00000018 LB00001       LT PITOT HEAT SW CB 24-53-01-5
XCB223CB167                      EQU XCBCB                           +00000019 LB00001       PROP AUTO HEAT SW CB 24-53-01-5
XCB223CB162                      EQU XCBCB                           +00000020 LB00001       TAIL FLOOD LIGHTS SW CB 24-53-01-5
XCB223CB135                      EQU XCBCB                           +00000021 LB00001       STROBE LIGHTS SWITCH CB 24-53-01-5
XCB223CB123                      EQU XCBCB                           +00000022 LB00001       BEACON LIGHTS SW CB 24-53-01-5
XCB146CB169                      EQU XCBCB                           +00000023 LB00001       AVIONICS MASTER CONT CB 24-53-01-5
XCB146CB159                      EQU XCBCB                           +00000024 LB00001       LT GEN OVERHEAT CB 24-53-01-5
XCB146CB190                      EQU XCBCB                           +00000025 LB00001       LT ENGINE INSTRUMENTS CB 24-53-01-5
XCB146CB139                      EQU XCBCB                           +00000026 LB00001       LT OIL PRESSURE WARN CB 24-53-01-5
XCB146CB115                      EQU XCBCB                           +00000027 LB00001       LT ENG FUEL CONT HEAT CB 24-53-01-5
XCB146CB154                      EQU XCBCB                           +00000028 LB00001       MAIN ENG ANTI-ICE LEFT CB 24-53-01-5
XCB146CB254                      EQU XCBCB                           +00000029 LB00001       STDBY ENG ANTI-ICE LEFT CB 24-53-01-5
XCB146CB143                      EQU XCBCB                           +00000030 LB00001       FIRE DETECTOR CB 24-53-01-5
XCB146CB163                      EQU XCBCB                           +00000031 LB00001       LEFT CHIP DETECTOR CB 24-53-01-5
XCB146CB161                      EQU XCBCB                           +00000032 LB00001       PROP BALANCE/PROP SYNC CB 24-53-01-5
XCB146CB164                      EQU XCBCB                           +00000033 LB00001       BRAKE DEICE CB 24-53-01-5
XCB146CB117                      EQU XCBCB                           +00000034 LB00001       PNEUMATIC SURFACE DEICE CB 24-53-01-5
XCB146CB113                      EQU XCBCB                           +00000035 LB00001       LEFT FUEL VENT HEAT CB 24-53-01-5
XCB146CB144                      EQU XCBCB                           +00000036 LB00001       ANNUNCIATOR POWER CB 24-53-01-5
XCB146CB108                      EQU XCBCB                           +00000037 LB00001       LANDING GEAR WARN HORN CB 24-53-01-5
XCB146CB146                      EQU XCBCB                           +00000038 LB00001       LEFT BLEED AIR WARNING CB 24-53-01-5
XCB146CB145                      EQU XCBCB                           +00000039 LB00001       STALL WARN CB 24-53-01-5
XCB146CB131                      EQU XCBCB                           +00000040 LB00001       OVRHD/PILOT FLT INST LIGHT 24-53-01-5
XCB146CB127                      EQU XCBCB                           +00000041 LB00001       AVIONIC/ENG INST LIGHTS CB 24-53-01-5
XCB146CB129                      EQU XCBCB                           +00000042 LB00001       CABIN LIGHTS & ORDINANCE CB 24-53-01-5
XCB146CB174                      EQU XCBCB                           +00000043 LB00001       NO.1 DUAL BUS RT FEEDER CB 24-53-01-5
XCB146CB172                      EQU XCBCB                           +00000044 LB00001       NO.1 DUAL BUS LT FEEDER CB 24-53-01-5
XCB146CB196                      EQU XCBCB                           +00000045 LB00001       LEFT GEN CONTROL CB 24-53-01-5
XCB146CB239                      EQU XCBCB                           +00000046 LB00001       OUTSIDE AIR TEMP CB 24-53-01-5
XCB146CB166                      EQU XCBCB                           +00000047 LB00001       TRIM TAB CB 24-53-01-5
XCB146CB104                      EQU XCBCB                           +00000048 LB00001       PILOT TURN & SLIP IND CB 24-53-01-5
XCB146CB141                      EQU XCBCB                           +00000049 LB00001       YAW DAMPER CB 24-53-01-5
XCB146CB118                      EQU XCBCB                           +00000050 LB00001       LT BLEED AIR CONTROL CB 24-53-01-5
XCB146CB120                      EQU XCBCB                           +00000051 LB00001       CABIN PRESS CONTROL CB 24-53-01-5
XCB146CB130                      EQU XCBCB                           +******** LB00001       AUTO OXYGEN CONTROL CB 24-53-01-5
XCB223CB185                      EQU XCBCB                           +******** LB00001       STALL WARNING HEAT SW CB 24-53-02-5
XCB223CB112                      EQU XCBCB                           +******** LB00001       RT PITOT HEAT SW CB 24-53-02-5
XCB223CB107                      EQU XCBCB                           +******** LB00001       LANDING GEAR CONTROL CB 24-53-02-5
XCB223CB134                      EQU XCBCB                           +00000056 LB00001       RECOGNITION LIGHTS SW CB 24-53-02-5
XCB223CB121                      EQU XCBCB                           +00000057 LB00001       NAV LIGHTS SW CB 24-53-02-5
XCB223CB126                      EQU XCBCB                           +00000058 LB00001       ICE LIGHTS SW CB 24-53-02-5
XCB223CB122                      EQU XCBCB                           +00000059 LB00001       TAXI LIGHT SW CB 24-53-02-5
XCB223CB178                      EQU XCBCB                           +00000060 LB00001       RT LANDING LIGHT SW CB 24-53-02-5
XCB146CB160                      EQU XCBCB                           +00000061 LB00001       RT GEN OVERHEAT CB 24-53-02-5
XCB146CB211                      EQU XCBCB                           +00000062 LB00001       RT ENGINE INSTRUMENTS CB 24-53-02-5
XCB146CB140                      EQU XCBCB                           +00000063 LB00001       RT OIL PRESSURE WARN CB 24-53-02-5
XCB146CB116                      EQU XCBCB                           +00000064 LB00001       RT ENG FUEL CONT HEAT CB 24-53-02-5
XCB146CB155                      EQU XCBCB                           +00000065 LB00001       MAIN ENG ANTI-ICE RIGHT CB 24-53-02-5
XCB146CB255                      EQU XCBCB                           +00000066 LB00001       STDBY ENG ANTI-ICE RT CB 24-53-02-5
XCB146CB156                      EQU XCBCB                           +00000067 LB00001       RT CHIP DETECTOR CB 24-53-02-5
XCB146CB176                      EQU XCBCB                           +00000068 LB00001       AUTOFEATHER CB 24-53-02-5
XCB146CB136                      EQU XCBCB                           +00000069 LB00001       WINDSHIELD WIPER CB 24-53-02-5
XCB146CB114                      EQU XCBCB                           +00000070 LB00001       RT FUEL VENT HEAT CB 24-53-02-5
XCB146CB138                      EQU XCBCB                           +00000071 LB00001       ANNUNCIATOR INDICATOR CB 24-53-02-5
XCB146CB106                      EQU XCBCB                           +00000072 LB00001       LANDING GEAR POSITION IND CB 24-53-02-5
XCB146CB147                      EQU XCBCB                           +00000073 LB00001       RT BLEED AIR WARNING CB 24-53-02-5
XCB146CB133                      EQU XCBCB                           +00000074 LB00001       COPILOT FLT INST LIGHTS CB 24-53-02-5
XCB146CB132                      EQU XCBCB                           +00000075 LB00001       OVRHD SUBPNL/CONSOLE LIGHTS CB 24-53-02-5
XCB146CB128                      EQU XCBCB                           +00000076 LB00001       INST INDIRECT LIGHTS CB 24-53-02-5
XCB146CB231                      EQU XCBCB                           +00000077 LB00001       CABIN READING LIGHTS CB 24-53-02-5
XCB146CB175                      EQU XCBCB                           +00000078 LB00001       NO.2 DUAL BUS RT FEEDER CB 24-53-02-5
XCB146CB173                      EQU XCBCB                           +00000079 LB00001       NO.2 DUAL BUS LT FEEDER CB 24-53-02-5
XCB146CB109                      EQU XCBCB                           +00000080 LB00001       CIGARETTE LIGHTER CB 24-53-02-5
XCB146CB232                      EQU XCBCB                           +00000081 LB00001       FURNISHINGS MASTER CONT CB 24-53-02-5
XCB146CB197                      EQU XCBCB                           +00000082 LB00001       RT GEN CONTROL CB 24-53-02-5
XCB146CB142                      EQU XCBCB                           +00000083 LB00001       RUDDER BOOST CONTROL CB 24-53-02-5
XCB146CB105                      EQU XCBCB                           +00000084 LB00001       COPILOT TURN & SLIP IND CB 24-53-02-5
XCB146CB119                      EQU XCBCB                           +00000085 LB00001       RT BLEED AIR CONTROL CB 24-53-02-5
XCB146CB184                      EQU XCBCB                           +00000086 LB00001       CABIN TEMP CONTROL CB 24-53-02-5
XCB146CB151                      EQU XCBCB                           +00000087 LB00001       CABIN PRESSURE LOSS CB 24-53-02-5
XCB124CB12                       EQU XCBCB                           +00000088 LB00001       LEFT FIREWALL VALVE CB 24-53-03-3
XCB124CB11                       EQU XCBCB                           +00000089 LB00001       LEFT STANDBY PUMP CB 24-53-03-3
XCB124CB10                       EQU XCBCB                           +00000090 LB00001       LT AUX FUEL QTY WRN & TRANS 24-53-03-3
XCB124CB9                        EQU XCBCB                           +00000091 LB00001       LEFT FUEL QUANTITY CB 24-53-03-3
XCB124CB8                        EQU XCBCB                           +00000092 LB00001       LEFT FUEL PRESS WARNING CB 24-53-03-3
XCB124CB22                       EQU XCBCB                           +00000093 LB00001       NO.3 DUAL BUS LT FEEDER CB 24-53-03-3
XCB124CB28                       EQU XCBCB                           +00000094 LB00001       NO.3 DUAL BUS RT FEEDER CB 24-53-03-3
XCB124CB27                       EQU XCBCB                           +00000095 LB00001       LEFT MANUAL PROP DEICE CB 24-53-03-3
XCB124CB26                       EQU XCBCB                           +******** LB00001       FLAP MOTOR CB 24-53-03-3
XCB124CB25                       EQU XCBCB                           +00000097 LB00001       FLAP CONTROL & INDICATOR CB 24-53-03-3
XCB124CB24                       EQU XCBCB                           +******** LB00001       LEFT IGNITOR POWER CB 24-53-03-3
XCB124CB23                       EQU XCBCB                           +00000099 LB00001       LEFT START CONTROL CB 24-53-03-3
XCB124CB37                       EQU XCBCB                           +******** LB00001       NO.4 DUAL BUS LT FEEDER CB 24-53-04-3
XCB124CB43                       EQU XCBCB                           +00000101 LB00001       NO.4 DUAL BUS RT FEEDER CB 24-53-04-3
XCB124CB42                       EQU XCBCB                           +******** LB00001       RIGHT MANUAL PROP DEICE CB 24-53-04-3
XCB124CB41                       EQU XCBCB                           +00000103 LB00001       MANUAL PROP DEICE CONT CB 24-53-04-3
XCB124CB40                       EQU XCBCB                           +00000104 LB00001       PROP GOV CB 24-53-04-3
XCB124CB39                       EQU XCBCB                           +00000105 LB00001       RIGHT IGNITOR PWR CB 24-53-04-3
XCB124CB38                       EQU XCBCB                           +00000106 LB00001       RIGHT START CONTROL CB 24-53-04-3
XCB124CB7                        EQU XCBCB                           +00000107 LB00001       FUEL CROSSFEED CB 24-53-04-3
XCB124CB6                        EQU XCBCB                           +00000108 LB00001       RIGHT FUEL PRESS WARNING CB 24-53-04-3
XCB124CB5                        EQU XCBCB                           +00000109 LB00001       RIGHT FUEL QUANTITY CB 24-53-04-3
XCB124CB4                        EQU XCBCB                           +00000110 LB00001       RT AUX FUEL QTY WRN & TRANS 24-53-04-3
XCB124CB3                        EQU XCBCB                           +00000111 LB00001       RIGHT STANDBY PUMP CB 24-53-04-3
XCB124CB2                        EQU XCBCB                           +00000112 LB00001       RIGHT FIREWALL VALVE CB 24-53-04-3
XCB145CB182                      EQU XCBCB                           +00000113 LB00001       AVIONICS BUS NO.3 FEEDER CB 24-56-00
XCB146CB169                      EQU XCBCB                           +00000114 LB00001       AVIONICS MASTER CONTROL CB 24-56-00
XCB146CB170                      EQU XCBCB                           +00000115 LB00001       AVIONICS BUS NO.1 CB 24-56-00
XCB146CB171                      EQU XCBCB                           +00000116 LB00001       AVIONICS BUS NO.2 CB 24-56-00
XCB146CB183                      EQU XCBCB                           +00000117 LB00001       AVIONICS BUS NO.3 CB 24-56-00
LBATVDROP                        EQU LBBUSA                          +00000083 LB00001       BATTERY VOLTAGE DROP DUE TO ENG START
LC_STDBYADICB                    EQU LBBUSA                          +00000046 LB00001       STBY HORZ IND BUS PWR                                
LC_TCAS_CB                       EQU LBBUSA                          +******** LB00001       TCAS ADVSY BUS PWR                                   
LC_RMI2_26VCB                    EQU LBBUSA                          +00000057 LB00001       RMI NO.2 26VAC BUS PWR                              
LC_NAV1_26VCB                    EQU LBBUSA                          +00000056 LB00001       NAV NO.1 26VAC BUS PWR                              
LC_RMI1_26VCB                    EQU LBBUSA                          +00000060 LB00001       RMI NO.1 26VAC BUS PWR                              
LC_NAV2_26VCB                    EQU LBBUSA                          +00000061 LB00001       NAV NO.2 26VAC BUS PWR                              
LC_COMM1_DC_CB                   EQU LBBUSA                          +00000062 LB00001       COMM NO.1 DC BUS PWR                                
LC_NAV1_DC_CB                    EQU LBBUSA                          +00000063 LB00001       NAV NO.1 DC BUS PWR                                 
LC_STALL_WARN_CB                 EQU LCTRIPLE                        +00000022 LB00001       STALL WARNING SYSTEM PWR                            
LC_FLAP_CONT_IND_CB              EQU LCLGENBA                        +00000002 LB00001       FLAP CONTROL & INDICATOR CB PWR                     
LC_COMPASS1_CB                   EQU LBBUSA                          +00000064 LB00001       COMPASS #1 CB PWR ENABLED                           
LC_COMM2_DC_CB                   EQU LBBUSA                          +00000065 LB00001       COMM NO.2 DC BUS PWR                                
LC_NAV2_DC_CB                    EQU LBBUSA                          +00000066 LB00001       NAV NO.2 DC BUS PWR                                 
LC_GPS_CB                        EQU LBBUSA                          +00000082 LB00001       GPS BUS PWR                                         
LC_PILOT_PHONE_CB                EQU LCLGENBA                        +00000003 LB00001       PILOT PHONE CB PWR                                  
LC_COMPASS2_CB                   EQU LCRGENBA                        +00000005 LB00001       COMPASS #2 CB PWR ENABLED                           
LC_RAD_ALT_CB                    EQU LBBUSA                          +00000067 LB00001       RADIO ALT DC BUS PWR                                
LC_COPILOT_PHONE_CB              EQU LCRGENBA                        +00000003 LB00001       PA & COPILOT PHONE CB PWR                           
LC_PILOT_ENC_ALT_CB              EQU LCTRIPLE                        +00000029 LB00001       PILOT ENCODING ALTIMETER PWR                        
LC_RMI2_DC_CB                    EQU LBBUSA                          +00000072 LB00001       RMI NO.2 DC BUS PWR                                 
LC_ADF1_CB                       EQU LBBUSA                          +00000073 LB00001       ADF NO.1 BUS PWR                                    
LC_DME1_CB                       EQU LBBUSA                          +00000074 LB00001       DME NO.1 BUS PWR                                    
LC_XPNDER1_CB                    EQU LBBUSA                          +00000075 LB00001       XPNDER NO.1 BUS PWR                                 
LC_RMI1_DC_CB                    EQU LBBUSA                          +00000080 LB00001       RMI NO.1 DC BUS PWR                                 
LC_DME2_CB                       EQU LBBUSA                          +00000081 LB00001       DME NO.2 BUS PWR                                    
LC_XPNDER2_CB                    EQU LBBUSA                          +00000043 LB00001       XPNDER NO.2 BUS PWR                                 
LC_FLAP_MOTOR_CB                 EQU LCLGENBA                        +00000013 LB00001       FLAP MOTOR CB PWR                                   

LC_OVERSPEED_SNSR_CB             EQU LCTRIPLE                        +00000046 LB00001       OVERSPEED SENSOR PWR                                 
LC_VCRCDR                        EQU LBBUSA                          +******** LB00001       CKPT VOICE RCDR BUS PWR (SHARED)
LC_ATT_GYRO1_CB                  EQU LBBUSA                          +00000051 LB00001       ATT NO.1 BUS PWR                                     
LC_AAT_GYRO2_CB                  EQU LBBUSA                          +******** LB00001       ATT NO.2 BUS PWR                                     
XLB224S205HI                     EQU FLY_MISC_BASE                   +00000476 LB00001       VENT BLOWER CNTL SW HIGH (S205)
XLB224S205LO                     EQU FLY_MISC_BASE                   +00000477 LB00001       VENT BLOWER CNTL SW LO (S205)
XLB224S205AUTO                   EQU FLY_MISC_BASE                   +00000478 LB00001       VENT BLOWER CNTL SW AUTO (S205)
LB224K141                        EQU FLY_MISC_BASE                   +00000479 LB00001       FWD VENT BLOWER LOW SPEED RELAY
LB224K143                        EQU FLY_MISC_BASE                   +00000480 LB00001       AFT VENT BLOWER LOW SPEED RELAY
LB224K203                        EQU FLY_MISC_BASE                   +00000481 LB00001       FWD VENT BLOWER HIGH SPEED RELAY
LB224K204                        EQU FLY_MISC_BASE                   +00000482 LB00001       AFT VENT BLOWER HIGH SPEED RELAY
LBK147                           EQU FLY_MISC_BASE                   +00000483 LB00001       CONDENSOR BLOWER MOTOR RELAY
XLB224S233OFF                    EQU FLY_MISC_BASE                   +00000484 LB00001       AFT BLOWER SWITCH OFF POSITION
XLB224S233ON                     EQU FLY_MISC_BASE                   +00000485 LB00001       AFT BLOWER SWITCH ON POSITION
LB246K1                          EQU FLY_MISC_BASE                   +00000486 LB00001       AFT BLOWER POWER RELAY
NGDECEL                          EQU tpengtables                     +00000952 IW00002       ng unfired decel table          
LBBUS                            EQU DP                              +00037560 LB00100       B200 AVIONICS BUS VARIABLES
LBIGLD                           EQU LBBUS                           +******** LB00001       ISOLATION BUS LOAD
LBDF1GLD                         EQU LBBUS                           +00000001 LB00001       DUEL FED 1 BUS LOAD
LBDF2GLD                         EQU LBBUS                           +00000002 LB00001       DUEL FED 2 BUS LOAD
LBDF3GLD                         EQU LBBUS                           +00000003 LB00001       DUEL FED 3 BUS LOAD
LBDF4GLD                         EQU LBBUS                           +00000004 LB00001       DUEL FED 4 BUS LOAD
LBISO                            EQU LBBUS                           +00000005 LB00001       ISOLATION BUS ENERGIZED
LBDF1                            EQU LBBUS                           +00000006 LB00001       DUEL FED 1 BUS ENERGIZED
LBDF2                            EQU LBBUS                           +00000007 LB00001       DUEL FED 2 BUS ENERGIZED
LBDF3                            EQU LBBUS                           +00000008 LB00001       DUEL FED 3 BUS ENERGIZED
LBDF4                            EQU LBBUS                           +00000009 LB00001       DUEL FED 4 BUS ENERGIZED
LISOVOLT                         EQU LBBUS                           +00000010 LB00001       ISOLATION BUS VOLTAGE
LBDF1VOLT                        EQU LBBUS                           +00000011 LB00001       DUEL FED 1 BUS VOLTAGE
LBDF2VOLT                        EQU LBBUS                           +00000012 LB00001       DUEL FED 2 BUS VOLTAGE
LBDF3VOLT                        EQU LBBUS                           +00000013 LB00001       DUEL FED 3 BUS VOLTAGE
LBDF4VOLT                        EQU LBBUS                           +00000014 LB00001       DUEL FED 4 BUS VOLTAGE
XLB221S177                       EQU LBBUS                           +00000015 LB00001       MAIN BATTERY SWITCH 24-51-00-4
LBK135                           EQU LBBUS                           +00000016 LB00001       BATTERY RELAY 24-51-00-4
LB145K114                        EQU LBBUS                           +00000017 LB00001       LEFT GEN RELAY CONTACT 24-31-01-4
LB145K115                        EQU LBBUS                           +00000018 LB00001       RIGHT GEN RELAY CONTACT 24-31-02-4
LCB145CB243                      EQU LCBCB                           +00000118 LB00001       NO. 1 INVERTER CONTROL CB 24-21-00-8
LCB145CB244                      EQU LCBCB                           +00000119 LB00001       NO. 2 INVERTER CONTROL CB 24-21-00-8
LCB246CB4                        EQU LCBCB                           +00000120 LB00001       LANDING GEAR MOTOR CB 32-31-00-5
LCB146CBEFISBAT                  EQU LCBCB                           +00000121 LB00001       EFIS STDBY BAT CB
LCB146CBEFISDSP                  EQU LCBCB                           +00000122 LB00001       EFIS DPS CB
LCB146CBEFISDPU                  EQU LCBCB                           +00000123 LB00001       EFIS DPU CB
LCB146CBEFISEHSI                 EQU LCBCB                           +00000124 LB00001       EFIS EHSI CB
LCB146CBEFISEADI                 EQU LCBCB                           +00000125 LB00001       EFIS EADI CB
LCB146CBALTALRT                  EQU LCBCB                           +00000126 LB00001       ALT ALERT CB
LCB146CBARLWRN                   EQU LCBCB                           +00000127 LB00001       AURAL WARNING CB
LCB146CBDPU                      EQU LCBCB                           +00000128 LB00001       DISPLAY PROCESSOR CB
LCB146CBPALTAIRD                 EQU LCBCB                           +00000129 LB00001       PILOT ALTM AIR DATA CB
LCB146CBPAUDIO                   EQU LCBCB                           +00000130 LB00001       PILOT AUDIO CB
LCB146CBCAUDIO                   EQU LCBCB                           +00000131 LB00001       COPILOT AUDIO CB
LCB146CBCABAUD                   EQU LCBCB                           +00000132 LB00001       CABIN AUDIO CB
LCB146CBCVR                      EQU LCBCB                           +00000133 LB00001       COCKPIT VOICE RECORDER CB
LCB146CBDSP                      EQU LCBCB                           +00000134 LB00001       DSP CB
LCB146CBEADI                     EQU LCBCB                           +00000135 LB00001       EADI CB
LCB146CBEHSI                     EQU LCBCB                           +00000136 LB00001       EHSI CB
LCB146CBAVANN                    EQU LCBCB                           +00000137 LB00001       AVIONICS ANNUNCIATORS CB
LCB146CBNOSMK                    EQU LCBCB                           +00000138 LB00001       NO SMOKING & FSB SIGN CB
LCB146CBCENCDALT                 EQU LCBCB                           +00000139 LB00001       COPILOT ENCODING ALTIMETER CB
LCB146CBCOMM1                    EQU LCBCB                           +00000140 LB00001       COMM 1 CB
LCB146CBCOMM2                    EQU LCBCB                           +00000141 LB00001       COMM 2 CB
LCB146CBNAV1                     EQU LCBCB                           +00000142 LB00001       NAV 1 CB
LCB146CBNAV2                     EQU LCBCB                           +00000143 LB00001       NAV 2 CB
LCB146CBDME1                     EQU LCBCB                           +00000144 LB00001       DME 1 CB
LCB146CBDME2                     EQU LCBCB                           +00000145 LB00001       DME 2 CB
LCB146CBRMI1                     EQU LCBCB                           +00000146 LB00001       RMI 1 CB
LCB146CBRMI2                     EQU LCBCB                           +00000147 LB00001       RMI 2 CB
LCB146CBCMPS1                    EQU LCBCB                           +00000148 LB00001       COMPASS 1 CB
LCB146CBCMPS2                    EQU LCBCB                           +00000149 LB00001       COMPASS 2 CB
LCB146CBXPNDR1                   EQU LCBCB                           +00000150 LB00001       TRANSPONDER 1 CB
LCB146CBXPNDR2                   EQU LCBCB                           +00000151 LB00001       TRANSPONDER 2 CB
LCB146CBADF                      EQU LCBCB                           +00000152 LB00001       ADF CB
LCB146CBAPSERV                   EQU LCBCB                           +00000153 LB00001       A/P SERVO CB
LCB146CBFCSPWR                   EQU LCBCB                           +00000154 LB00001       FCS POWER CB
LCB146CBRDOALT                   EQU LCBCB                           +00000155 LB00001       RADIO ALTIMITER CB
LCB146CBFANNRML                  EQU LCBCB                           +00000156 LB00001       EFIS FAN NORMAL CB
LCB146CBFANSTBY                  EQU LCBCB                           +00000157 LB00001       EFIS FAN STANDBY CB
LCB146CBTCAS                     EQU LCBCB                           +00000158 LB00001       TCAS CB
LCB146CBGPS                      EQU LCBCB                           +00000159 LB00001       GPS CB
LCB146CBTAWS                     EQU LCBCB                           +00000160 LB00001       TAWS CB
XCB145CB243                      EQU XCBCB                           +00000118 LB00001       NO. 1 INVERTER CONTROL CB 24-21-00-8
XCB145CB244                      EQU XCBCB                           +00000119 LB00001       NO. 2 INVERTER CONTROL CB 24-21-00-8
XCB246CB4                        EQU XCBCB                           +00000120 LB00001       LANDING GEAR MOTOR CB 32-31-00-5
XCB146CBEFISBAT                  EQU XCBCB                           +00000121 LB00001       EFIS STDBY BAT CB
XCB146CBEFISDSP                  EQU XCBCB                           +00000122 LB00001       EFIS DPS CB
XCB146CBEFISDPU                  EQU XCBCB                           +00000123 LB00001       EFIS DPU CB
XCB146CBEFISEHSI                 EQU XCBCB                           +00000124 LB00001       EFIS EHSI CB
XCB146CBEFISEADI                 EQU XCBCB                           +00000125 LB00001       EFIS EADI CB
XCB146CBALTALRT                  EQU XCBCB                           +00000126 LB00001       ALT ALERT CB
XCB146CBARLWRN                   EQU XCBCB                           +00000127 LB00001       AURAL WARNING CB
XCB146CBDPU                      EQU XCBCB                           +00000128 LB00001       DISPLAY PROCESSOR CB
XCB146CBPALTAIRD                 EQU XCBCB                           +00000129 LB00001       PILOT ALTM AIR DATA CB
XCB146CBPAUDIO                   EQU XCBCB                           +00000130 LB00001       PILOT AUDIO CB
XCB146CBCAUDIO                   EQU XCBCB                           +00000131 LB00001       COPILOT AUDIO CB
XCB146CBCABAUD                   EQU XCBCB                           +00000132 LB00001       CABIN AUDIO CB
XCB146CBCVR                      EQU XCBCB                           +00000133 LB00001       COCKPIT VOICE RECORDER CB
XCB146CBDSP                      EQU XCBCB                           +00000134 LB00001       DSP CB
XCB146CBEADI                     EQU XCBCB                           +00000135 LB00001       EADI CB
XCB146CBEHSI                     EQU XCBCB                           +00000136 LB00001       EHSI CB
XCB146CBAVANN                    EQU XCBCB                           +00000137 LB00001       AVIONICS ANNUNCIATORS CB
XCB146CBNOSMK                    EQU XCBCB                           +00000138 LB00001       NO SMOKING & FSB SIGN CB
XCB146CBCENCDALT                 EQU XCBCB                           +00000139 LB00001       COPILOT ENCODING ALTIMETER CB
XCB146CBCOMM1                    EQU XCBCB                           +00000140 LB00001       COMM 1 CB
XCB146CBCOMM2                    EQU XCBCB                           +00000141 LB00001       COMM 2 CB
XCB146CBNAV1                     EQU XCBCB                           +00000142 LB00001       NAV 1 CB
XCB146CBNAV2                     EQU XCBCB                           +00000143 LB00001       NAV 2 CB
XCB146CBDME1                     EQU XCBCB                           +00000144 LB00001       DME 1 CB
XCB146CBDME2                     EQU XCBCB                           +00000145 LB00001       DME 2 CB
XCB146CBRMI1                     EQU XCBCB                           +00000146 LB00001       RMI 1 CB
XCB146CBRMI2                     EQU XCBCB                           +00000147 LB00001       RMI 2 CB
XCB146CBCMPS1                    EQU XCBCB                           +00000148 LB00001       COMPASS 1 CB
XCB146CBCMPS2                    EQU XCBCB                           +00000149 LB00001       COMPASS 2 CB
XCB146CBXPNDR1                   EQU XCBCB                           +00000150 LB00001       TRANSPONDER 1 CB
XCB146CBXPNDR2                   EQU XCBCB                           +00000151 LB00001       TRANSPONDER 2 CB
XCB146CBADF                      EQU XCBCB                           +00000152 LB00001       ADF CB
XCB146CBAPSERV                   EQU XCBCB                           +00000153 LB00001       A/P SERVO CB
XCB146CBFCSPWR                   EQU XCBCB                           +00000154 LB00001       FCS POWER CB
XCB146CBRDOALT                   EQU XCBCB                           +00000155 LB00001       RADIO ALTIMITER CB
XCB146CBFANNRML                  EQU XCBCB                           +00000156 LB00001       EFIS FAN NORMAL CB
XCB146CBFANSTBY                  EQU XCBCB                           +00000157 LB00001       EFIS FAN STANDBY CB
XCB146CBTCAS                     EQU XCBCB                           +00000158 LB00001       TCAS CB
XCB146CBGPS                      EQU XCBCB                           +00000159 LB00001       GPS CB
XCB146CBTAWS                     EQU XCBCB                           +00000160 LB00001       TAWS CB
LBIGLD                          -EQU LBBUS                           +******** LB00001       ISOLATION BUS LOAD
LBDF1GLD                        -EQU LBBUS                           +00000001 LB00001       DUEL FED 1 BUS LOAD
LBDF2GLD                        -EQU LBBUS                           +00000002 LB00001       DUEL FED 2 BUS LOAD
LBDF3GLD                        -EQU LBBUS                           +00000003 LB00001       DUEL FED 3 BUS LOAD
LBDF4GLD                        -EQU LBBUS                           +00000004 LB00001       DUEL FED 4 BUS LOAD
LBISO                           -EQU LBBUS                           +00000005 LB00001       ISOLATION BUS ENERGIZED
LBDF1                           -EQU LBBUS                           +00000006 LB00001       DUEL FED 1 BUS ENERGIZED
LBDF2                           -EQU LBBUS                           +00000007 LB00001       DUEL FED 2 BUS ENERGIZED
LBDF3                           -EQU LBBUS                           +00000008 LB00001       DUEL FED 3 BUS ENERGIZED
LBDF4                           -EQU LBBUS                           +00000009 LB00001       DUEL FED 4 BUS ENERGIZED
LISOVOLT                        -EQU LBBUS                           +00000010 LB00001       ISOLATION BUS VOLTAGE
LBDF1VOLT                       -EQU LBBUS                           +00000011 LB00001       DUEL FED 1 BUS VOLTAGE
LBDF2VOLT                       -EQU LBBUS                           +00000012 LB00001       DUEL FED 2 BUS VOLTAGE
LBDF3VOLT                       -EQU LBBUS                           +00000013 LB00001       DUEL FED 3 BUS VOLTAGE
LBDF4VOLT                       -EQU LBBUS                           +00000014 LB00001       DUEL FED 4 BUS VOLTAGE
XLB221S177                      -EQU LBBUS                           +00000015 LB00001       MAIN BATTERY SWITCH 24-51-00-4
LBK135                          -EQU LBBUS                           +00000016 LB00001       BATTERY RELAY 24-51-00-4
LB145K114                       -EQU LBBUS                           +00000017 LB00001       LEFT GEN RELAY CONTACT 24-31-01-4
LB145K115                       -EQU LBBUS                           +00000018 LB00001       RIGHT GEN RELAY CONTACT 24-31-02-4
LBBUS                           +EQU DP                              +00037560 LB00200       B200 AVIONICS BUS VARIABLES

LBIGLD                           EQU LBBUS                           +******** EW00001       ISOLATION BUS LOAD
LBDF1GLD                         EQU LBBUS                           +00000004 EW00001       DUEL FED 1 BUS LOAD
LBDF2GLD                         EQU LBBUS                           +00000008 EW00001       DUEL FED 2 BUS LOAD
LBDF3GLD                         EQU LBBUS                           +00000012 EW00001       DUEL FED 3 BUS LOAD
LBDF4GLD                         EQU LBBUS                           +00000016 EW00001       DUEL FED 4 BUS LOAD
LISOVOLT                         EQU LBBUS                           +00000020 EW00001       ISOLATION BUS VOLTAGE
LBDF1VOLT                        EQU LBBUS                           +00000024 EW00001       DUEL FED 1 BUS VOLTAGE
LBDF2VOLT                        EQU LBBUS                           +00000028 EW00001       DUEL FED 2 BUS VOLTAGE
LBDF3VOLT                        EQU LBBUS                           +00000032 EW00001       DUEL FED 3 BUS VOLTAGE
LBDF4VOLT                        EQU LBBUS                           +00000036 EW00001       DUEL FED 4 BUS VOLTAGE
LBISO                            EQU LBBUS                           +00000037 LB00001       ISOLATION BUS ENERGIZED
LBDF1                            EQU LBBUS                           +00000038 LB00001       DUEL FED 1 BUS ENERGIZED
LBDF2                            EQU LBBUS                           +00000039 LB00001       DUEL FED 2 BUS ENERGIZED
LBDF3                            EQU LBBUS                           +00000040 LB00001       DUEL FED 3 BUS ENERGIZED
LBDF4                            EQU LBBUS                           +00000041 LB00001       DUEL FED 4 BUS ENERGIZED
XLB221S177                       EQU LBBUS                           +00000042 LB00001       MAIN BATTERY SWITCH 24-51-00-4
LBK135                           EQU LBBUS                           +00000043 LB00001       BATTERY RELAY 24-51-00-4
LB145K114                        EQU LBBUS                           +00000044 LB00001       LEFT GEN RELAY CONTACT 24-31-01-4
LB145K115                        EQU LBBUS                           +00000045 LB00001       RIGHT GEN RELAY CONTACT 24-31-02-4
LBISO                            EQU LBBUS                           +00000040 LB00001       ISOLATION BUS ENERGIZED                              000000037597 000000037598 000000037760
LBDF1                            EQU LBBUS                           +00000041 LB00001       DUEL FED 1 BUS ENERGIZED                             000000037598 000000037599 000000037760
LBDF2                            EQU LBBUS                           +00000042 LB00001       DUEL FED 2 BUS ENERGIZED                             000000037599 000000037600 000000037760
LBDF3                            EQU LBBUS                           +00000043 LB00001       DUEL FED 3 BUS ENERGIZED                             000000037600 000000037601 000000037760
LBDF4                            EQU LBBUS                           +00000044 LB00001       DUEL FED 4 BUS ENERGIZED                             000000037601 000000037602 000000037760
XLB221S177                       EQU LBBUS                           +00000045 LB00001       MAIN BATTERY SWITCH 24-51-00-4                       000000037602 000000037603 000000037760
LBK135                           EQU LBBUS                           +00000046 LB00001       BATTERY RELAY 24-51-00-4                             000000037603 000000037604 000000037760
LB145K114                        EQU LBBUS                           +00000047 LB00001       LEFT GEN RELAY CONTACT 24-31-01-4                    000000037604 000000037605 000000037760
LB145K115                        EQU LBBUS                           +00000048 LB00001       RIGHT GEN RELAY CONTACT 24-31-02-4                   000000037605 000000037606 000000037760

LBISO                           +EQU LBBUS                           +00000040 LB00001       ISOLATION BUS ENERGIZED                              000000037597 000000037598 000000037760
LBDF1                           +EQU LBBUS                           +00000041 LB00001       DUEL FED 1 BUS ENERGIZED                             000000037598 000000037599 000000037760
LBDF2                           +EQU LBBUS                           +00000042 LB00001       DUEL FED 2 BUS ENERGIZED                             000000037599 000000037600 000000037760
LBDF3                           +EQU LBBUS                           +00000043 LB00001       DUEL FED 3 BUS ENERGIZED                             000000037600 000000037601 000000037760
LBDF4                           +EQU LBBUS                           +00000044 LB00001       DUEL FED 4 BUS ENERGIZED                             000000037601 000000037602 000000037760
XLB221S177                      +EQU LBBUS                           +00000045 LB00001       MAIN BATTERY SWITCH 24-51-00-4                       000000037602 000000037603 000000037760
LBK135                          +EQU LBBUS                           +00000046 LB00001       BATTERY RELAY 24-51-00-4                             000000037603 000000037604 000000037760
LB145K114                       +EQU LBBUS                           +00000047 LB00001       LEFT GEN RELAY CONTACT 24-31-01-4                    000000037604 000000037605 000000037760
LB145K115                       +EQU LBBUS                           +00000048 LB00001       RIGHT GEN RELAY CONTACT 24-31-02-4                   000000037605 000000037606 000000037760

rwaf_sel_hdg                     EQU X_ABW1                          +00001116 EW00001       Selected Heading
FLPDET00                         EQU                                 +00696128 LB00001       Flap 0 Detent
FLPDET17                         EQU                                 +00696129 LB00001       Flap 17 Detent
FLPDET35                         EQU                                 +00696130 LB00001       Flap 35 Detent
rwaf_vor_ictmr                   EQU X_ABW1                          +00001120 EW00001       Cone of Confusion timer                                   ********6460 ********6464 ********6464
lbaf_vor_ic                      EQU X_ABB1                          +00000437 LB00002       VOR in Cone of Confusion                              ********8779 ********8781 ********8781
HNGRAVGRTEU                      EQU                                 +00696152 EW00001       Nose Gear Up Rate
HNGRAVGRTED                      EQU                                 +00696156 EW00001       Nose Gear Down Rate
LCBAC26CBNAV1                    EQU LCBCB                           +00000161 LB00001       26 VAC NAV 1 CB
LCBAC26CBNAV2                    EQU LCBCB                           +00000162 LB00001       26 VAC NAV 2 CB
LCBAC26CBCMPS1                   EQU LCBCB                           +00000163 LB00001       26 VAC COMPASS 1 CB
LCBAC26CBCMPS2                   EQU LCBCB                           +00000164 LB00001       26 VAC COMPASS 2 CB
LCBAC26CBADF                     EQU LCBCB                           +00000165 LB00001       26 VAC ADF CB
LCBAC26CBAP                      EQU LCBCB                           +00000165 LB00001       26 VAC AUTOPILOT CB
LCBAC115CBVGY                    EQU LCBCB                           +00000166 LB00001       115 VAC Vertical Gyro CB
XLA221S136INV1                   EQU LBBUS                           +00000049 LB00001       AC SELECT ON SWITCH INV 1
XLA221S136INV2                   EQU LBBUS                           +00000050 LB00001       AC SELECT ON SWITCH INV 2
LBK108                           EQU LBBUS                           +00000051 LB00001       INV 1 PWR RELAY
LBK109                           EQU LBBUS                           +******** LB00001       INV 2 PWR RELAY
LB120K2                          EQU LBBUS                           +******** LB00001       AC SELECT RELAY
LB226K106                        EQU LBBUS                           +******** LB00001       INV WARN LIGHT RELAY
LLACINV                          EQU LBBUS                           +******** LB00001       INV LT INTERMEDIATE
XKFADIRSIN                       EQU X_QAO2                          +******** EW00001       BANK ATTITUDE SIN                                    
XKFADIRCOS                       EQU X_QAO2                          +******** EW00001       BANK ATTITUDE COS                                    
XKFHSIGLIDESLOPE                 EQU X_QAO2                          +******** EW00001       HSI GLIDESLOPE                                       
XKFHSICOURSEDEV                  EQU X_QAO2                          +******** EW00001       HSI COURSE DEVIATION                                 
XKFHSITOFROM                     EQU X_QAO2                          +******** EW00001       HSI TO FROM FLAG                                     
XKFHSIHDGSIN                     EQU X_QAO2                          +******** EW00001       HSI HEADING SIN                                          
XKFHSIHDGCOS                     EQU X_QAO2                          +******** EW00001       HSI HEADING COS                                          
XKFHSIHDGBUG                     EQU X_QAI2                          +******** EW00001       HSI HEADING BUG                                      
XKFHSICOURSESELECTSIN            EQU X_QAI2                          +00000376 EW00001       HSI COURSE SELECT SIN                                
XKFHSICOURSESELECTCOS            EQU X_QAI2                          +00000380 EW00001       HSI COURSE SELECT COS                                
rwaf_vor_ictmr                  -EQU X_ABW1                          +00001120 EW00001       Cone of Confusion timer      
rwaf_vor_ictmr                   EQU X_ABW1                          +00001120 EW00002       Cone of Confusion timer      
rwaf_ats_pla                     EQU X_ABW1                          +00001128 EW00002       ATS pla for offline throttle control
rwaf_trg_arsp                    EQU X_ABW1                          +00001136 EW00001       ATS target airspeed
rwaf_arsp_error                  EQU X_ABW1                          +00001140 EW00001       ATS airspeed error
lbaf_mep_at                      EQU X_ABB1                          +00000439 LB00001       Mponder offline ATS on flag
lbaf_ats_enable                  EQU X_ABB1                          +00000440 LB00001       Mponder offline ATS enable flag 
rwaf_ap_gn                       EQU X_ABW1                          +00001144 EW00001       ATS airspeed error gain
DF22BPX                          EQU                                 +00696160 IW00002       Brake Press vs Brake Posn , ASkid On
DF23BPX                          EQU                                 +00696168 IW00002       Brake Press vs Brake Posn , ASkid Off
rwc_trim_rate                    EQU X_ABW1                          +00001148 EW00001       Offline auto trim rate
XLMASTERLIGHTSWITCH              EQU X_QDI2                          +00000558 LB00001       MASTER LIGHT SWITCH                                  
FC_DELTA_CL                      EQU                                 +00696176 EW00001       Delta CL
FC_DELTA_CD                      EQU                                 +00696180 EW00001       Delta CD
FC_DELTA_CM                      EQU                                 +00696184 EW00001       Delta CM
FC_DELTA_CY                      EQU                                 +00696188 EW00001       Delta CY
FC_DELTA_CN                      EQU                                 +00696192 EW00001       Delta CN
FC_DELTA_CR                      EQU                                 +00696196 EW00001       Delta CR
GXFEEDON                         EQU FLY_MISC_BASE                   +00000487 LB00001       FUEL CROSSFEED IN OPERATION
QDGPS114                         EQU Q_DITS                          +00027857 IW00001       GPS ARINC LABEL 114                                  
QDGPS114SSM                      EQU Q_DITS                          +00027861 LB00004       GPS ARINC LABEL 114                                  
QDGPS121                         EQU Q_DITS                          +00027865 IW00001       GPS ARINC LABEL 121                                  
QDGPS121SSM                      EQU Q_DITS                          +00027869 LB00004       GPS ARINC LABEL 121                                  
QDGPS147                         EQU Q_DITS                          +00027873 IW00001       GPS ARINC LABEL 147                                  
QDGPS147SSM                      EQU Q_DITS                          +00027877 LB00004       GPS ARINC LABEL 147                                  
QDGPS251                         EQU Q_DITS                          +00027881 IW00001       GPS ARINC LABEL 251                                  
QDGPS251SSM                      EQU Q_DITS                          +00027885 LB00004       GPS ARINC LABEL 251                                  
QDGPS252                         EQU Q_DITS                          +00027889 IW00001       GPS ARINC LABEL 252                                  
QDGPS252SSM                      EQU Q_DITS                          +00027893 LB00004       GPS ARINC LABEL 252                                  
QDGPS300                         EQU Q_DITS                          +00027897 IW00001       GPS ARINC LABEL 300                                  
QDGPS300SSM                      EQU Q_DITS                          +00027901 LB00004       GPS ARINC LABEL 300                                  
QDGPS314                         EQU Q_DITS                          +00027905 IW00001       GPS ARINC LABEL 314                                  
QDGPS314SSM                      EQU Q_DITS                          +00027909 LB00004       GPS ARINC LABEL 314                                  
QDGPS321                         EQU Q_DITS                          +00027913 IW00001       GPS ARINC LABEL 321                                  
QDGPS321SSM                      EQU Q_DITS                          +00027917 LB00004       GPS ARINC LABEL 321                                  
QDGPS352                         EQU Q_DITS                          +00027921 IW00001       GPS ARINC LABEL 352                                  
QDGPS352SSM                      EQU Q_DITS                          +00027925 LB00004       GPS ARINC LABEL 352                                  
QDGPS371                         EQU Q_DITS                          +00027929 IW00001       GPS ARINC LABEL 371                                  
QDGPS371SSM                      EQU Q_DITS                          +00027933 LB00004       GPS ARINC LABEL 371                                  
LPAIRCONDLOW                    +EQU locpp_cabinpressure             +00000333 LB00001       N1 TOO LOW FOR A/C OPERATION ANNUN INTER
XLLENGFIRE                       EQU LTGBASE                         +00000187 LB00001       B200 L ENG FIRE ANNUN
XLRENGFIRE                       EQU LTGBASE                         +00000188 LB00001       B200 R ENG FIRE ANNUN
XLACINV                          EQU LTGBASE                         +00000189 LB00001       B200 INVERTER ANNUN
XLALTALRT                        EQU LTGBASE                         +00000190 LB00001       B200 ALT WARN RED ANNUN
XLLGENOVHT                       EQU LTGBASE                         +00000191 LB00001       B200 L GEN OVER HEAT ANNUN
XLRGENOVHT                       EQU LTGBASE                         +00000192 LB00001       B200 R GEN OVER HEAT ANNUN
XLLCHIPDET                       EQU LTGBASE                         +00000193 LB00001       B200 L CHIP DETECT ANNUN
XLRCHIPDET                       EQU LTGBASE                         +00000194 LB00001       B200 R CHIP DETECT ANNUN
XLDOVRTEMP                       EQU LTGBASE                         +00000195 LB00001       B200 DUCT OVER TEMP ANNUN
XLRVSNORDY                       EQU LTGBASE                         +00000196 LB00001       B200 RVS NOT READY ANNUN
XLPRPSYNCON                      EQU LTGBASE                         +00000197 LB00001       B200 PROP SYNC ON ANNUN
XLELECTRMOFF                     EQU LTGBASE                         +00000198 LB00001       B200 ELEC TRIM OFF ANNUN
XLAIRCONDLOW                     EQU LTGBASE                         +00000199 LB00001       B200 AIR CND N1 LOW ANNUN
XLBRKDEICE                       EQU LTGBASE                         +00000200 LB00001       B200 BRAKE DEICE ON ANNUN
XLPASSOXYON                      EQU LTGBASE                         +00000201 LB00001       B200 PASSENGER OXY ON ANNUN
XLELECHEAT                       EQU LTGBASE                         +00000202 LB00001       B200 ELECTRIC HEAT ON ANNUN
XLLBLAIROFF                      EQU LTGBASE                         +00000203 LB00001       B200 L BLEED AIR OFF ANNUN
XLRBLAIROFF                      EQU LTGBASE                         +00000204 LB00001       B200 R BLEED AIR OFF ANNUN
LLLENGFIRE                       EQU LTGBASE                         +00000205 LB00001       B200 L ENG FIRE ANNUN INTERMEDIATE
LLRENGFIRE                       EQU LTGBASE                         +00000206 LB00001       B200 R ENG FIRE ANNUN INTERMEDIATE
LLACINV                          EQU LTGBASE                         +00000207 LB00001       B200 INVERTER ANNUN INTERMEDIATE
LLALTALRT                        EQU LTGBASE                         +00000208 LB00001       B200 ALT WARN RED ANNUN INTERMEDIATE
LLLGENOVHT                       EQU LTGBASE                         +00000209 LB00001       B200 L GEN OVER HEAT ANNUN INTERMEDIATE
LLRGENOVHT                       EQU LTGBASE                         +00000210 LB00001       B200 R GEN OVER HEAT ANNUN INTERMEDIATE
LLLCHIPDET                       EQU LTGBASE                         +00000211 LB00001       B200 L CHIP DETECT ANNUN INTERMEDIATE
LLRCHIPDET                       EQU LTGBASE                         +00000212 LB00001       B200 R CHIP DETECT ANNUN INTERMEDIATE
LLDOVRTEMP                       EQU LTGBASE                         +00000213 LB00001       B200 DUCT OVER TEMP ANNUN INTERMEDIATE
LLRVSNORDY                       EQU LTGBASE                         +00000214 LB00001       B200 RVS NOT READY ANNUN INTERMEDIATE
LLPRPSYNCON                      EQU LTGBASE                         +00000215 LB00001       B200 PROP SYNC ON ANNUN INTERMEDIATE
LLELECTRMOFF                     EQU LTGBASE                         +00000216 LB00001       B200 ELEC TRIM OFF ANNUN INTERMEDIATE
LLAIRCONDLOW                     EQU LTGBASE                         +00000217 LB00001       B200 AIR CND N1 LOW ANNUN INTERMEDIATE
LLBRKDEICE                       EQU LTGBASE                         +00000218 LB00001       B200 BRAKE DEICE ON ANNUN INTERMEDIATE
LLPASSOXYON                      EQU LTGBASE                         +00000219 LB00001       B200 PASSENGER OXY ON ANNUN INTERMEDIATE
LLELECHEAT                       EQU LTGBASE                         +00000220 LB00001       B200 ELECTRIC HEAT ON ANNUN INTERMEDIATE
LLLBLAIROFF                      EQU LTGBASE                         +00000221 LB00001       B200 L BLEED AIR OFF ANNUN INTERMEDIATE
LLRBLAIROFF                      EQU LTGBASE                         +00000222 LB00001       B200 R BLEED AIR OFF ANNUN INTERMEDIATE
TFCCDCOMB                        EQU                                 +00696200 IW00002       CD Basic LFI
RFCCDCOMB                        EQU                                 +00696208 EW00002       CD Basic 
CLS_DELTQ                       -EQU CLS_HOST_UNIQUE_F               +******** EW00001       Engine Torque Differential
CLS_RUD_BOOST_FOR                EQU CLS_HOST_UNIQUE_F               +******** EW00001       Rudder Boost Force From Autopilot 
DELTQ                           -EQU HOST_UNIQUE_F                   +******** EW00001       Engine Torque Differential
RUD_BOOST_FOR                    EQU HOST_UNIQUE_F                   +******** EW00001       Rudder Boost Force From Autopilot 
rwaf_rudboost_force              EQU X_ABW1                          +00001152 EW00001       Rudder boost calculated force required
rwaf_rudboost_force_app          EQU X_ABW1                          +00001156 EW00001       Rudder boost force applied
iwaf_rudboost_force              EQU X_ABW1                          +00001160 IW00001       Rudder boost force required table
LC_AAT_GYRO2_CB                 -EQU LBBUSA                          +******** LB00001       ATT NO.2 BUS PWR                                     000000653513 000000653514 000000653640
LC_ATT_GYRO2_CB                  EQU LBBUSA                          +******** LB00001       ATT NO.2 BUS PWR                                     000000653513 000000653514 000000653640


rwaf_gs_dev_rate_error          -EQU X_ABW1                          +00001180 EW00001       Glide Slope Deviation Rate Error                     ********6520 ********6524 ********6524
rwaf_gs_error_gn                -EQU X_ABW1                          +00001184 EW00001       GS Error Gain               
rwaf_gs_dev_rate_error           EQU X_ABW1                          +00001180 EW00001       Glide Slope Deviation Rate Error                     ********6520 ********6524 ********6524
rwaf_gs_error_gn                 EQU X_ABW1                          +00001184 EW00001       GS Error Gain               
rwaf_gs_dev_rate_targ            EQU X_ABW1                          +00001188 EW00002       GS deviation rate target                             ********6532 ********6540 ********6540
rwaf_gs_dev_rate_targ            EQU X_ABW1                          +00001188 EW00002       GS deviation rate target                             ********6532 ********6540 ********6540
rwaf_gs_dev_rate_targ_lim        EQU X_ABW1                          +00001196 EW00001       GS deviation rate target limit                       ********6540 ********6544 ********6544
rwaf_gs_dev_rate_error          -EQU X_ABW1                          +00001180 EW00001       Glide Slope Deviation Rate Error   
rwaf_gs_error_gn                -EQU X_ABW1                          +00001184 EW00001       GS Error Gain                   
rwaf_gs_dev_rate_targ           -EQU X_ABW1                          +00001188 EW00002       GS deviation rate target         
rwaf_gs_dev_rate_targ_lim       -EQU X_ABW1                          +00001196 EW00001       GS deviation rate target limit    
rwaf_fd_gs_altrate_targ          EQU X_ABW1                          +00001180 EW00001       GS deviation ROC target            
rwaf_fd_gs_roc_gn                EQU X_ABW1                          +00001184 EW00001       GS GS ROC gain                       
rwaf_fd_gs_dev_term              EQU X_ABW1                          +00001188 EW00002       Proportional GS Dev Term
rwaf_gs_dev_rate_term            EQU X_ABW1                          +00001196 EW00002       GS Dev Rate Term(used for GS dev dpng)
rwaf_gs_pitch_rate_term          EQU X_ABW1                          +00001204 EW00002       GS Pitch Rate Term
rwaf_gs_roc_term                 EQU X_ABW1                          +00001212 EW00002       GS Rate of Climb Term


rwaf_vor_beamdev_rollgn          EQU X_ABW1                          +00001220 EW00001       VOR Beam Dev Roll Gain
rwaf_fd_vor_crs_ergn             EQU X_ABW1                          +00001224 EW00001       VOR FD Crs Error Gain  
rwaf_fd_vor_ocrs_gn              EQU X_ABW1                          +00001228 EW00001       VOR FD VOR on course Error Gain
rwaf_vor_beam_rt_gn              EQU X_ABW1                          +00001232 EW00001       VOR FD Beam rate gain   
MBLMFVFC                        -EQU X_LFU1                          +00000325 LB00001       LEFT MOTIVE FLOW VALVE FAIL CLOSED
MBRMFVFC                        -EQU X_LFU1                          +00000326 LB00001       RIGHT MOTIVE FLOW VALVE FAIL CLOSED
MBLMFVFC                         EQU X_LFU1                          +00000328 LB00001       LEFT MOTIVE FLOW VALVE FAIL CLOSED
MBRMFVFC                         EQU X_LFU1                          +00000329 LB00001       RIGHT MOTIVE FLOW VALVE FAIL CLOSED
KFMTESTNO                        EQU F54EWBS                         +00000628 IW00001FM     MOTION TEST NUMBER            
RFMAMPFADE                       EQU F54EWBS                         +00000632 EW00001FM     TEST AMP FADE GAIN             
RFMAMPT                          EQU F54EWBS                         +00000636 EW00001FM     TEST AMP FADE IN 1/TAU          
RFMAMPZ                          EQU F54EWBS                         +00000640 EW00001FM     TEST AMPLITUDE Z                 
RFMAMPZC                         EQU F54EWBS                         +00000644 EW00001FM     TEST AMPLITUDE Z COMMAND          
RFMCPS                           EQU F54EWBS                         +00000648 EW00001FM     TEST AMPLITUDE CYCLES PER SEC      
RFMCYC                           EQU F54EWBS                         +00000652 EW00001FM     MOTION TEST CYCLE                   
RFMSIN                           EQU F54EWBS                         +00000656 EW00001FM     TEST AMPLITUDE SIN GENERATOR          
UFLTIP                           EQU F54EWBS                         +00000660 LB00001FL     FLIGHT IN PROGRESS                     
XMOTON                           EQU F54EWBS                         +00000661 LB00001MO     MOTION ON LIGHT                         
RFMAMPZG                         EQU F54EWBS                         +00000664 EW00001FM     TEST AMPLITUDE SETPOINT GAIN             
RFMAMPZO                         EQU F54EWBS                         +00000668 EW00001FM     TEST AMPLITUDE SETPOINT OFFSET            
RFMVIBQGN                        EQU F54EWBS                         +00000672 EW00001       motion vib cue gain                        
RFMVIBQGNC                       EQU F54EWBS                         +00000676 EW00001       motion vib cue gain cmd                     
LFMVIBQON                        EQU F54EWBS                         +00000680 LB00001       motion vib cue on flag                       
LFMTSTINPUT                      EQU F54EWBS                         +00000681 LB00001       motion inteface test input enable             
MAXA                             EQU F54EWBS                         +00000684 EW00001       motion interface test x accel                  
MAYA                             EQU F54EWBS                         +00000688 EW00001       motion interface test y accel                   
MAZA                             EQU F54EWBS                         +00000692 EW00001       motion interface test z accel                    
MDPAI                            EQU F54EWBS                         +00000696 EW00001       motion interface test pdot input accel            
MDQAI                            EQU F54EWBS                         +00000700 EW00001       motion interface test qdot input accel             
MDRAI                            EQU F54EWBS                         +00000704 EW00001       motion interface test rdot input accel              
MDPA                             EQU F54EWBS                         +00000708 EW00001       motion interface test pdot accel 
MDQA                             EQU F54EWBS                         +00000712 EW00001       motion interface test qdot accel  
MDRA                             EQU F54EWBS                         +00000716 EW00001       motion interface test rdot accel   
MPA                              EQU F54EWBS                         +00000720 EW00001       motion interface test p rate        
MQA                              EQU F54EWBS                         +00000724 EW00001       motion interface test q rate         
MRA                              EQU F54EWBS                         +00000728 EW00001       motion interface test r rate          
MTSTTAU                          EQU F54EWBS                         +00000732 EW00001       motion interface test washout tau      
RFMTSTFADE                       EQU F54EWBS                         +00000736 EW00001       motion interface test fade factor       
RFMFREQ                          EQU F54EWBS                         +00000628 EW00001       vib gain vs frequency freq ind var                   0********560 0********564 0********564
TFMFQGNX                         EQU F54EWBS                         +00000632 IW00001       vib gain vs frequency table                          0********804 0********808 0********808
TFMFQGNY                         EQU F54EWBS                         +00000636 IW00001       vib gain vs frequency table                          0********808 0********812 0********812
TFMFQGNZ                         EQU F54EWBS                         +00000640 IW00001       vib gain vs frequency table
RFMFQGNX                         EQU F54EWBS                         +00000740 EW00020       vib gain vs frequency                                0********564 0********644 0********644
RFMFQGNY                         EQU F54EWBS                         +00000820 EW00020       vib gain vs frequency                                0********644 0********724 0********724
RFMFQGNZ                         EQU F54EWBS                         +00000900 EW00020       vib gain vs frequency                                0********724 0********804 0********804
TENGRPMVIBX                      EQU F54EWBS                         +00000980 IW00004       vib gain for rpm vs n1                               000000674500 000000674516 000000674516
TENGRPMVIBY                      EQU F54EWBS                         +00000996 IW00004       vib gain for rpm vs n1                               000000674516 000000674532 000000674532
TENGRPMVIBZ                      EQU F54EWBS                         +00001112 IW00004       vib gain for rpm vs n1                               000000674532 000000674548 000000674548
TENGFRQ                          EQU F54EWBS                         +00001128 IW00004       vib gain for freq resp correction 
LMOTARST                         EQU F54EWBS                         +00000682 LB00001       MOTION ABORT RESET
RFMBTXHAGL                       EQU F54EWBS                         +00000644 EW00001       Height above ground level           
XNIDME2ON                        EQU X_QDI2                          +00000559 LB00001       DME 2 ON                                             
KFMBRXABORT                      EQU FMBUFRXG                        +00000080 IW00006       Motion leg abort status              
RFMBRXXPACCL                     EQU FMBUFRXG                        +00000104 EW00001       Pilot x accel (m/s2)                  
RFMBRXYPACCL                     EQU FMBUFRXG                        +00000108 EW00001       Pilot y accel (m/s2)                   
RFMBRXZPACCL                     EQU FMBUFRXG                        +00000112 EW00001       Pilot z accel (m/s2)                    
RFMBRXVEL1                       EQU FMBUFRXG                        +00000116 EW00001       Motion vel act 1 (m/s)                   
RFMBRXVEL2                       EQU FMBUFRXG                        +00000120 EW00001       Motion vel act 2 (m/s)                    
RFMBRXVEL3                       EQU FMBUFRXG                        +00000124 EW00001       Motion vel act 3 (m/s)                     
RFMBRXVEL4                       EQU FMBUFRXG                        +00000128 EW00001       Motion vel act 4 (m/s)                      
RFMBRXVEL5                       EQU FMBUFRXG                        +00000132 EW00001       Motion vel act 5 (m/s)                       
RFMBRXVEL6                       EQU FMBUFRXG                        +00000136 EW00001       Motion vel act 6 (m/s)                        
RFMBRXACL1                       EQU FMBUFRXG                        +00000140 EW00001       Motion accel act 1 (m/s)                       
RFMBRXACL2                       EQU FMBUFRXG                        +00000144 EW00001       Motion accel act 2 (m/s)                        
RFMBRXACL3                       EQU FMBUFRXG                        +00000148 EW00001       Motion accel act 3 (m/s)                         
RFMBRXACL4                       EQU FMBUFRXG                        +00000152 EW00001       Motion accel act 4 (m/s)                          
RFMBRXACL5                       EQU FMBUFRXG                        +00000156 EW00001       Motion accel act 5 (m/s)                           
RFMBRXACL6                       EQU FMBUFRXG                        +00000160 EW00001       Motion accel act 6 (m/s)  
MOT_OSNAP_CTL                    EQU FMBUFTX                         +00000440 IW00001       OSNAP CONTROL                    
MOT_OSNAP_ADR                    EQU FMBUFTX                         +00000444 IW00001       OSNAP LOGICAL ADDRESS             
MOT_OSNAP_TYP                    EQU FMBUFTX                         +00000448 IW00001       OSNAP VARIABLE TYPE                
MOT_OSNAP_NDX                    EQU FMBUFTX                         +00000452 IW00001       OSNAP BUFFER INDEX                  
KFMBTXSWTT                       EQU FMBUFTX                         +00000456 IW00001       Sin Wave Test Axis                   
RFMBTXSWTF                       EQU FMBUFTX                         +00000460 EW00006       Sin Wave Test Freq (Hz)               
RFMBTXSWTM                       EQU FMBUFTX                         +00000488 EW00006       Sin Wave Test Ampl (m,rads))           
RFMBTXSWTP                       EQU FMBUFTX                         +00000512 IW00006       Sin Wave Wave Type 0 - sin  1 - square  
RFMBTXHAGL                       EQU FMBUFTX                         +00000540 EW00001       Height above ground level     
RFMBTXHAGL                      -EQU F54EWBS                         +00000644 EW00001       Height above ground level        
RFMBTXHAGL                       EQU FMBUFTX                         +00000540 EW00001       Height above ground level     
tbrakepress                      EQU                                 +00696216 IW00002       Brake pressure table
TKRUD                            EQU locfv_all                       +00000420 EW00001ZT     Rudder vibration gain
XLB124CB10                       EQU XCBCB                           +00000161 LB00001       POP LEFT AUX TRANSFER CB                             
XLB124CB11                       EQU XCBCB                           +00000162 LB00001       POP LEFT STANDBY PUMP CB                             
XLB124CB3                        EQU XCBCB                           +00000163 LB00001       POP RIGHT STANDBY PUMP CB                            
XLB124CB4                        EQU XCBCB                           +00000164 LB00001       POP RIGHT AUX TRANSFER CB                            
XLB124CB7                        EQU XCBCB                           +00000165 LB00001       POP CROSS FEED CB                                    
XLB124CB25                       EQU XCBCB                           +00000166 LB00001       POP LEFT FLAPS CONTROL CB                            
XLB124CB27                       EQU XCBCB                           +00000167 LB00001       POP LEFT PROP DEICE CB                               
XLB146CB108                      EQU XCBCB                           +00000168 LB00001       POP LANDING GEAR WARN CB                             
XLB146CB142                      EQU XCBCB                           +00000169 LB00001       POP RUDDER BOOST CB                                  
XLB146CB145                      EQU XCBCB                           +00000170 LB00001       POP STALL WARN CB                                    
XLB146CB166                      EQU XCBCB                           +00000171 LB00001       POP PITCH TRIM CB                                    
XLB146CB169                      EQU XCBCB                           +00000172 LB00001       POP AVIONICS MASTER CB                               
XLB146CB170                      EQU XCBCB                           +00000173 LB00001       POP AVIONICS NO1 CB                                  
XLB146CB171                      EQU XCBCB                           +00000174 LB00001       POP AVIONICS NO2 CB                                  
XLB146CBADF                      EQU XCBCB                           +00000175 LB00001       POP ADF CB                                           
XLB146CBALTAIRD                  EQU XCBCB                           +00000176 LB00001       POP PILOTS ALTIMETER-AIR DATA CB                     
XLB146CBARLWRN                   EQU XCBCB                           +00000177 LB00001       POP ALTITUDE ALERTER CB                              
XLB146CBCENCDALT                 EQU XCBCB                           +00000178 LB00001       POP COPILOT ALTIMETER CB                             
XLB146CBCMPS1                    EQU XCBCB                           +00000179 LB00001       POP COMPASS NO1 CB                                   
XLB146CBCMPS2                    EQU XCBCB                           +00000180 LB00001       POP COMPASS NO2 CB                                   
XLB146CBCOMM1                    EQU XCBCB                           +00000181 LB00001       POP COMM NO1 CB                                      
XLB146CBCOMM2                    EQU XCBCB                           +00000182 LB00001       POP COMM NO2 CB                                      
XLB146CBDME1                     EQU XCBCB                           +00000183 LB00001       POP DME NO1 CB                                       
XLB146CBDME2                     EQU XCBCB                           +00000184 LB00001       POP DME NO2 CB                                       
XLB146CBDSP                      EQU XCBCB                           +00000185 LB00001       POP DSP CB                                           
XLB146CBEADI                     EQU XCBCB                           +00000186 LB00001       POP EADI CB                                          
XLB146CBFCSPWR                   EQU XCBCB                           +00000187 LB00001       POP FCS POWER CB                                     
XLB146CBEHSI                     EQU XCBCB                           +00000188 LB00001       POP EHSI CB                                          
XLB146CBNAV1                     EQU XCBCB                           +00000189 LB00001       POP NAV NO1 CB                                       
XLB146CBNAV2                     EQU XCBCB                           +00000190 LB00001       POP NAV NO2 CB                                       
XLB146CBRMI1                     EQU XCBCB                           +00000191 LB00001       POP RMI NO1 CB                                       
XLB146CBRMI2                     EQU XCBCB                           +00000192 LB00001       POP RMI NO2 CB                                       
loc_fv_aero                      EQU                                 +00696224 EW01200lc     locals for fv_aero                                   000000330340 000000334876 000000334876
I_FVA                            EQU loc_fv_aero                     +******** IW00001lc     LOCAL                                                000000330340 000000330344 000000334876
TAURA_FVA                        EQU loc_fv_aero                     +00000004 EW00001lc     LOCAL                                                000000330344 000000330348 000000334876
TBETAFTN_FVA                     EQU loc_fv_aero                     +00000008 EW00001lc     LOCAL                                                000000330348 000000330352 000000334876
TBETAX_FVA                       EQU loc_fv_aero                     +00000012 EW00018lc     LOCAL                                                000000330352 000000330424 000000334876
TBETAY_FVA                       EQU loc_fv_aero                     +00000084 EW00018lc     LOCAL                                                000000330424 000000330496 000000334876
TBETAZ_FVA                       EQU loc_fv_aero                     +00000156 EW00018lc     LOCAL                                                000000330496 000000330568 000000334876
TBSPDFTN_FVA                     EQU loc_fv_aero                     +00000228 EW00001lc     LOCAL                                                000000330568 000000330572 000000334876
TCARX_FVA                        EQU loc_fv_aero                     +00000232 EW00018lc     LOCAL                                                000000330572 000000330644 000000334876
TCARY_FVA                        EQU loc_fv_aero                     +00000304 EW00018lc     LOCAL                                                000000330644 000000330716 000000334876
TCARZ_FVA                        EQU loc_fv_aero                     +00000376 EW00018lc     LOCAL                                                000000330716 000000330788 000000334876
TCLHSDF_FVA                      EQU loc_fv_aero                     +00000448 EW00001lc     LOCAL                                                000000330788 000000330792 000000334876
TCZ0LS_FVA                       EQU loc_fv_aero                     +00000452 EW00001lc     LOCAL                                                000000330792 000000330796 000000334876
TCZ2HS_FVA                       EQU loc_fv_aero                     +00000456 EW00001lc     LOCAL                                                000000330796 000000330800 000000334876
TCZAHS_FVA                       EQU loc_fv_aero                     +00000460 EW00001lc     LOCAL                                                000000330800 000000330804 000000334876
TCZAHS0_FVA                      EQU loc_fv_aero                     +00000464 EW00001lc     LOCAL                                                000000330804 000000330808 000000334876
TCZALS_FVA                       EQU loc_fv_aero                     +00000468 EW00001lc     LOCAL                                                000000330808 000000330812 000000334876
TCZR_FVA                         EQU loc_fv_aero                     +00000472 EW00001lc     LOCAL                                                000000330812 000000330816 000000334876
TDOORV_FVA                       EQU loc_fv_aero                     +00000476 EW00001lc     LOCAL                                                000000330816 000000330820 000000334876
TDV01DAT_FVA                     EQU loc_fv_aero                     +00000480 EW00008lc     LOCAL                                                000000330820 000000330852 000000334876
TDV01NNP_FVA                     EQU loc_fv_aero                     +00000512 IW00001lc     LOCAL                                                000000330852 000000330856 000000334876
TDV01NP_FVA                      EQU loc_fv_aero                     +00000516 IW00001lc     LOCAL                                                000000330856 000000330860 000000334876
TDV02DAT_FVA                     EQU loc_fv_aero                     +00000520 EW00008lc     LOCAL                                                000000330860 000000330892 000000334876
TDV02NNP_FVA                     EQU loc_fv_aero                     +00000552 IW00001lc     LOCAL                                                000000330892 000000330896 000000334876
TDV02NP_FVA                      EQU loc_fv_aero                     +00000556 IW00001lc     LOCAL                                                000000330896 000000330900 000000334876
TDV03DAT_FVA                     EQU loc_fv_aero                     +00000560 EW00008lc     LOCAL                                                000000330900 000000330932 000000334876
TDV03NNP_FVA                     EQU loc_fv_aero                     +******** IW00001lc     LOCAL                                                000000330932 000000330936 000000334876
TDV03NP_FVA                      EQU loc_fv_aero                     +00000596 IW00001lc     LOCAL                                                000000330936 000000330940 000000334876
TDV04DAT_FVA                     EQU loc_fv_aero                     +00000600 EW00008lc     LOCAL                                                000000330940 000000330972 000000334876
TDV04NNP_FVA                     EQU loc_fv_aero                     +00000632 IW00001lc     LOCAL                                                000000330972 000000330976 000000334876
TDV04NP_FVA                      EQU loc_fv_aero                     +00000636 IW00001lc     LOCAL                                                000000330976 000000330980 000000334876
TDV05DAT_FVA                     EQU loc_fv_aero                     +00000640 EW00008lc     LOCAL                                                000000330980 000000331012 000000334876
TDV05NNP_FVA                     EQU loc_fv_aero                     +00000672 IW00001lc     LOCAL                                                000000331012 000000331016 000000334876
TDV05NP_FVA                      EQU loc_fv_aero                     +00000676 IW00001lc     LOCAL                                                000000331016 000000331020 000000334876
TDV06DAT_FVA                     EQU loc_fv_aero                     +00000680 EW00008lc     LOCAL                                                000000331020 000000331052 000000334876
TDV06NNP_FVA                     EQU loc_fv_aero                     +00000712 IW00001lc     LOCAL                                                000000331052 000000331056 000000334876
TDV06NP_FVA                      EQU loc_fv_aero                     +00000716 IW00001lc     LOCAL                                                000000331056 000000331060 000000334876
TDV07DAT_FVA                     EQU loc_fv_aero                     +00000720 EW00008lc     LOCAL                                                000000331060 000000331092 000000334876
TDV07NNP_FVA                     EQU loc_fv_aero                     +00000752 IW00001lc     LOCAL                                                000000331092 000000331096 000000334876
TDV07NP_FVA                      EQU loc_fv_aero                     +00000756 IW00001lc     LOCAL                                                000000331096 000000331100 000000334876
TDV08DAT_FVA                     EQU loc_fv_aero                     +00000760 EW00008lc     LOCAL                                                000000331100 000000331132 000000334876
TDV08NNP_FVA                     EQU loc_fv_aero                     +00000792 IW00001lc     LOCAL                                                000000331132 000000331136 000000334876
TDV08NP_FVA                      EQU loc_fv_aero                     +00000796 IW00001lc     LOCAL                                                000000331136 000000331140 000000334876
TDV09DAT_FVA                     EQU loc_fv_aero                     +00000800 EW00008lc     LOCAL                                                000000331140 000000331172 000000334876
TDV09NNP_FVA                     EQU loc_fv_aero                     +00000832 IW00001lc     LOCAL                                                000000331172 000000331176 000000334876
TDV09NP_FVA                      EQU loc_fv_aero                     +00000836 IW00001lc     LOCAL                                                000000331176 000000331180 000000334876
TFLAPFTN_FVA                     EQU loc_fv_aero                     +00000840 EW00001lc     LOCAL                                                000000331180 000000331184 000000334876
TFLAPX_FVA                       EQU loc_fv_aero                     +00000844 EW00018lc     LOCAL                                                000000331184 000000331256 000000334876
TFLAPY_FVA                       EQU loc_fv_aero                     +00000916 EW00018lc     LOCAL                                                000000331256 000000331328 000000334876
TFLAPZ_FVA                       EQU loc_fv_aero                     +00000988 EW00018lc     LOCAL                                                000000331328 000000331400 000000334876
TFPOSFTN_FVA                     EQU loc_fv_aero                     +******** EW00001lc     LOCAL                                                000000331400 000000331404 000000334876
TFREQV_FVA                       EQU loc_fv_aero                     +00001064 EW00020lc     LOCAL                                                000000331404 000000331484 000000334876
TGAMMAZ_FVA                      EQU loc_fv_aero                     +00001144 EW00001lc     LOCAL                                                000000331484 000000331488 000000334876
TGAMMAZL_FVA                     EQU loc_fv_aero                     +00001148 EW00001lc     LOCAL                                                000000331488 000000331492 000000334876
TGDPFTN_FVA                      EQU loc_fv_aero                     +00001152 EW00001lc     LOCAL                                                000000331492 000000331496 000000334876
TGEARX_FVA                       EQU loc_fv_aero                     +00001156 EW00018lc     LOCAL                                                000000331496 000000331568 000000334876
TGEARXV_FVA                      EQU loc_fv_aero                     +00001228 EW00001lc     LOCAL                                                000000331568 000000331572 000000334876
TGEARY_FVA                       EQU loc_fv_aero                     +00001232 EW00018lc     LOCAL                                                000000331572 000000331644 000000334876
TGEARYV_FVA                      EQU loc_fv_aero                     +00001304 EW00001lc     LOCAL                                                000000331644 000000331648 000000334876
TGEARZ_FVA                       EQU loc_fv_aero                     +00001308 EW00018lc     LOCAL                                                000000331648 000000331720 000000334876
TGEARZV_FVA                      EQU loc_fv_aero                     +00001380 EW00001lc     LOCAL                                                000000331720 000000331724 000000334876
TGZ1_FVA                         EQU loc_fv_aero                     +00001384 EW00001lc     LOCAL                                                000000331724 000000331728 000000334876
TGZ2_FVA                         EQU loc_fv_aero                     +00001388 EW00001lc     LOCAL                                                000000331728 000000331732 000000334876
TGZ3_FVA                         EQU loc_fv_aero                     +00001392 EW00001lc     LOCAL                                                000000331732 000000331736 000000334876
TIV01DAT_FVA                     EQU loc_fv_aero                     +00001396 EW00008lc     LOCAL                                                000000331736 000000331768 000000334876
TIV02DAT_FVA                     EQU loc_fv_aero                     +00001428 EW00008lc     LOCAL                                                000000331768 000000331800 000000334876
TIV03DAT_FVA                     EQU loc_fv_aero                     +00001460 EW00008lc     LOCAL                                                000000331800 000000331832 000000334876
TIV04DAT_FVA                     EQU loc_fv_aero                     +00001492 EW00008lc     LOCAL                                                000000331832 000000331864 000000334876
TIV05DAT_FVA                     EQU loc_fv_aero                     +00001524 EW00008lc     LOCAL                                                000000331864 000000331896 000000334876
TIV06DAT_FVA                     EQU loc_fv_aero                     +00001556 EW00008lc     LOCAL                                                000000331896 000000331928 000000334876
TIV07DAT_FVA                     EQU loc_fv_aero                     +00001588 EW00008lc     LOCAL                                                000000331928 000000331960 000000334876
TIV08DAT_FVA                     EQU loc_fv_aero                     +00001620 EW00008lc     LOCAL                                                000000331960 000000331992 000000334876
TIV09DAT_FVA                     EQU loc_fv_aero                     +00001652 EW00008lc     LOCAL                                                000000331992 000000332024 000000334876
TKBETAB_FVA                      EQU loc_fv_aero                     +00001684 EW00001lc     LOCAL                                                000000332024 000000332028 000000334876
TKCLHSDF_FVA                     EQU loc_fv_aero                     +00001688 EW00001lc     LOCAL                                                000000332028 000000332032 000000334876
TKCZAHS_FVA                      EQU loc_fv_aero                     +00001692 EW00001lc     LOCAL                                                000000332032 000000332036 000000334876
TKDOORB_FVA                      EQU loc_fv_aero                     +00001696 EW00001lc     LOCAL                                                000000332036 000000332040 000000334876
TKFLAP10_FVA                     EQU loc_fv_aero                     +00001700 EW00001lc     LOCAL                                                000000332040 000000332044 000000334876
TKFLAPB_FVA                      EQU loc_fv_aero                     +00001704 EW00001lc     LOCAL                                                000000332044 000000332048 000000334876
TKFREQ_FVA                       EQU loc_fv_aero                     +00001708 EW00001lc     LOCAL                                                000000332048 000000332052 000000334876
TKGAMMA_FVA                      EQU loc_fv_aero                     +00001712 EW00001lc     LOCAL                                                000000332052 000000332056 000000334876
TKLGB_FVA                        EQU loc_fv_aero                     +00001716 EW00001lc     LOCAL                                                000000332056 000000332060 000000334876
TKMACHB_FVA                      EQU loc_fv_aero                     +00001720 EW00001lc     LOCAL                                                000000332060 000000332064 000000334876
TKREVB_FVA                       EQU loc_fv_aero                     +00001724 EW00001lc     LOCAL                                                000000332064 000000332068 000000334876
TKSPDF_FVA                       EQU loc_fv_aero                     +00001728 EW00001lc     LOCAL                                                000000332068 000000332072 000000334876
TKSPOILB_FVA                     EQU loc_fv_aero                     +00001732 EW00001lc     LOCAL                                                000000332072 000000332076 000000334876
TKSTALB_FVA                      EQU loc_fv_aero                     +00001736 EW00001lc     LOCAL                                                000000332076 000000332080 000000334876
TKYRAIRB_FVA                     EQU loc_fv_aero                     +00001740 EW00001lc     LOCAL                                                000000332080 000000332084 000000334876
TKZRAIRB_FVA                     EQU loc_fv_aero                     +00001744 EW00001lc     LOCAL                                                000000332084 000000332088 000000334876
TLGFTN_FVA                       EQU loc_fv_aero                     +00001748 EW00001lc     LOCAL                                                000000332088 000000332092 000000334876
TLGPFTN_FVA                      EQU loc_fv_aero                     +00001752 EW00001lc     LOCAL                                                000000332092 000000332096 000000334876
TLGSPDFN_FVA                     EQU loc_fv_aero                     +00001756 EW00001lc     LOCAL                                                000000332096 000000332100 000000334876
TMACHFTN_FVA                     EQU loc_fv_aero                     +00001760 EW00001lc     LOCAL                                                000000332100 000000332104 000000334876
TMACHX_FVA                       EQU loc_fv_aero                     +00001764 EW00018lc     LOCAL                                                000000332104 000000332176 000000334876
TMACHY_FVA                       EQU loc_fv_aero                     +00001836 EW00018lc     LOCAL                                                000000332176 000000332248 000000334876
TMACHZ_FVA                       EQU loc_fv_aero                     +00001908 EW00018lc     LOCAL                                                000000332248 000000332320 000000334876
TPOSFTN_FVA                      EQU loc_fv_aero                     +00001980 EW00001lc     LOCAL                                                000000332320 000000332324 000000334876
TRAIRX_FVA                       EQU loc_fv_aero                     +00001984 EW00018lc     LOCAL                                                000000332324 000000332396 000000334876
TRAIRY_FVA                       EQU loc_fv_aero                     +00002056 EW00018lc     LOCAL                                                000000332396 000000332468 000000334876
TRAIRZ_FVA                       EQU loc_fv_aero                     +00002128 EW00018lc     LOCAL                                                000000332468 000000332540 000000334876
TRANDFTN_FVA                     EQU loc_fv_aero                     +00002200 EW00018lc     LOCAL                                                000000332540 000000332612 000000334876
TRANRA_FVA                       EQU loc_fv_aero                     +00002272 LB00001lc     LOCAL                                                000000332612 000000332613 000000334876
TREVFTN_FVA                      EQU loc_fv_aero                     +00002276 EW00001lc     LOCAL                                                000000332616 000000332620 000000334876
TREVSFTN_FVA                     EQU loc_fv_aero                     +00002280 EW00001lc     LOCAL                                                000000332620 000000332624 000000334876
TREVTOTP_FVA                     EQU loc_fv_aero                     +00002284 EW00001lc     LOCAL                                                000000332624 000000332628 000000334876
TREVX_FVA                        EQU loc_fv_aero                     +00002288 EW00018lc     LOCAL                                                000000332628 000000332700 000000334876
TREVY_FVA                        EQU loc_fv_aero                     +00002360 EW00018lc     LOCAL                                                000000332700 000000332772 000000334876
TREVZ_FVA                        EQU loc_fv_aero                     +00002432 EW00018lc     LOCAL                                                000000332772 000000332844 000000334876
TRGH1TAU_FVA                     EQU loc_fv_aero                     +00002504 EW00001lc     LOCAL                                                000000332844 000000332848 000000334876
TSPDFTNL_FVA                     EQU loc_fv_aero                     +00002508 EW00001lc     LOCAL                                                000000332848 000000332852 000000334876
TSPDFTNU_FVA                     EQU loc_fv_aero                     +00002512 EW00001lc     LOCAL                                                000000332852 000000332856 000000334876
TSPG1_FVA                        EQU loc_fv_aero                     +00002516 EW00001lc     LOCAL                                                000000332856 000000332860 000000334876
TSPG2_FVA                        EQU loc_fv_aero                     +00002520 EW00001lc     LOCAL                                                000000332860 000000332864 000000334876
TSPG3_FVA                        EQU loc_fv_aero                     +00002524 EW00001lc     LOCAL                                                000000332864 000000332868 000000334876
TSPLRX_FVA                       EQU loc_fv_aero                     +00002528 EW00018lc     LOCAL                                                000000332868 000000332940 000000334876
TSPLRY_FVA                       EQU loc_fv_aero                     +00002600 EW00018lc     LOCAL                                                000000332940 000000333012 000000334876
TSPLRZ_FVA                       EQU loc_fv_aero                     +00002672 EW00018lc     LOCAL                                                000000333012 000000333084 000000334876
TSPOILFN_FVA                     EQU loc_fv_aero                     +00002744 EW00001lc     LOCAL                                                000000333084 000000333088 000000334876
TSPOILPF_FVA                     EQU loc_fv_aero                     +00002748 EW00001lc     LOCAL                                                000000333088 000000333092 000000334876
TSTALFTN_FVA                     EQU loc_fv_aero                     +00002752 EW00001lc     LOCAL                                                000000333092 000000333096 000000334876
TSTALX_FVA                       EQU loc_fv_aero                     +00002756 EW00018lc     LOCAL                                                000000333096 000000333168 000000334876
TSTALY_FVA                       EQU loc_fv_aero                     +00002828 EW00018lc     LOCAL                                                000000333168 000000333240 000000334876
TSTALZ_FVA                       EQU loc_fv_aero                     +00002900 EW00018lc     LOCAL                                                000000333240 000000333312 000000334876
TSTL00X_FVA                      EQU loc_fv_aero                     +00002972 EW00018lc     LOCAL                                                000000333312 000000333384 000000334876
TSTL00Y_FVA                      EQU loc_fv_aero                     +00003044 EW00018lc     LOCAL                                                000000333384 000000333456 000000334876
TSTL00Z_FVA                      EQU loc_fv_aero                     +00003116 EW00018lc     LOCAL                                                000000333456 000000333528 000000334876
TSTL01X_FVA                      EQU loc_fv_aero                     +00003188 EW00018lc     LOCAL                                                000000333528 000000333600 000000334876
TSTL01Y_FVA                      EQU loc_fv_aero                     +00003260 EW00018lc     LOCAL                                                000000333600 000000333672 000000334876
TSTL01Z_FVA                      EQU loc_fv_aero                     +00003332 EW00018lc     LOCAL                                                000000333672 000000333744 000000334876
TSTL02X_FVA                      EQU loc_fv_aero                     +00003404 EW00018lc     LOCAL                                                000000333744 000000333816 000000334876
TSTL02Y_FVA                      EQU loc_fv_aero                     +00003476 EW00018lc     LOCAL                                                000000333816 000000333888 000000334876
TSTL02Z_FVA                      EQU loc_fv_aero                     +00003548 EW00018lc     LOCAL                                                000000333888 000000333960 000000334876
TSTL03X_FVA                      EQU loc_fv_aero                     +00003620 EW00018lc     LOCAL                                                000000333960 000000334032 000000334876
TSTL03Y_FVA                      EQU loc_fv_aero                     +00003692 EW00018lc     LOCAL                                                000000334032 000000334104 000000334876
TSTL03Z_FVA                      EQU loc_fv_aero                     +00003764 EW00018lc     LOCAL                                                000000334104 000000334176 000000334876
TSTL04X_FVA                      EQU loc_fv_aero                     +00003836 EW00018lc     LOCAL                                                000000334176 000000334248 000000334876
TSTL04Y_FVA                      EQU loc_fv_aero                     +00003908 EW00018lc     LOCAL                                                000000334248 000000334320 000000334876
TSTL04Z_FVA                      EQU loc_fv_aero                     +00003980 EW00018lc     LOCAL                                                000000334320 000000334392 000000334876
TSTL10X_FVA                      EQU loc_fv_aero                     +00004052 EW00018lc     LOCAL                                                000000334392 000000334464 000000334876
TSTL10Y_FVA                      EQU loc_fv_aero                     +00004124 EW00018lc     LOCAL                                                000000334464 000000334536 000000334876
TSTL10Z_FVA                      EQU loc_fv_aero                     +00004196 EW00018lc     LOCAL                                                000000334536 000000334608 000000334876
TVAT_FVA                         EQU loc_fv_aero                     +00004268 EW00001lc     LOCAL                                                000000334608 000000334612 000000334876
TVATAC1_FVA                      EQU loc_fv_aero                     +00004272 EW00001lc     LOCAL                                                000000334612 000000334616 000000334876
TVATACC_FVA                      EQU loc_fv_aero                     +00004276 EW00001lc     LOCAL                                                000000334616 000000334620 000000334876
TVATN1_FVA                       EQU loc_fv_aero                     +00004280 EW00001lc     LOCAL                                                000000334620 000000334624 000000334876
TWAT_FVA                         EQU loc_fv_aero                     +00004284 EW00001lc     LOCAL                                                000000334624 000000334628 000000334876
TWATAC1_FVA                      EQU loc_fv_aero                     +00004288 EW00001lc     LOCAL                                                000000334628 000000334632 000000334876
TWATACC_FVA                      EQU loc_fv_aero                     +00004292 EW00001lc     LOCAL                                                000000334632 000000334636 000000334876
TWATN1_FVA                       EQU loc_fv_aero                     +00004296 EW00001lc     LOCAL                                                000000334636 000000334640 000000334876
TX20HAC_FVA                      EQU loc_fv_aero                     +00004300 EW00001lc     LOCAL                                                000000334640 000000334644 000000334876
TXAMP_FVA                        EQU loc_fv_aero                     +00004304 EW00018lc     LOCAL                                                000000334644 000000334716 000000334876
TY20HAC_FVA                      EQU loc_fv_aero                     +00004376 EW00001lc     LOCAL                                                000000334716 000000334720 000000334876
TYAMP_FVA                        EQU loc_fv_aero                     +00004380 EW00018lc     LOCAL                                                000000334720 000000334792 000000334876
TYFTN_FVA                        EQU loc_fv_aero                     +00004452 EW00001lc     LOCAL                                                000000334792 000000334796 000000334876
TZ20HAC_FVA                      EQU loc_fv_aero                     +00004456 EW00001lc     LOCAL                                                000000334796 000000334800 000000334876
TZAMP_FVA                        EQU loc_fv_aero                     +00004460 EW00018lc     LOCAL  
TZFTN_FVA                        EQU loc_fv_aero                     +00004532 EW00001lc     LOCAL
loc_fv_all                       EQU                                 +00701024 EW00150lc     locals for fv_all                                    000000334876 000000335292 000000335292
I_FVAL                           EQU loc_fv_all                      +******** IW00001lc     LOCAL                                                000000334876 000000334880 000000335292
TAGAS_FVAL                       EQU loc_fv_all                      +00000004 EW00001lc     LOCAL                                                000000334880 000000334884 000000335292
TAGASN1_FVAL                     EQU loc_fv_all                      +00000008 EW00001lc     LOCAL                                                000000334884 000000334888 000000335292
TAGASPR_FVAL                     EQU loc_fv_all                      +00000012 EW00001lc     LOCAL                                                000000334888 000000334892 000000335292
TAILEX_FVAL                      EQU loc_fv_all                      +00000016 EW00001lc     LOCAL                                                000000334892 000000334896 000000335292
TARSTRB_FVAL                     EQU loc_fv_all                      +00000020 EW00001lc     LOCAL                                                000000334896 000000334900 000000335292
TAYALAT_FVAL                     EQU loc_fv_all                      +00000024 EW00001lc     LOCAL                                                000000334900 000000334904 000000335292
TAZAELV_FVAL                     EQU loc_fv_all                      +00000028 EW00001lc     LOCAL                                                000000334904 000000334908 000000335292
TAZASP_FVAL                      EQU loc_fv_all                      +00000032 EW00001lc     LOCAL                                                000000334908 000000334912 000000335292
TBGAS_FVAL                       EQU loc_fv_all                      +00000036 EW00001lc     LOCAL                                                000000334912 000000334916 000000335292
TBLNBRST_FVAL                    EQU loc_fv_all                      +00000040 LB00001lc     LOCAL                                                000000334916 000000334917 000000335292
TBPDCA_FVAL                      EQU loc_fv_all                      +00000041 LB00001lc     LOCAL                                                000000334917 000000334918 000000335292
TBPDCF_FVAL                      EQU loc_fv_all                      +00000042 LB00001lc     LOCAL                                                000000334918 000000334919 000000335292
TBPRD_FVAL                       EQU loc_fv_all                      +00000043 LB00001lc     LOCAL                                                000000334919 000000334920 000000335292
TBRNBRST_FVAL                    EQU loc_fv_all                      +00000044 LB00001lc     LOCAL                                                000000334920 000000334921 000000335292
TBURSTM_FVAL                     EQU loc_fv_all                      +00000048 EW00001lc     LOCAL                                                000000334924 000000334928 000000335292
TBZCNTLT_FVAL                    EQU loc_fv_all                      +******** EW00001lc     LOCAL                                                000000334928 000000334932 000000335292
TBZESD1_FVAL                     EQU loc_fv_all                      +00000056 EW00001lc     LOCAL                                                000000334932 000000334936 000000335292
TBZESD2_FVAL                     EQU loc_fv_all                      +00000060 EW00001lc     LOCAL                                                000000334936 000000334940 000000335292
TBZGLOCK_FVAL                    EQU loc_fv_all                      +00000064 EW00001lc     LOCAL                                                000000334940 000000334944 000000335292
TBZRD_FVAL                       EQU loc_fv_all                      +00000068 EW00001lc     LOCAL                                                000000334944 000000334948 000000335292
TBZSTRUT_FVAL                    EQU loc_fv_all                      +00000072 EW00001lc     LOCAL                                                000000334948 000000334952 000000335292
TDAIL_FVAL                       EQU loc_fv_all                      +00000076 EW00001lc     LOCAL                                                000000334952 000000334956 000000335292
TDELEV_FVAL                      EQU loc_fv_all                      +00000080 EW00001lc     LOCAL                                                000000334956 000000334960 000000335292
TDELTAR_FVAL                     EQU loc_fv_all                      +00000084 EW00001lc     LOCAL                                                000000334960 000000334964 000000335292
TDOLEOL_FVAL                     EQU loc_fv_all                      +00000088 EW00001lc     LOCAL                                                000000334964 000000334968 000000335292
TDOLEON_FVAL                     EQU loc_fv_all                      +00000092 EW00001lc     LOCAL                                                000000334968 000000334972 000000335292
TDOLEOR_FVAL                     EQU loc_fv_all                      +******** EW00001lc     LOCAL                                                000000334972 000000334976 000000335292
TDRUD_FVAL                       EQU loc_fv_all                      +******** EW00001lc     LOCAL                                                000000334976 000000334980 000000335292
TEBESD1_FVAL                     EQU loc_fv_all                      +00000104 LB00001lc     LOCAL                                                000000334980 000000334981 000000335292
TEBESD2_FVAL                     EQU loc_fv_all                      +00000105 LB00001lc     LOCAL                                                000000334981 000000334982 000000335292
TELEVEX_FVAL                     EQU loc_fv_all                      +00000108 EW00001lc     LOCAL                                                000000334984 000000334988 000000335292
TELVIN_FVAL                      EQU loc_fv_all                      +00000112 EW00001lc     LOCAL                                                000000334988 000000334992 000000335292
TELVOUT_FVAL                     EQU loc_fv_all                      +00000116 EW00001lc     LOCAL                                                000000334992 000000334996 000000335292
TEMPAYSE_FVAL                    EQU loc_fv_all                      +00000120 EW00001lc     LOCAL                                                000000334996 000000335000 000000335292
TEMPAZSE_FVAL                    EQU loc_fv_all                      +00000124 EW00001lc     LOCAL                                                000000335000 000000335004 000000335292
TFSB1_FVAL                       EQU loc_fv_all                      +00000128 LB00001lc     LOCAL                                                000000335004 000000335005 000000335292
TFSB2_FVAL                       EQU loc_fv_all                      +00000129 LB00001lc     LOCAL                                                000000335005 000000335006 000000335292
TFSBE_FVAL                       EQU loc_fv_all                      +00000130 LB00001lc     LOCAL                                                000000335006 000000335007 000000335292
TFSBR_FVAL                       EQU loc_fv_all                      +00000131 LB00001lc     LOCAL                                                000000335007 000000335008 000000335292
TFSPPA_FVAL                      EQU loc_fv_all                      +00000132 EW00001lc     LOCAL                                                000000335008 000000335012 000000335292
TFSPPAL_FVAL                     EQU loc_fv_all                      +00000136 EW00001lc     LOCAL                                                000000335012 000000335016 000000335292
TFUG3_FVAL                       EQU loc_fv_all                      +00000140 EW00001lc     LOCAL                                                000000335016 000000335020 000000335292
TFUG4_FVAL                       EQU loc_fv_all                      +00000144 EW00001lc     LOCAL                                                000000335020 000000335024 000000335292
TIPACTIV_FVAL                    EQU loc_fv_all                      +00000148 LB00001lc     LOCAL                                                000000335024 000000335025 000000335292
TK1TAUEL_FVAL                    EQU loc_fv_all                      +00000152 EW00001lc     LOCAL                                                000000335028 000000335032 000000335292
TK1TAURD_FVAL                    EQU loc_fv_all                      +00000156 EW00001lc     LOCAL                                                000000335032 000000335036 000000335292
TK2TAUEL_FVAL                    EQU loc_fv_all                      +00000160 EW00001lc     LOCAL                                                000000335036 000000335040 000000335292
TK2TAURD_FVAL                    EQU loc_fv_all                      +00000164 EW00001lc     LOCAL                                                000000335040 000000335044 000000335292
TKAIL_FVAL                       EQU loc_fv_all                      +00000168 EW00001lc     LOCAL                                                000000335044 000000335048 000000335292
TKCLLBMP_FVAL                    EQU loc_fv_all                      +00000172 EW00001lc     LOCAL                                                000000335048 000000335052 000000335292
TKELEV_FVAL                      EQU loc_fv_all                      +00000176 EW00001lc     LOCAL                                                000000335052 000000335056 000000335292
TKESD_FVAL                       EQU loc_fv_all                      +00000180 EW00001lc     LOCAL                                                000000335056 000000335060 000000335292
TKLDIST_FVAL                     EQU loc_fv_all                      +00000184 EW00001lc     LOCAL                                                000000335060 000000335064 000000335292
TKLENGTH_FVAL                    EQU loc_fv_all                      +00000188 EW00001lc     LOCAL                                                000000335064 000000335068 000000335292
TKLOCK_FVAL                      EQU loc_fv_all                      +00000192 EW00001lc     LOCAL                                                000000335068 000000335072 000000335292
TKLOMAGM_FVAL                    EQU loc_fv_all                      +00000196 EW00001lc     LOCAL                                                000000335072 000000335076 000000335292
TKLOMAGN_FVAL                    EQU loc_fv_all                      +00000200 EW00001lc     LOCAL                                                000000335076 000000335080 000000335292
TKMACCEL_FVAL                    EQU loc_fv_all                      +00000204 EW00001lc     LOCAL                                                000000335080 000000335084 000000335292
TKMG_FVAL                        EQU loc_fv_all                      +00000208 EW00003lc     LOCAL                                                000000335084 000000335096 000000335292
TKMLOCK_FVAL                     EQU loc_fv_all                      +00000220 EW00001lc     LOCAL                                                000000335096 000000335100 000000335292
TKNACCEL_FVAL                    EQU loc_fv_all                      +00000224 EW00001lc     LOCAL                                                000000335100 000000335104 000000335292
TKNG_FVAL                        EQU loc_fv_all                      +00000228 EW00003lc     LOCAL                                                000000335104 000000335116 000000335292
TKOFFSET_FVAL                    EQU loc_fv_all                      +00000240 EW00001lc     LOCAL                                                000000335116 000000335120 000000335292
TKP50_FVAL                       EQU loc_fv_all                      +00000244 EW00001lc     LOCAL                                                000000335120 000000335124 000000335292
TKP60_FVAL                       EQU loc_fv_all                      +00000248 EW00001lc     LOCAL                                                000000335124 000000335128 000000335292
TKRADTAR_FVAL                    EQU loc_fv_all                      +00000252 EW00001lc     LOCAL                                                000000335128 000000335132 000000335292
TKRD_FVAL                        EQU loc_fv_all                      +00000256 EW00001lc     LOCAL                                                000000335132 000000335136 000000335292
TKRUD_FVAL                       EQU loc_fv_all                      +00000260 EW00001lc     LOCAL                                                000000335136 000000335140 000000335292
TKSBL_FVAL                       EQU loc_fv_all                      +00000264 EW00001lc     LOCAL                                                000000335140 000000335144 000000335292
TKSBU_FVAL                       EQU loc_fv_all                      +00000268 EW00001lc     LOCAL                                                000000335144 000000335148 000000335292
TKSGR_FVAL                       EQU loc_fv_all                      +00000272 EW00001lc     LOCAL                                                000000335148 000000335152 000000335292
TKTBVIB_FVAL                     EQU loc_fv_all                      +00000276 EW00001lc     LOCAL                                                000000335152 000000335156 000000335292
TKYAFUND_FVAL                    EQU loc_fv_all                      +00000280 EW00001lc     LOCAL                                                000000335156 000000335160 000000335292
TKYPFUND_FVAL                    EQU loc_fv_all                      +00000284 EW00001lc     LOCAL                                                000000335160 000000335164 000000335292
TKYVFUND_FVAL                    EQU loc_fv_all                      +00000288 EW00001lc     LOCAL                                                000000335164 000000335168 000000335292
TKZAFUND_FVAL                    EQU loc_fv_all                      +00000292 EW00001lc     LOCAL                                                000000335168 000000335172 000000335292
TKZPFUND_FVAL                    EQU loc_fv_all                      +00000296 EW00001lc     LOCAL                                                000000335172 000000335176 000000335292
TKZVFUND_FVAL                    EQU loc_fv_all                      +00000300 EW00001lc     LOCAL                                                000000335176 000000335180 000000335292
TLATIN_FVAL                      EQU loc_fv_all                      +00000304 EW00001lc     LOCAL                                                000000335180 000000335184 000000335292
TLATOUT_FVAL                     EQU loc_fv_all                      +00000308 EW00001lc     LOCAL                                                000000335184 000000335188 000000335292
TLBWO_FVAL                       EQU loc_fv_all                      +00000312 LB00001lc     LOCAL                                                000000335188 000000335189 000000335292
TLGDL_FVAL                       EQU loc_fv_all                      +00000313 LB00001lc     LOCAL                                                000000335189 000000335190 000000335292
TLGDLN1_FVAL                     EQU loc_fv_all                      +00000314 LB00001lc     LOCAL                                                000000335190 000000335191 000000335292
TLGUL_FVAL                       EQU loc_fv_all                      +00000315 LB00001lc     LOCAL                                                000000335191 000000335192 000000335292
TLGULN1_FVAL                     EQU loc_fv_all                      +00000316 LB00001lc     LOCAL                                                000000335192 000000335193 000000335292
TLIBRST_FVAL                     EQU loc_fv_all                      +00000317 LB00001lc     LOCAL                                                000000335193 000000335194 000000335292
TLIM3_FVAL                       EQU loc_fv_all                      +00000320 EW00001lc     LOCAL                                                000000335196 000000335200 000000335292
TLOBRST_FVAL                     EQU loc_fv_all                      +00000324 LB00001lc     LOCAL                                                000000335200 000000335201 000000335292
TNBWO_FVAL                       EQU loc_fv_all                      +00000325 LB00001lc     LOCAL                                                000000335201 000000335202 000000335292
TNGDL_FVAL                       EQU loc_fv_all                      +00000326 LB00001lc     LOCAL                                                000000335202 000000335203 000000335292
TNGDLN1_FVAL                     EQU loc_fv_all                      +00000327 LB00001lc     LOCAL                                                000000335203 000000335204 000000335292
TNGUL_FVAL                       EQU loc_fv_all                      +00000328 LB00001lc     LOCAL                                                000000335204 000000335205 000000335292
TNGULN1_FVAL                     EQU loc_fv_all                      +00000329 LB00001lc     LOCAL                                                000000335205 000000335206 000000335292
TQTRTIME_FVAL                    EQU loc_fv_all                      +00000332 EW00001lc     LOCAL                                                000000335208 000000335212 000000335292
TRBWO_FVAL                       EQU loc_fv_all                      +00000336 LB00001lc     LOCAL                                                000000335212 000000335213 000000335292
TRGDL_FVAL                       EQU loc_fv_all                      +00000337 LB00001lc     LOCAL                                                000000335213 000000335214 000000335292
TRGDLN1_FVAL                     EQU loc_fv_all                      +00000338 LB00001lc     LOCAL                                                000000335214 000000335215 000000335292
TRGUL_FVAL                       EQU loc_fv_all                      +00000339 LB00001lc     LOCAL                                                000000335215 000000335216 000000335292
TRGULN1_FVAL                     EQU loc_fv_all                      +00000340 LB00001lc     LOCAL                                                000000335216 000000335217 000000335292
TRIBRST_FVAL                     EQU loc_fv_all                      +00000341 LB00001lc     LOCAL                                                000000335217 000000335218 000000335292
TROBRST_FVAL                     EQU loc_fv_all                      +00000342 LB00001lc     LOCAL                                                000000335218 000000335219 000000335292
TRUDEX_FVAL                      EQU loc_fv_all                      +00000344 EW00001lc     LOCAL                                                000000335220 000000335224 000000335292
TRWTSTIM_FVAL                    EQU loc_fv_all                      +00000348 EW00001lc     LOCAL                                                000000335224 000000335228 000000335292
TSBB_FVAL                        EQU loc_fv_all                      +00000352 EW00001lc     LOCAL                                                000000335228 000000335232 000000335292
TSBP_FVAL                        EQU loc_fv_all                      +00000356 EW00001lc     LOCAL                                                000000335232 000000335236 000000335292
TSGR_FVAL                        EQU loc_fv_all                      +00000360 EW00001lc     LOCAL                                                000000335236 000000335240 000000335292
TSPBK_FVAL                       EQU loc_fv_all                      +00000364 EW00001lc     LOCAL                                                000000335240 000000335244 000000335292
TSPD_FVAL                        EQU loc_fv_all                      +00000368 EW00001lc     LOCAL                                                000000335244 000000335248 000000335292
TTBWC_FVAL                       EQU loc_fv_all                      +******** EW00001lc     LOCAL                                                000000335248 000000335252 000000335292
TTSG_FVAL                        EQU loc_fv_all                      +00000376 EW00001lc     LOCAL                                                000000335252 000000335256 000000335292
TXTDB4_FVAL                      EQU loc_fv_all                      +00000380 EW00001lc     LOCAL                                                000000335256 000000335260 000000335292
TYDDSTRF_FVAL                    EQU loc_fv_all                      +00000384 EW00001lc     LOCAL                                                000000335260 000000335264 000000335292
TYDSTRF_FVAL                     EQU loc_fv_all                      +00000388 EW00001lc     LOCAL                                                000000335264 000000335268 000000335292
TYSTRF_FVAL                      EQU loc_fv_all                      +00000392 EW00001lc     LOCAL                                                000000335268 000000335272 000000335292
TYTDB4_FVAL                      EQU loc_fv_all                      +00000396 EW00001lc     LOCAL                                                000000335272 000000335276 000000335292
TZDDSTRF_FVAL                    EQU loc_fv_all                      +00000400 EW00001lc     LOCAL                                                000000335276 000000335280 000000335292
TZDSTRF_FVAL                     EQU loc_fv_all                      +00000404 EW00001lc     LOCAL                                                000000335280 000000335284 000000335292
TZSTRF_FVAL                      EQU loc_fv_all                      +00000408 EW00001lc     LOCAL                                                000000335284 000000335288 000000335292
TZTDB4_FVAL                      EQU loc_fv_all                      +00000412 EW00001lc     LOCAL
loc_fv_eng                       EQU                                 +00701624 EW00350lc     locals for fv_eng                                    000000335292 000000336500 000000336500
I_FVE                            EQU loc_fv_eng                      +******** IW00001lc     LOCAL                                                000000335292 000000335296 000000336500
MDEDM1_FVE                       EQU loc_fv_eng                      +00000004 EW00001       LOCAL                                                000000335296 000000335300 000000336500
MDEDM2_FVE                       EQU loc_fv_eng                      +00000008 EW00001       LOCAL                                                000000335300 000000335304 000000336500
TB100X_FVE                       EQU loc_fv_eng                      +00000012 EW00018lc     LOCAL                                                000000335304 000000335376 000000336500
TB100Y_FVE                       EQU loc_fv_eng                      +00000084 EW00018lc     LOCAL                                                000000335376 000000335448 000000336500
TB100Z_FVE                       EQU loc_fv_eng                      +00000156 EW00018lc     LOCAL                                                000000335448 000000335520 000000336500
TB25X_FVE                        EQU loc_fv_eng                      +00000228 EW00018lc     LOCAL                                                000000335520 000000335592 000000336500
TB25Y_FVE                        EQU loc_fv_eng                      +00000300 EW00018lc     LOCAL                                                000000335592 000000335664 000000336500
TB25Z_FVE                        EQU loc_fv_eng                      +******** EW00018lc     LOCAL                                                000000335664 000000335736 000000336500
TB55X_FVE                        EQU loc_fv_eng                      +00000444 EW00018lc     LOCAL                                                000000335736 000000335808 000000336500
TB55Y_FVE                        EQU loc_fv_eng                      +00000516 EW00018lc     LOCAL                                                000000335808 000000335880 000000336500
TB55Z_FVE                        EQU loc_fv_eng                      +******** EW00018lc     LOCAL                                                000000335880 000000335952 000000336500
TB96X_FVE                        EQU loc_fv_eng                      +00000660 EW00018lc     LOCAL                                                000000335952 000000336024 000000336500
TB96Y_FVE                        EQU loc_fv_eng                      +00000732 EW00018lc     LOCAL                                                000000336024 000000336096 000000336500
TB96Z_FVE                        EQU loc_fv_eng                      +00000804 EW00018lc     LOCAL                                                000000336096 000000336168 000000336500
TENGRPMH_FVE                     EQU loc_fv_eng                      +00000876 EW00001lc     LOCAL                                                000000336168 000000336172 000000336500
TENGRPML_FVE                     EQU loc_fv_eng                      +00000880 EW00001lc     LOCAL                                                000000336172 000000336176 000000336500
TGNDSPD_FVE                      EQU loc_fv_eng                      +00000884 EW00001lc     LOCAL                                                000000336176 000000336180 000000336500
TKEDM1_FVE                       EQU loc_fv_eng                      +00000888 EW00001lc     LOCAL                                                000000336180 000000336184 000000336500
TKEDM2_FVE                       EQU loc_fv_eng                      +00000892 EW00001lc     LOCAL                                                000000336184 000000336188 000000336500
TKEDMTOT_FVE                     EQU loc_fv_eng                      +00000896 EW00001lc     LOCAL                                                000000336188 000000336192 000000336500
TKENG_FVE                        EQU loc_fv_eng                      +00000900 EW00001lc     LOCAL                                                000000336192 000000336196 000000336500
TRANDFTN_FVE                     EQU loc_fv_eng                      +00000904 EW00018lc     LOCAL                                                000000336196 000000336268 000000336500
TRPMDIFF_FVE                     EQU loc_fv_eng                      +00000976 EW00001lc     LOCAL                                                000000336268 000000336272 000000336500
TRPMGAIN_FVE                     EQU loc_fv_eng                      +00000980 EW00001lc     LOCAL                                                000000336272 000000336276 000000336500
TSPDFTNL_FVE                     EQU loc_fv_eng                      +00000984 EW00001lc     LOCAL                                                000000336276 000000336280 000000336500
TSPDFTNU_FVE                     EQU loc_fv_eng                      +00000988 EW00001lc     LOCAL                                                000000336280 000000336284 000000336500
TXAMP_FVE                        EQU loc_fv_eng                      +00000992 EW00018lc     LOCAL                                                000000336284 000000336356 000000336500
TYAMP_FVE                        EQU loc_fv_eng                      +00001064 EW00018lc     LOCAL                                                000000336356 000000336428 000000336500
TZAMP_FVE                        EQU loc_fv_eng                      +00001136 EW00018lc     LOCAL
loc_fv_grnd                      EQU                                 +00703024 EW01300lc     locals for fv_grnd                                   000000336500 000000341472 000000341472
I_FVG                            EQU loc_fv_grnd                     +******** IW00001lc     LOCAL                                                000000336500 000000336504 000000341472
T1_FVG                           EQU loc_fv_grnd                     +00000004 EW00001lc     LOCAL                                                000000336504 000000336508 000000341472
T2_FVG                           EQU loc_fv_grnd                     +00000008 EW00001lc     LOCAL                                                000000336508 000000336512 000000341472
TC100X_FVG                       EQU loc_fv_grnd                     +00000012 EW00018lc     LOCAL                                                000000336512 000000336584 000000341472
TC100Y_FVG                       EQU loc_fv_grnd                     +00000084 EW00018lc     LOCAL                                                000000336584 000000336656 000000341472
TC100Z_FVG                       EQU loc_fv_grnd                     +00000156 EW00018lc     LOCAL                                                000000336656 000000336728 000000341472
TC10X_FVG                        EQU loc_fv_grnd                     +00000228 EW00018lc     LOCAL                                                000000336728 000000336800 000000341472
TC10Y_FVG                        EQU loc_fv_grnd                     +00000300 EW00018lc     LOCAL                                                000000336800 000000336872 000000341472
TC10Z_FVG                        EQU loc_fv_grnd                     +******** EW00018lc     LOCAL                                                000000336872 000000336944 000000341472
TC120X_FVG                       EQU loc_fv_grnd                     +00000444 EW00018lc     LOCAL                                                000000336944 000000337016 000000341472
TC120Y_FVG                       EQU loc_fv_grnd                     +00000516 EW00018lc     LOCAL                                                000000337016 000000337088 000000341472
TC120Z_FVG                       EQU loc_fv_grnd                     +******** EW00018lc     LOCAL                                                000000337088 000000337160 000000341472
TC30X_FVG                        EQU loc_fv_grnd                     +00000660 EW00018lc     LOCAL                                                000000337160 000000337232 000000341472
TC30Y_FVG                        EQU loc_fv_grnd                     +00000732 EW00018lc     LOCAL                                                000000337232 000000337304 000000341472
TC30Z_FVG                        EQU loc_fv_grnd                     +00000804 EW00018lc     LOCAL                                                000000337304 000000337376 000000341472
TC50X_FVG                        EQU loc_fv_grnd                     +00000876 EW00018lc     LOCAL                                                000000337376 000000337448 000000341472
TC50Y_FVG                        EQU loc_fv_grnd                     +00000948 EW00018lc     LOCAL                                                000000337448 000000337520 000000341472
TC50Z_FVG                        EQU loc_fv_grnd                     +00001020 EW00018lc     LOCAL                                                000000337520 000000337592 000000341472
TCOMFTN_FVG                      EQU loc_fv_grnd                     +00001092 EW00001lc     LOCAL                                                000000337592 000000337596 000000341472
TCOMX_FVG                        EQU loc_fv_grnd                     +00001096 EW00018lc     LOCAL                                                000000337596 000000337668 000000341472
TCOMY_FVG                        EQU loc_fv_grnd                     +00001168 EW00018lc     LOCAL                                                000000337668 000000337740 000000341472
TCOMZ_FVG                        EQU loc_fv_grnd                     +00001240 EW00018lc     LOCAL                                                000000337740 000000337812 000000341472
TDIST1_FVG                       EQU loc_fv_grnd                     +00001312 EW00001lc     LOCAL                                                000000337812 000000337816 000000341472
TDIST2_FVG                       EQU loc_fv_grnd                     +00001316 EW00001lc     LOCAL                                                000000337816 000000337820 000000341472
TDV01DAT_FVG                     EQU loc_fv_grnd                     +00001320 EW00013lc     LOCAL                                                000000337820 000000337872 000000341472
TDV01NNP_FVG                     EQU loc_fv_grnd                     +00001372 IW00001lc     LOCAL                                                000000337872 000000337876 000000341472
TDV01NP_FVG                      EQU loc_fv_grnd                     +00001376 IW00001lc     LOCAL                                                000000337876 000000337880 000000341472
TDV02DAT_FVG                     EQU loc_fv_grnd                     +00001380 EW00013lc     LOCAL                                                000000337880 000000337932 000000341472
TDV02NNP_FVG                     EQU loc_fv_grnd                     +00001432 IW00001lc     LOCAL                                                000000337932 000000337936 000000341472
TDV02NP_FVG                      EQU loc_fv_grnd                     +00001436 IW00001lc     LOCAL                                                000000337936 000000337940 000000341472
TDV03DAT_FVG                     EQU loc_fv_grnd                     +00001440 EW00013lc     LOCAL                                                000000337940 000000337992 000000341472
TDV03NNP_FVG                     EQU loc_fv_grnd                     +00001492 IW00001lc     LOCAL                                                000000337992 000000337996 000000341472
TDV03NP_FVG                      EQU loc_fv_grnd                     +00001496 IW00001lc     LOCAL                                                000000337996 000000338000 000000341472
TE100X_FVG                       EQU loc_fv_grnd                     +00001500 EW00018lc     LOCAL                                                000000338000 000000338072 000000341472
TE100Y_FVG                       EQU loc_fv_grnd                     +00001572 EW00018lc     LOCAL                                                000000338072 000000338144 000000341472
TE100Z_FVG                       EQU loc_fv_grnd                     +00001644 EW00018lc     LOCAL                                                000000338144 000000338216 000000341472
TE10X_FVG                        EQU loc_fv_grnd                     +00001716 EW00018lc     LOCAL                                                000000338216 000000338288 000000341472
TE10Y_FVG                        EQU loc_fv_grnd                     +00001788 EW00018lc     LOCAL                                                000000338288 000000338360 000000341472
TE10Z_FVG                        EQU loc_fv_grnd                     +00001860 EW00018lc     LOCAL                                                000000338360 000000338432 000000341472
TE120X_FVG                       EQU loc_fv_grnd                     +00001932 EW00018lc     LOCAL                                                000000338432 000000338504 000000341472
TE120Y_FVG                       EQU loc_fv_grnd                     +00002004 EW00018lc     LOCAL                                                000000338504 000000338576 000000341472
TE120Z_FVG                       EQU loc_fv_grnd                     +00002076 EW00018lc     LOCAL                                                000000338576 000000338648 000000341472
TE30X_FVG                        EQU loc_fv_grnd                     +00002148 EW00018lc     LOCAL                                                000000338648 000000338720 000000341472
TE30Y_FVG                        EQU loc_fv_grnd                     +00002220 EW00018lc     LOCAL                                                000000338720 000000338792 000000341472
TE30Z_FVG                        EQU loc_fv_grnd                     +00002292 EW00018lc     LOCAL                                                000000338792 000000338864 000000341472
TE50X_FVG                        EQU loc_fv_grnd                     +00002364 EW00018lc     LOCAL                                                000000338864 000000338936 000000341472
TE50Y_FVG                        EQU loc_fv_grnd                     +00002436 EW00018lc     LOCAL                                                000000338936 000000339008 000000341472
TE50Z_FVG                        EQU loc_fv_grnd                     +00002508 EW00018lc     LOCAL                                                000000339008 000000339080 000000341472
TEXTFTN_FVG                      EQU loc_fv_grnd                     +00002580 EW00001lc     LOCAL                                                000000339080 000000339084 000000341472
TEXTX_FVG                        EQU loc_fv_grnd                     +00002584 EW00018lc     LOCAL                                                000000339084 000000339156 000000341472
TEXTY_FVG                        EQU loc_fv_grnd                     +00002656 EW00018lc     LOCAL                                                000000339156 000000339228 000000341472
TEXTZ_FVG                        EQU loc_fv_grnd                     +00002728 EW00018lc     LOCAL                                                000000339228 000000339300 000000341472
TFFZ_FVG                         EQU loc_fv_grnd                     +00002800 EW00001lc     LOCAL                                                000000339300 000000339304 000000341472
TFFZG_FVG                        EQU loc_fv_grnd                     +00002804 EW00001lc     LOCAL                                                000000339304 000000339308 000000341472
TFLATFTN_FVG                     EQU loc_fv_grnd                     +00002808 EW00001lc     LOCAL                                                000000339308 000000339312 000000341472
TFREQV_FVG                       EQU loc_fv_grnd                     +00002812 EW00020lc     LOCAL                                                000000339312 000000339392 000000341472
TFSCUFF_FVG                      EQU loc_fv_grnd                     +00002892 LB00001lc     LOCAL                                                000000339392 000000339393 000000341472
TINSTFTN_FVG                     EQU loc_fv_grnd                     +00002896 EW00001lc     LOCAL                                                000000339396 000000339400 000000341472
TIV01DAT_FVG                     EQU loc_fv_grnd                     +00002900 EW00013lc     LOCAL                                                000000339400 000000339452 000000341472
TIV02DAT_FVG                     EQU loc_fv_grnd                     +00002952 EW00013lc     LOCAL                                                000000339452 000000339504 000000341472
TIV03DAT_FVG                     EQU loc_fv_grnd                     +00003004 EW00013lc     LOCAL                                                000000339504 000000339556 000000341472
TKDRGHR_FVG                      EQU loc_fv_grnd                     +00003056 EW00001lc     LOCAL                                                000000339556 000000339560 000000341472
TKFREQ_FVG                       EQU loc_fv_grnd                     +00003060 EW00001lc     LOCAL                                                000000339560 000000339564 000000341472
TKFTIREA_FVG                     EQU loc_fv_grnd                     +00003064 EW00001lc     LOCAL                                                000000339564 000000339568 000000341472
TKILLF_FVG                       EQU loc_fv_grnd                     +00003068 IW00018lc     LOCAL                                                000000339568 000000339640 000000341472
TKRNY_FVG                        EQU loc_fv_grnd                     +00003140 EW00001lc     LOCAL                                                000000339640 000000339644 000000341472
TKTAUZ_FVG                       EQU loc_fv_grnd                     +00003144 EW00001lc     LOCAL                                                000000339644 000000339648 000000341472
TKTBM_FVG                        EQU loc_fv_grnd                     +00003148 EW00001lc     LOCAL                                                000000339648 000000339652 000000341472
TKTBN_FVG                        EQU loc_fv_grnd                     +00003152 EW00001lc     LOCAL                                                000000339652 000000339656 000000341472
TKYGNA_FVG                       EQU loc_fv_grnd                     +00003156 EW00001lc     LOCAL                                                000000339656 000000339660 000000341472
TLDCOEF_FVG                      EQU loc_fv_grnd                     +00003160 EW00001lc     LOCAL                                                000000339660 000000339664 000000341472
TN100X_FVG                       EQU loc_fv_grnd                     +00003164 EW00018lc     LOCAL                                                000000339664 000000339736 000000341472
TN100Y_FVG                       EQU loc_fv_grnd                     +00003236 EW00018lc     LOCAL                                                000000339736 000000339808 000000341472
TN100Z_FVG                       EQU loc_fv_grnd                     +00003308 EW00018lc     LOCAL                                                000000339808 000000339880 000000341472
TN10X_FVG                        EQU loc_fv_grnd                     +00003380 EW00018lc     LOCAL                                                000000339880 000000339952 000000341472
TN10Y_FVG                        EQU loc_fv_grnd                     +00003452 EW00018lc     LOCAL                                                000000339952 000000340024 000000341472
TN10Z_FVG                        EQU loc_fv_grnd                     +00003524 EW00018lc     LOCAL                                                000000340024 000000340096 000000341472
TN120X_FVG                       EQU loc_fv_grnd                     +00003596 EW00018lc     LOCAL                                                000000340096 000000340168 000000341472
TN120Y_FVG                       EQU loc_fv_grnd                     +00003668 EW00018lc     LOCAL                                                000000340168 000000340240 000000341472
TN120Z_FVG                       EQU loc_fv_grnd                     +00003740 EW00018lc     LOCAL                                                000000340240 000000340312 000000341472
TN30X_FVG                        EQU loc_fv_grnd                     +00003812 EW00018lc     LOCAL                                                000000340312 000000340384 000000341472
TN30Y_FVG                        EQU loc_fv_grnd                     +00003884 EW00018lc     LOCAL                                                000000340384 000000340456 000000341472
TN30Z_FVG                        EQU loc_fv_grnd                     +00003956 EW00018lc     LOCAL                                                000000340456 000000340528 000000341472
TN50X_FVG                        EQU loc_fv_grnd                     +00004028 EW00018lc     LOCAL                                                000000340528 000000340600 000000341472
TN50Y_FVG                        EQU loc_fv_grnd                     +00004100 EW00018lc     LOCAL                                                000000340600 000000340672 000000341472
TN50Z_FVG                        EQU loc_fv_grnd                     +00004172 EW00018lc     LOCAL                                                000000340672 000000340744 000000341472
TNORFTN_FVG                      EQU loc_fv_grnd                     +00004244 EW00001lc     LOCAL                                                000000340744 000000340748 000000341472
TNORX_FVG                        EQU loc_fv_grnd                     +00004248 EW00018lc     LOCAL                                                000000340748 000000340820 000000341472
TNORY_FVG                        EQU loc_fv_grnd                     +00004320 EW00018lc     LOCAL                                                000000340820 000000340892 000000341472
TNORZ_FVG                        EQU loc_fv_grnd                     +00004392 EW00018lc     LOCAL                                                000000340892 000000340964 000000341472
TNSEL1_FVG                       EQU loc_fv_grnd                     +00004464 EW00001lc     LOCAL                                                000000340964 000000340968 000000341472
TNSEL2_FVG                       EQU loc_fv_grnd                     +00004468 EW00001lc     LOCAL                                                000000340968 000000340972 000000341472
TRANDFTN_FVG                     EQU loc_fv_grnd                     +00004472 EW00018lc     LOCAL                                                000000340972 000000341044 000000341472
TRANRA_FVG                       EQU loc_fv_grnd                     +00004544 LB00001lc     LOCAL                                                000000341044 000000341045 000000341472
TRNGFTN1_FVG                     EQU loc_fv_grnd                     +00004548 EW00001lc     LOCAL                                                000000341048 000000341052 000000341472
TRNGFTN2_FVG                     EQU loc_fv_grnd                     +00004552 EW00001lc     LOCAL                                                000000341052 000000341056 000000341472
TRNYFTN_FVG                      EQU loc_fv_grnd                     +00004556 EW00001lc     LOCAL                                                000000341056 000000341060 000000341472
TSCUFFTN_FVG                     EQU loc_fv_grnd                     +00004560 EW00001lc     LOCAL                                                000000341060 000000341064 000000341472
TSCUFX_FVG                       EQU loc_fv_grnd                     +00004564 EW00001lc     LOCAL                                                000000341064 000000341068 000000341472
TSCUFY_FVG                       EQU loc_fv_grnd                     +00004568 EW00001lc     LOCAL                                                000000341068 000000341072 000000341472
TSCUFZ_FVG                       EQU loc_fv_grnd                     +00004572 EW00001lc     LOCAL                                                000000341072 000000341076 000000341472
TSPDFTNL_FVG                     EQU loc_fv_grnd                     +00004576 EW00001lc     LOCAL                                                000000341076 000000341080 000000341472
TSPDFTNU_FVG                     EQU loc_fv_grnd                     +00004580 EW00001lc     LOCAL                                                000000341080 000000341084 000000341472
TSW_FVG                          EQU loc_fv_grnd                     +00004584 IW00020lc     LOCAL                                                000000341084 000000341164 000000341472
TURNFTN_FVG                      EQU loc_fv_grnd                     +00004664 EW00001lc     LOCAL                                                000000341164 000000341168 000000341472
TX20HAC_FVG                      EQU loc_fv_grnd                     +00004668 EW00001lc     LOCAL                                                000000341168 000000341172 000000341472
TXAMP_FVG                        EQU loc_fv_grnd                     +00004672 EW00018lc     LOCAL                                                000000341172 000000341244 000000341472
TY20HAC_FVG                      EQU loc_fv_grnd                     +00004744 EW00001lc     LOCAL                                                000000341244 000000341248 000000341472
TYAMP_FVG                        EQU loc_fv_grnd                     +00004748 EW00018lc     LOCAL                                                000000341248 000000341320 000000341472
TZ20HAC_FVG                      EQU loc_fv_grnd                     +00004820 EW00001lc     LOCAL                                                000000341320 000000341324 000000341472
TZAMP_FVG                        EQU loc_fv_grnd                     +00004824 EW00018lc     LOCAL                                                000000341324 000000341396 000000341472
TZAMPF_FVG                       EQU loc_fv_grnd                     +00004896 EW00018lc     LOCAL                                                000000341396 000000341468 000000341472
TZSPD_FVG                        EQU loc_fv_grnd                     +00004968 EW00001lc     LOCAL
TWINDK_FVA                       EQU loc_fv_aero                     +00004536 EW00001       ground wind vibs gain                                0********816 0********820 0********820
TWAT_FVA_MAX                     EQU loc_fv_aero                     +00004540 EW00001       ground wind vibs max accel      
TXTDB4_FVAL_LIM                  EQU loc_fv_all                      +00000416 EW00001       Touch down bump limit                                0********824 0********828 0********828
TYTDB4_FVAL_LIM                  EQU loc_fv_all                      +00000420 EW00001       Touch down bump limit                                0********828 0********832 0********832
TZTDB4_FVAL_LIM                  EQU loc_fv_all                      +00000424 EW00001       Touch down bump limit        
TZTDB4_FVAL_LIM                  EQU loc_fv_all                      +00000424 EW00001       Touch down bump limit     
TVIBFNVEL                        EQU loc_fv_all                      +00000428 IW00001       vib gain for thrust vs vel                           0********488 0********492 0********492
PBSPDVARGN                       EQU loc_fv_all                      +00000432 EW00001       Pushback variable speed gain                         0********532 0********536 0********536
TPBSPDVAR                        EQU loc_fv_all                      +00000436 EW00001       Pushback variable speed                              0********536 0********540 0********540
TBZESTM1_FVAL                    EQU loc_fv_all                      +00000440 EW00001       Compressor stall accel                               0********540 0********544 0********544
TBZESTM2_FVAL                    EQU loc_fv_all                      +00000444 EW00001       Compressor stall accel                               0********544 0********548 0********548
TKESTM2_FVAL                     EQU loc_fv_all                      +00000448 EW00001       Compressor stall accel input                         0********548 0********552 0********552
TEBESTM1_FVAL                    EQU loc_fv_all                      +00000452 LB00001       Compressor stall n-1                                 0********552 0********553 0********553
TEBESTM2_FVAL                    EQU loc_fv_all                      +00000456 LB00001       Compressor stall n-1                                 0********553 0********554 0********554
FVENGREV                         EQU loc_fv_all                      +00000460 EW00001       Max of each eng reverser position   
TLLTX                            EQU                                 +00708224 EW00018       landing light vibs amplitude table                   0********112 0********184 0********184
TLLTY                            EQU                                 +00708296 EW00018       landing light vibs amplitude table                   0********184 0********256 0********256
TLLTZ                            EQU                                 +00708368 EW00018       landing light vibs amplitude table                   0********256 0********328 0********328
FBWFREQG                         EQU                                 +00708440 EW00020       vib freq table on ground                             0********328 0********408 0********408
FBWFREQA                         EQU                                 +00708520 EW00020       vib freq table in flight         
TGAMMAZ_LIM                      EQU loc_fv_aero                     +00004544 EW00001       Stall buffet gamma limit                             000000665056 000000665060 000000665060
TVIBBETA                         EQU loc_fv_aero                     +00004548 IW00001       Vibs table for side slip                             000000665060 000000665064 000000665064
TVIBREVINAIR                     EQU loc_fv_aero                     +00004552 IW00001       Vibs table for thrust reversers in air               000000665064 000000665068 000000665068
TVIBDOOR                         EQU loc_fv_aero                     +00004556 IW00001       Vibs table for door                                  000000665068 000000665072 000000665072
TVIBFLAP                         EQU loc_fv_aero                     +00004560 IW00001       Vibs table for flap                                  000000665072 000000665076 000000665076
TVIBGEAR                         EQU loc_fv_aero                     +00004564 IW00001       Vibs table for gear                                  000000665076 000000665080 000000665080
TVIBENG                          EQU loc_fv_aero                     +00004568 IW00004       Vibs table for engines    
TVIBENG                          EQU loc_fv_aero                     +00004568 IW00004       Vibs table for engines     
TSTLBUFX_FVA                     EQU loc_fv_aero                     +00004584 EW00001       stall buffet gain x                                  000000585060 000000585064 000000585064
TSTLBUFY_FVA                     EQU loc_fv_aero                     +00004588 EW00001       stall buffet gain y                                  000000585064 000000585068 000000585068
TSTLBUFZ_FVA                     EQU loc_fv_aero                     +00004592 EW00001       stall buffet gain z                                  000000585068 000000585072 000000585072
DFSTLBUFX                        EQU loc_fv_aero                     +00004596 IW00001       stall buffet gain x table                            000000585072 000000585076 000000585076
DFSTLBUFY                        EQU loc_fv_aero                     +00004600 IW00001       stall buffet gain y table                            000000585076 000000585080 000000585080
DFSTLBUFZ                        EQU loc_fv_aero                     +00004604 IW00001       stall buffet gain z table                            000000585080 000000585084 000000585084
THGEINIT_FAW                     EQU loc_fv_aero                     +00004608 EW00001       vertical height fade factor for turb                 000000585084 000000585088 000000585088
TZDDELMK_FVAL                    EQU loc_fv_aero                     +00004612 EW00001       gear touchdown bump gain for roc                     000000585088 000000585092 000000585092
TZDDELNK_FVAL                    EQU loc_fv_aero                     +00004616 EW00001       gear touchdown bump gain for roc   
FLAPPERC                         EQU loc_fv_aero                     +00004620 EW00001       Flap Position (Percent)        
tfc_air_defl                     EQU loc_fv_aero                     +00004624 IW00001       air deflector vib table  
rfv_air_defl_vib                 EQU loc_fv_aero                     +00004628 EW00001       air deflector vibs           
TBSCUFF_FVAL                     EQU loc_fv_aero                     +00004632 EW00001       scuffing accel                                       0********880 0********884 0********884
TKSCUFF_FVAL                     EQU loc_fv_aero                     +00004636 EW00001       scuffing accel init                                  0********884 0********888 0********888
TLSCUFF_FVAL                     EQU loc_fv_aero                     +00004640 LB00001       scuffing accel 30 htz toggle                         0********888 0********889 0********889
TXTDB4L_FVAL                     EQU loc_fv_aero                     +00004644 EW00001       left gear touch down bump accel                      0********892 0********896 0********896
TYTDB4L_FVAL                     EQU loc_fv_aero                     +00004648 EW00001       left gear touch down bump accel                      0********896 0********900 0********900
TZTDB4L_FVAL                     EQU loc_fv_aero                     +00004652 EW00001       left gear touch down bump accel                      0********900 0********904 0********904
TXTDB4R_FVAL                     EQU loc_fv_aero                     +00004656 EW00001       right gear touch down bump accel                     0********904 0********908 0********908
TYTDB4R_FVAL                     EQU loc_fv_aero                     +00004660 EW00001       right gear touch down bump accel                     0********908 0********912 0********912
TZTDB4R_FVAL                     EQU loc_fv_aero                     +00004664 EW00001       right gear touch down bump accel                     0********912 0********916 0********916
TXTDB4M_FVAL_LIM                 EQU loc_fv_aero                     +00004668 EW00001       main gear touch down bump accel                      0********916 0********920 0********920
TYTDB4M_FVAL_LIM                 EQU loc_fv_aero                     +00004672 EW00001       main gear touch down bump accel                      0********920 0********924 0********924
TZTDB4M_FVAL_LIM                 EQU loc_fv_aero                     +00004676 EW00001       main gear touch down bump accel  
TKNACCELI_FVAL                   EQU loc_fv_aero                     +00004680 EW00001       TD bump accel due to strut preload     
TKMACCELI_FVAL                   EQU loc_fv_aero                     +00004684 EW00001       TD bump accel due to strut preload      
TKENGV_FVE                       EQU loc_fv_aero                     +00004688 EW00001       severe eng damage residual speed gain k  
TENGV_FVE                        EQU loc_fv_aero                     +00004692 EW00001       severe eng damage residual speed gain      
XLLAUXPTT                        EQU LTGBASE                         +00000223 LB00001       PUSH TO TEST - LEFT AUX NO TRANS ANNUN
XLRAUXPTT                        EQU LTGBASE                         +00000224 LB00001       PUSH TO TEST - RIGHT AUX NO TRANS ANNUN
reg_prp_spd                      EQU loc_fv_eng                      +00001208 EW00004lc     Eng prop spd
TVIBFNN1                         EQU loc_fv_eng                      +00001224 IW00004       vib gain for thrust vs n1       
rfc_air_defl_norm                EQU loc_fv_eng                      +00001240 EW00001       air deflector normalized
TLLIGHTV                         EQU loc_fv_all                      +00000464 IW00001       landing light vibs gain table      
MBPACDO                          EQU X_MBB1                          +00000659 LB00001P8     AFT CARGO DOOR OPEN-DEPRESS       
lft_boom_contact                 EQU loc_fv_all                      +00000468 LB00001       boom contact flag                     
lfv_boom_contact_prev            EQU loc_fv_all                      +00000469 LB00001       boom contact flag previous             
rfv_boom_contact_bump            EQU loc_fv_all                      +00000472 EW00001       boom contact bump          
rfv_boom_contact_gain            EQU loc_fv_all                      +00000476 EW00001       boom contact bump gain       
TKEDMTOT_MAX                     EQU loc_fv_eng                      +00001244 EW00001       severe eng damage vib amplitude max   
RFMBRXSIMAX                      EQU FMBUFRXG                        +00000164 EW00001       Calculated actual sim x displacement 
RFMBRXSIMAY                      EQU FMBUFRXG                        +00000168 EW00001       Calculated actual sim y displacement 
RFMBRXSIMAZ                      EQU FMBUFRXG                        +00000172 EW00001       Calculated actual sim z displacement 
RFMBRXSIMAR                      EQU FMBUFRXG                        +00000176 EW00001       Calculated actual sim roll angle
RFMBRXSIMAP                      EQU FMBUFRXG                        +00000180 EW00001       Calculated actual sim pitch angle
RFMBRXSIMAYW                     EQU FMBUFRXG                        +00000184 EW00001       Calculated actual sim yaw angle

RFCCMELEVK                       EQU                                 +00708600 EW00002       CM Elev Factor
TFCCMELEVK                       EQU                                 +00708608 IW00002       LFI TABLE
XCANNDIM                        -EQU LTGBASE                         +00000184 EW00001       ANN DIMMER INTERMEDIATE
XCANNDIM                         EQU LTGBASE                         +00000225 EW00001       ANN DIMMER INTERMEDIATE

LCB124CB14                       EQU LCBCB                           +00000167 LB00001       L OIL TEMP IND CB
LCB124CB15                       EQU LCBCB                           +00000168 LB00001       L OIL PRES IND CB
LCB124CB16                       EQU LCBCB                           +00000169 LB00001       L FUEL FLOW IND CB
LCB124CB17                       EQU LCBCB                           +00000170 LB00001       L TURB TACH IND CB
LCB124CB18                       EQU LCBCB                           +00000171 LB00001       L PROP TACH IND CB
LCB124CB19                       EQU LCBCB                           +00000172 LB00001       L TORQU MTR IND CB
LCB124CB20                       EQU LCBCB                           +00000173 LB00001       L ITT IND CB
LCB124CB29                       EQU LCBCB                           +00000174 LB00001       R OIL TEMP IND CB
LCB124CB30                       EQU LCBCB                           +00000175 LB00001       R OIL PRES IND CB
LCB124CB31                       EQU LCBCB                           +00000176 LB00001       R FUEL FLOW IND CB
LCB124CB32                       EQU LCBCB                           +00000177 LB00001       R TURB TACH IND CB
LCB124CB33                       EQU LCBCB                           +00000178 LB00001       R PROP TACH IND CB
LCB124CB34                       EQU LCBCB                           +00000179 LB00001       R TORQU MTR IND CB
LCB124CB35                       EQU LCBCB                           +00000180 LB00001       R ITT IND CB
LCB145CB200                      EQU LCBCB                           +00000181 LB00001       L GEN LOAD METER CB C+
LCB145CB201                      EQU LCBCB                           +00000182 LB00001       L GEN LOAD METER CB A-
LCB145CB202                      EQU LCBCB                           +00000183 LB00001       R GEN LOAD METER CB C+
LCB145CB203                      EQU LCBCB                           +00000184 LB00001       R GEN LOAD METER CB A-
XCB124CB14                       EQU XCBCB                           +00000167 LB00001       L OIL TEMP IND CB
XCB124CB15                       EQU XCBCB                           +00000168 LB00001       L OIL PRES IND CB
XCB124CB16                       EQU XCBCB                           +00000169 LB00001       L FUEL FLOW IND CB
XCB124CB17                       EQU XCBCB                           +00000170 LB00001       L TURB TACH IND CB
XCB124CB18                       EQU XCBCB                           +00000171 LB00001       L PROP TACH IND CB
XCB124CB19                       EQU XCBCB                           +00000172 LB00001       L TORQU MTR IND CB
XCB124CB20                       EQU XCBCB                           +00000173 LB00001       L ITT IND CB
XCB124CB29                       EQU XCBCB                           +00000174 LB00001       R OIL TEMP IND CB
XCB124CB30                       EQU XCBCB                           +00000175 LB00001       R OIL PRES IND CB
XCB124CB31                       EQU XCBCB                           +00000176 LB00001       R FUEL FLOW IND CB
XCB124CB32                       EQU XCBCB                           +00000177 LB00001       R TURB TACH IND CB
XCB124CB33                       EQU XCBCB                           +00000178 LB00001       R PROP TACH IND CB
XCB124CB34                       EQU XCBCB                           +00000179 LB00001       R TORQU MTR IND CB
XCB124CB35                       EQU XCBCB                           +00000180 LB00001       R ITT IND CB
XCB145CB200                      EQU XCBCB                           +00000181 LB00001       L GEN LOAD METER CB C+
XCB145CB201                      EQU XCBCB                           +00000182 LB00001       L GEN LOAD METER CB A-
XCB145CB202                      EQU XCBCB                           +00000183 LB00001       R GEN LOAD METER CB C+
XCB145CB203                      EQU XCBCB                           +00000184 LB00001       R GEN LOAD METER CB A-
XLB224S121BRT                    EQU LBBUS                           +00000056 LB00001       CABIN LIGHTS SW: BRIGHT
XLB224S121DIM                    EQU LBBUS                           +00000057 LB00001       CABIN LIGHTS SW: DIM
XLB224S121OFF                    EQU LBBUS                           +00000058 LB00001       CABIN LIGHTS SW: OFF
XLB224S180NSMK                   EQU LBBUS                           +00000059 LB00001       ORDINANCE SIGN SW: FSB + NOSMK
XLB224S180OFF                    EQU LBBUS                           +00000060 LB00001       ORDINANCE SIGN SW: OFF
XLB224S180FSB                    EQU LBBUS                           +00000061 LB00001       ORDINANCE SIGN SW: FSB ONLY
XLB136S150                       EQU LBBUS                           +00000062 LB00001       PROP SYNC CONTROL SW
XLB223S173                       EQU LBBUS                           +00000063 LB00001       LEFT FUEL VENT HEAT SW
XLB223S174                       EQU LBBUS                           +00000064 LB00001       RIGHT FUEL VENT HEAT SW
XLB221S162                       EQU LBBUS                           +00000065 LB00001       L ENG ANTI-ICE ON/OFF SW
XLB221S163                       EQU LBBUS                           +00000066 LB00001       R ENG ANTI-ICE ON/OFF SW
XLB221S263                       EQU LBBUS                           +00000067 LB00001       L ENG ANTI-ICE MAIN/STBY SW
XLB221S264                       EQU LBBUS                           +00000068 LB00001       R ENG ANTI-ICE MAIN/STBY SW
XLB245S2                         EQU LBBUS                           +00000069 LB00001       RUDDER BOOST SW
XLB224S300A                      EQU LBBUS                           +00000070 LB00001       FURN MSTR CONT SW: FURN ON
XLB224S300B                      EQU LBBUS                           +00000071 LB00001       FURN MSTR CONT SW: COFFEE OFF
XLB221S147TST                    EQU LBBUS                           +00000072 LB00001       PROP OVERSPEED GOV TEST SW
XCB124CB14                      -EQU XCBCB                           +00000167 LB00001       L OIL TEMP IND CB
XCB124CB15                      -EQU XCBCB                           +00000168 LB00001       L OIL PRES IND CB
XCB124CB16                      -EQU XCBCB                           +00000169 LB00001       L FUEL FLOW IND CB
XCB124CB17                      -EQU XCBCB                           +00000170 LB00001       L TURB TACH IND CB
XCB124CB18                      -EQU XCBCB                           +00000171 LB00001       L PROP TACH IND CB
XCB124CB19                      -EQU XCBCB                           +00000172 LB00001       L TORQU MTR IND CB
XCB124CB20                      -EQU XCBCB                           +00000173 LB00001       L ITT IND CB
XCB124CB29                      -EQU XCBCB                           +00000174 LB00001       R OIL TEMP IND CB
XCB124CB30                      -EQU XCBCB                           +00000175 LB00001       R OIL PRES IND CB
XCB124CB31                      -EQU XCBCB                           +00000176 LB00001       R FUEL FLOW IND CB
XCB124CB32                      -EQU XCBCB                           +00000177 LB00001       R TURB TACH IND CB
XCB124CB33                      -EQU XCBCB                           +00000178 LB00001       R PROP TACH IND CB
XCB124CB34                      -EQU XCBCB                           +00000179 LB00001       R TORQU MTR IND CB
XCB124CB35                      -EQU XCBCB                           +00000180 LB00001       R ITT IND CB
XCB145CB200                     -EQU XCBCB                           +00000181 LB00001       L GEN LOAD METER CB C+
XCB145CB201                     -EQU XCBCB                           +00000182 LB00001       L GEN LOAD METER CB A-
XCB145CB202                     -EQU XCBCB                           +00000183 LB00001       R GEN LOAD METER CB C+
XCB145CB203                     -EQU XCBCB                           +00000184 LB00001       R GEN LOAD METER CB A-
LCBAC26CBAP                      EQU LCBCB                           +00000166 LB00001       26 VAC AUTOPILOT CB
LCBAC115CBVGY                    EQU LCBCB                           +00000167 LB00001       115 VAC Vertical Gyro CB
LCB124CB14                       EQU LCBCB                           +00000168 LB00001       L OIL TEMP IND CB
LCB124CB15                       EQU LCBCB                           +00000169 LB00001       L OIL PRES IND CB
LCB124CB16                       EQU LCBCB                           +00000170 LB00001       L FUEL FLOW IND CB
LCB124CB17                       EQU LCBCB                           +00000171 LB00001       L TURB TACH IND CB
LCB124CB18                       EQU LCBCB                           +00000172 LB00001       L PROP TACH IND CB
LCB124CB19                       EQU LCBCB                           +00000173 LB00001       L TORQU MTR IND CB
LCB124CB20                       EQU LCBCB                           +00000174 LB00001       L ITT IND CB
LCB124CB29                       EQU LCBCB                           +00000175 LB00001       R OIL TEMP IND CB
LCB124CB30                       EQU LCBCB                           +00000176 LB00001       R OIL PRES IND CB
LCB124CB31                       EQU LCBCB                           +00000177 LB00001       R FUEL FLOW IND CB
LCB124CB32                       EQU LCBCB                           +00000178 LB00001       R TURB TACH IND CB
LCB124CB33                       EQU LCBCB                           +00000179 LB00001       R PROP TACH IND CB
LCB124CB34                       EQU LCBCB                           +00000180 LB00001       R TORQU MTR IND CB
LCB124CB35                       EQU LCBCB                           +00000181 LB00001       R ITT IND CB
LCB145CB200                      EQU LCBCB                           +00000182 LB00001       L GEN LOAD METER CB C+
LCB145CB201                      EQU LCBCB                           +00000183 LB00001       L GEN LOAD METER CB A-
LCB145CB202                      EQU LCBCB                           +00000184 LB00001       R GEN LOAD METER CB C+
LCB145CB203                      EQU LCBCB                           +00000185 LB00001       R GEN LOAD METER CB A-
LCBAC26CBAP                     -EQU LCBCB                           +00000165 LB00001       26 VAC AUTOPILOT CB
LCBAC115CBVGY                   -EQU LCBCB                           +00000166 LB00001       115 VAC Vertical Gyro CB
LCB124CB14                      -EQU LCBCB                           +00000167 LB00001       L OIL TEMP IND CB
LCB124CB15                      -EQU LCBCB                           +00000168 LB00001       L OIL PRES IND CB
LCB124CB16                      -EQU LCBCB                           +00000169 LB00001       L FUEL FLOW IND CB
LCB124CB17                      -EQU LCBCB                           +00000170 LB00001       L TURB TACH IND CB
LCB124CB18                      -EQU LCBCB                           +00000171 LB00001       L PROP TACH IND CB
LCB124CB19                      -EQU LCBCB                           +00000172 LB00001       L TORQU MTR IND CB
LCB124CB20                      -EQU LCBCB                           +00000173 LB00001       L ITT IND CB
LCB124CB29                      -EQU LCBCB                           +00000174 LB00001       R OIL TEMP IND CB
LCB124CB30                      -EQU LCBCB                           +00000175 LB00001       R OIL PRES IND CB
LCB124CB31                      -EQU LCBCB                           +00000176 LB00001       R FUEL FLOW IND CB
LCB124CB32                      -EQU LCBCB                           +00000177 LB00001       R TURB TACH IND CB
LCB124CB33                      -EQU LCBCB                           +00000178 LB00001       R PROP TACH IND CB
LCB124CB34                      -EQU LCBCB                           +00000179 LB00001       R TORQU MTR IND CB
LCB124CB35                      -EQU LCBCB                           +00000180 LB00001       R ITT IND CB
LCB145CB200                     -EQU LCBCB                           +00000181 LB00001       L GEN LOAD METER CB C+
LCB145CB201                     -EQU LCBCB                           +00000182 LB00001       L GEN LOAD METER CB A-
LCB145CB202                     -EQU LCBCB                           +00000183 LB00001       R GEN LOAD METER CB C+
LCB145CB203                     -EQU LCBCB                           +00000184 LB00001       R GEN LOAD METER CB A-
LCBAC26CBAP                      EQU LCBCB                           +00000166 LB00001       26 VAC AUTOPILOT CB
LCBAC115CBVGY                    EQU LCBCB                           +00000167 LB00001       115 VAC Vertical Gyro CB
LCB124CB14                       EQU LCBCB                           +00000168 LB00001       L OIL TEMP IND CB
LCB124CB15                       EQU LCBCB                           +00000169 LB00001       L OIL PRES IND CB
LCB124CB16                       EQU LCBCB                           +00000170 LB00001       L FUEL FLOW IND CB
LCB124CB17                       EQU LCBCB                           +00000171 LB00001       L TURB TACH IND CB
LCB124CB18                       EQU LCBCB                           +00000172 LB00001       L PROP TACH IND CB
LCB124CB19                       EQU LCBCB                           +00000173 LB00001       L TORQU MTR IND CB
LCB124CB20                       EQU LCBCB                           +00000174 LB00001       L ITT IND CB
LCB124CB29                       EQU LCBCB                           +00000175 LB00001       R OIL TEMP IND CB
LCB124CB30                       EQU LCBCB                           +00000176 LB00001       R OIL PRES IND CB
LCB124CB31                       EQU LCBCB                           +00000177 LB00001       R FUEL FLOW IND CB
LCB124CB32                       EQU LCBCB                           +00000178 LB00001       R TURB TACH IND CB
LCB124CB33                       EQU LCBCB                           +00000179 LB00001       R PROP TACH IND CB
LCB124CB34                       EQU LCBCB                           +00000180 LB00001       R TORQU MTR IND CB
LCB124CB35                       EQU LCBCB                           +00000181 LB00001       R ITT IND CB
LCB145CB200                      EQU LCBCB                           +00000182 LB00001       L GEN LOAD METER CB C+
LCB145CB201                      EQU LCBCB                           +00000183 LB00001       L GEN LOAD METER CB A-
LCB145CB202                      EQU LCBCB                           +00000184 LB00001       R GEN LOAD METER CB C+
LCB145CB203                      EQU LCBCB                           +00000185 LB00001       R GEN LOAD METER CB A-
XLB124CB10                      -EQU XCBCB                           +00000161 LB00001       POP LEFT AUX TRANSFER CB
XLB124CB11                      -EQU XCBCB                           +00000162 LB00001       POP LEFT STANDBY PUMP CB
XLB124CB3                       -EQU XCBCB                           +00000163 LB00001       POP RIGHT STANDBY PUMP CB
XLB124CB4                       -EQU XCBCB                           +00000164 LB00001       POP RIGHT AUX TRANSFER CB
XLB124CB7                       -EQU XCBCB                           +00000165 LB00001       POP CROSS FEED CB
XLB124CB25                      -EQU XCBCB                           +00000166 LB00001       POP LEFT FLAPS CONTROL CB
XLB124CB27                      -EQU XCBCB                           +00000167 LB00001       POP LEFT PROP DEICE CB
XLB146CB108                     -EQU XCBCB                           +00000168 LB00001       POP LANDING GEAR WARN CB
XLB146CB142                     -EQU XCBCB                           +00000169 LB00001       POP RUDDER BOOST CB
XLB146CB145                     -EQU XCBCB                           +00000170 LB00001       POP STALL WARN CB
XLB146CB166                     -EQU XCBCB                           +00000171 LB00001       POP PITCH TRIM CB
XLB146CB169                     -EQU XCBCB                           +00000172 LB00001       POP AVIONICS MASTER CB
XLB146CB170                     -EQU XCBCB                           +00000173 LB00001       POP AVIONICS NO1 CB
XLB146CB171                     -EQU XCBCB                           +00000174 LB00001       POP AVIONICS NO2 CB
XLB146CBADF                     -EQU XCBCB                           +00000175 LB00001       POP ADF CB
XLB146CBALTAIRD                 -EQU XCBCB                           +00000176 LB00001       POP PILOTS ALTIMETER-AIR DATA CB
XLB146CBARLWRN                  -EQU XCBCB                           +00000177 LB00001       POP ALTITUDE ALERTER CB
XLB146CBCENCDALT                -EQU XCBCB                           +00000178 LB00001       POP COPILOT ALTIMETER CB
XLB146CBCMPS1                   -EQU XCBCB                           +00000179 LB00001       POP COMPASS NO1 CB
XLB146CBCMPS2                   -EQU XCBCB                           +00000180 LB00001       POP COMPASS NO2 CB
XLB146CBCOMM1                   -EQU XCBCB                           +00000181 LB00001       POP COMM NO1 CB
XLB146CBCOMM2                   -EQU XCBCB                           +00000182 LB00001       POP COMM NO2 CB
XLB146CBDME1                    -EQU XCBCB                           +00000183 LB00001       POP DME NO1 CB
XLB146CBDME2                    -EQU XCBCB                           +00000184 LB00001       POP DME NO2 CB
XLB146CBDSP                     -EQU XCBCB                           +00000185 LB00001       POP DSP CB
XLB146CBEADI                    -EQU XCBCB                           +00000186 LB00001       POP EADI CB
XLB146CBFCSPWR                  -EQU XCBCB                           +00000187 LB00001       POP FCS POWER CB
XLB146CBEHSI                    -EQU XCBCB                           +00000188 LB00001       POP EHSI CB
XLB146CBNAV1                    -EQU XCBCB                           +00000189 LB00001       POP NAV NO1 CB
XLB146CBNAV2                    -EQU XCBCB                           +00000190 LB00001       POP NAV NO2 CB
XLB146CBRMI1                    -EQU XCBCB                           +00000191 LB00001       POP RMI NO1 CB
XLB146CBRMI2                    -EQU XCBCB                           +00000192 LB00001       POP RMI NO2 CB
XLBCB                            EQU DP                              +00037760 LB00050       B200 CB POPS
XLB124CB10                       EQU XLBCB                           +0000000 LB00001       POP LEFT AUX TRANSFER CB
XLB124CB11                       EQU XLBCB                           +0000001 LB00001       POP LEFT STANDBY PUMP CB
XLB124CB3                        EQU XLBCB                           +0000002 LB00001       POP RIGHT STANDBY PUMP CB
XLB124CB4                        EQU XLBCB                           +0000003 LB00001       POP RIGHT AUX TRANSFER CB
XLB124CB7                        EQU XLBCB                           +0000004 LB00001       POP CROSS FEED CB
XLB124CB25                       EQU XLBCB                           +0000005 LB00001       POP LEFT FLAPS CONTROL CB
XLB124CB27                       EQU XLBCB                           +0000006 LB00001       POP LEFT PROP DEICE CB
XLB146CB108                      EQU XLBCB                           +0000007 LB00001       POP LANDING GEAR WARN CB
XLB146CB142                      EQU XLBCB                           +0000008 LB00001       POP RUDDER BOOST CB
XLB146CB145                      EQU XLBCB                           +0000009 LB00001       POP STALL WARN CB
XLB146CB166                      EQU XLBCB                           +0000010 LB00001       POP PITCH TRIM CB
XLB146CB169                      EQU XLBCB                           +0000011 LB00001       POP AVIONICS MASTER CB
XLB146CB170                      EQU XLBCB                           +0000012 LB00001       POP AVIONICS NO1 CB
XLB146CB171                      EQU XLBCB                           +0000013 LB00001       POP AVIONICS NO2 CB
XLB146CBADF                      EQU XLBCB                           +0000014 LB00001       POP ADF CB
XLB146CBALTAIRD                  EQU XLBCB                           +0000015 LB00001       POP PILOTS ALTIMETER-AIR DATA CB
XLB146CBARLWRN                   EQU XLBCB                           +0000016 LB00001       POP ALTITUDE ALERTER CB
XLB146CBCENCDALT                 EQU XLBCB                           +0000017 LB00001       POP COPILOT ALTIMETER CB
XLB146CBCMPS1                    EQU XLBCB                           +0000018 LB00001       POP COMPASS NO1 CB
XLB146CBCMPS2                    EQU XLBCB                           +0000019 LB00001       POP COMPASS NO2 CB
XLB146CBCOMM1                    EQU XLBCB                           +0000020 LB00001       POP COMM NO1 CB
XLB146CBCOMM2                    EQU XLBCB                           +0000021 LB00001       POP COMM NO2 CB
XLB146CBDME1                     EQU XLBCB                           +0000022 LB00001       POP DME NO1 CB
XLB146CBDME2                     EQU XLBCB                           +0000023 LB00001       POP DME NO2 CB
XLB146CBDSP                      EQU XLBCB                           +0000024 LB00001       POP DSP CB
XLB146CBEADI                     EQU XLBCB                           +0000025 LB00001       POP EADI CB
XLB146CBFCSPWR                   EQU XLBCB                           +0000026 LB00001       POP FCS POWER CB
XLB146CBEHSI                     EQU XLBCB                           +0000027 LB00001       POP EHSI CB
XLB146CBNAV1                     EQU XLBCB                           +0000028 LB00001       POP NAV NO1 CB
XLB146CBNAV2                     EQU XLBCB                           +0000029 LB00001       POP NAV NO2 CB
XLB146CBRMI1                     EQU XLBCB                           +0000030 LB00001       POP RMI NO1 CB
XLB146CBRMI2                     EQU XLBCB                           +0000031 LB00001       POP RMI NO2 CB
XLB124CB10                      -EQU XLBCB                           +******** B000001      POP LEFT AUX TRANSFER CB
XCB124CB14                       EQU XCBCB                           +00000161 LB00001       L OIL TEMP IND CB
XCB124CB15                       EQU XCBCB                           +00000162 LB00001       L OIL PRES IND CB
XCB124CB16                       EQU XCBCB                           +00000163 LB00001       L FUEL FLOW IND CB
XCB124CB17                       EQU XCBCB                           +00000164 LB00001       L TURB TACH IND CB
XCB124CB18                       EQU XCBCB                           +00000165 LB00001       L PROP TACH IND CB
XCB124CB19                       EQU XCBCB                           +00000166 LB00001       L TORQU MTR IND CB
XCB124CB20                       EQU XCBCB                           +00000167 LB00001       L ITT IND CB
XCB124CB29                       EQU XCBCB                           +00000168 LB00001       R OIL TEMP IND CB
XCB124CB30                       EQU XCBCB                           +00000169 LB00001       R OIL PRES IND CB
XCB124CB31                       EQU XCBCB                           +00000170 LB00001       R FUEL FLOW IND CB
XCB124CB32                       EQU XCBCB                           +00000171 LB00001       R TURB TACH IND CB
XCB124CB33                       EQU XCBCB                           +00000172 LB00001       R PROP TACH IND CB
XCB124CB34                       EQU XCBCB                           +00000173 LB00001       R TORQU MTR IND CB
XCB124CB35                       EQU XCBCB                           +00000174 LB00001       R ITT IND CB
XLB124CB11                      +EQU XLBCB                           +00000001 B000001V      POP LEFT STANDBY PUMP CB
XLB124CB3                       +EQU XLBCB                           +00000002 B000001       POP RIGHT STANDBY PUMP CB
XLB124CB4                       +EQU XLBCB                           +00000003 B000001       POP RIGHT AUX TRANSFER CB
XLB124CB7                       +EQU XLBCB                           +00000004 B000001       POP CROSS FEED CB
XLB124CB25                      +EQU XLBCB                           +00000005 B000001       POP LEFT FLAPS CONTROL CB
XLB124CB27                      +EQU XLBCB                           +00000006 B000001       POP LEFT PROP DEICE CB
XLB146CB108                     +EQU XLBCB                           +00000007 B000001       POP LANDING GEAR WARN CB
XLB146CB142                     +EQU XLBCB                           +00000008 B000001       POP RUDDER BOOST CB
XLB146CB145                     +EQU XLBCB                           +00000009 B000001       POP STALL WARN CB
XLB146CB166                     +EQU XLBCB                           +00000010 B000001       POP PITCH TRIM CB
XLB146CB169                     +EQU XLBCB                           +00000011 B000001       POP AVIONICS MASTER CB
XLB146CB170                     +EQU XLBCB                           +00000012 B000001       POP AVIONICS NO1 CB
XLB146CB171                     +EQU XLBCB                           +00000013 B000001       POP AVIONICS NO2 CB
XLB146CBADF                     +EQU XLBCB                           +00000014 B000001       POP ADF CB
XLB146CBALTAIRD                 +EQU XLBCB                           +00000015 B000001       POP PILOTS ALTIMETER-AIR DATA CB
XLB146CBARLWRN                  +EQU XLBCB                           +00000016 B000001       POP ALTITUDE ALERTER CB
XLB146CBCENCDALT                +EQU XLBCB                           +00000017 B000001       POP COPILOT ALTIMETER CB
XLB146CBCMPS1                   +EQU XLBCB                           +00000018 B000001       POP COMPASS NO1 CB
XLB146CBCMPS2                   +EQU XLBCB                           +00000019 B000001       POP COMPASS NO2 CB
XLB146CBCOMM1                   +EQU XLBCB                           +00000020 B000001       POP COMM NO1 CB
XLB146CBCOMM2                   +EQU XLBCB                           +00000021 B000001       POP COMM NO2 CB
XLB146CBDME1                    +EQU XLBCB                           +00000022 B000001       POP DME NO1 CB
XLB146CBDME2                    +EQU XLBCB                           +00000023 B000001       POP DME NO2 CB
XLB146CBDSP                     +EQU XLBCB                           +00000024 B000001       POP DSP CB
XLB146CBEADI                    +EQU XLBCB                           +00000025 B000001       POP EADI CB
XLB146CBFCSPWR                  +EQU XLBCB                           +00000026 B000001       POP FCS POWER CB
XLB146CBEHSI                    +EQU XLBCB                           +00000027 B000001       POP EHSI CB
XLB146CBNAV1                    +EQU XLBCB                           +00000028 B000001       POP NAV NO1 CB
XLB146CBNAV2                    +EQU XLBCB                           +00000029 B000001       POP NAV NO2 CB
XLB146CBRMI1                    +EQU XLBCB                           +00000030 B000001       POP RMI NO1 CB
XLB146CBRMI2                    +EQU XLBCB                           +00000031 B000001       POP RMI NO2 CB
XLB124CB10                       EQU XLBCB                           +******** B000001       POP LEFT AUX TRANSFER CB
XLB124CB11                       EQU XLBCB                           +00000001 B000001       POP LEFT STANDBY PUMP CB
XLB124CB11                      +EQU XLBCB                           +00000001 B000001       POP LEFT STANDBY PUMP CB
XLB124CB10                      +EQU XLBCB                           +******** LB00001       POP LEFT AUX TRANSFER CB
XLB124CB11                      +EQU XLBCB                           +00000001 LB00001       POP LEFT STANDBY PUMP CB
XLB124CB3                       +EQU XLBCB                           +00000002 LB00001       POP RIGHT STANDBY PUMP CB
XLB124CB4                       +EQU XLBCB                           +00000003 LB00001       POP RIGHT AUX TRANSFER CB
XLB124CB7                       +EQU XLBCB                           +00000004 LB00001       POP CROSS FEED CB
XLB124CB25                      +EQU XLBCB                           +00000005 LB00001       POP LEFT FLAPS CONTROL CB
XLB124CB27                      +EQU XLBCB                           +00000006 LB00001       POP LEFT PROP DEICE CB
XLB146CB108                     +EQU XLBCB                           +00000007 LB00001       POP LANDING GEAR WARN CB
XLB146CB142                     +EQU XLBCB                           +00000008 LB00001       POP RUDDER BOOST CB
XLB146CB145                     +EQU XLBCB                           +00000009 LB00001       POP STALL WARN CB
XLB146CB166                     +EQU XLBCB                           +00000010 LB00001       POP PITCH TRIM CB
XLB146CB169                     +EQU XLBCB                           +00000011 LB00001       POP AVIONICS MASTER CB
XLB146CB170                     +EQU XLBCB                           +00000012 LB00001       POP AVIONICS NO1 CB
XLB146CB171                     +EQU XLBCB                           +00000013 LB00001       POP AVIONICS NO2 CB
XLB146CBADF                     +EQU XLBCB                           +00000014 LB00001       POP ADF CB
XLB146CBALTAIRD                 +EQU XLBCB                           +00000015 LB00001       POP PILOTS ALTIMETER-AIR DATA CB
XLB146CBARLWRN                  +EQU XLBCB                           +00000016 LB00001       POP ALTITUDE ALERTER CB
XLB146CBCENCDALT                +EQU XLBCB                           +00000017 LB00001       POP COPILOT ALTIMETER CB
XLB146CBCMPS1                   +EQU XLBCB                           +00000018 LB00001       POP COMPASS NO1 CB
XLB146CBCMPS2                   +EQU XLBCB                           +00000019 LB00001       POP COMPASS NO2 CB
XLB146CBCOMM1                   +EQU XLBCB                           +00000020 LB00001       POP COMM NO1 CB
XLB146CBCOMM2                   +EQU XLBCB                           +00000021 B000001       POP COMM NO2 CB
XLB146CBDME1                    +EQU XLBCB                           +00000022 B000001       POP DME NO1 CB
XLB146CBDME2                    +EQU XLBCB                           +00000023 LB00001       POP DME NO2 CB
XLB146CBDSP                     +EQU XLBCB                           +00000024 LB00001       POP DSP CB
XLB146CBEADI                    +EQU XLBCB                           +00000025 LB00001       POP EADI CB
XLB146CBFCSPWR                  +EQU XLBCB                           +00000026 LB00001       POP FCS POWER CB
XLB146CBEHSI                    +EQU XLBCB                           +00000027 LB00001       POP EHSI CB
XLB146CBNAV1                    +EQU XLBCB                           +00000028 LB00001       POP NAV NO1 CB
XLB146CBNAV2                    +EQU XLBCB                           +00000029 LB00001       POP NAV NO2 CB
XLB146CBRMI1                    +EQU XLBCB                           +00000030 LB00001       POP RMI NO1 CB
XLB146CBRMI2                    +EQU XLBCB                           +00000031 LB00001       POP RMI NO2 CB
XLB124CB10                      -EQU XLBCB                           +******** LB00001       POP LEFT AUX TRANSFER CB
XLB124CB10                       EQU XLBCB                           +******** LB00001       POP LEFT AUX TRANSFER CB
XLB124CB10                      +EQU XLBCB                           +00000001 LB00001       POP LEFT AUX TRANSFER CB                             000000037760 000000037761 000000037761
XLB124CB11                      +EQU XLBCB                           +00000002 LB00001       POP LEFT STANDBY PUMP CB                             000000037761 000000037762 000000037810
XLB124CB3                       +EQU XLBCB                           +00000003 LB00001       POP RIGHT STANDBY PUMP CB                            000000037762 000000037763 000000037810
XLB124CB4                       +EQU XLBCB                           +00000004 LB00001       POP RIGHT AUX TRANSFER CB                            000000037763 000000037764 000000037810
XLB124CB7                       +EQU XLBCB                           +00000005 LB00001       POP CROSS FEED CB                                    000000037764 000000037765 000000037810
XLB124CB25                      +EQU XLBCB                           +00000006 LB00001       POP LEFT FLAPS CONTROL CB                            000000037765 000000037766 000000037810
XLB124CB27                      +EQU XLBCB                           +00000007 LB00001       POP LEFT PROP DEICE CB                               000000037766 000000037767 000000037810
XLB146CB108                     +EQU XLBCB                           +00000008 LB00001       POP LANDING GEAR WARN CB                             000000037767 000000037768 000000037810
XLB146CB142                     +EQU XLBCB                           +00000009 LB00001       POP RUDDER BOOST CB                                  000000037768 000000037769 000000037810
XLB146CB145                     +EQU XLBCB                           +00000010 LB00001       POP STALL WARN CB                                    000000037769 000000037770 000000037810
XLB146CB166                     +EQU XLBCB                           +00000011 LB00001       POP PITCH TRIM CB                                    000000037770 000000037771 000000037810
XLB146CB169                     +EQU XLBCB                           +00000012 LB00001       POP AVIONICS MASTER CB                               000000037771 000000037772 000000037810
XLB146CB170                     +EQU XLBCB                           +00000013 LB00001       POP AVIONICS NO1 CB                                  000000037772 000000037773 000000037810
XLB146CB171                     +EQU XLBCB                           +00000014 LB00001       POP AVIONICS NO2 CB                                  000000037773 000000037774 000000037810
XLB146CBADF                     +EQU XLBCB                           +00000015 LB00001       POP ADF CB                                           000000037774 000000037775 000000037810
XLB146CBALTAIRD                 +EQU XLBCB                           +00000016 LB00001       POP PILOTS ALTIMETER-AIR DATA CB                     000000037775 000000037776 000000037810
XLB146CBARLWRN                  +EQU XLBCB                           +00000017 LB00001       POP ALTITUDE ALERTER CB                              000000037776 000000037777 000000037810
XLB146CBCENCDALT                +EQU XLBCB                           +00000018 LB00001       POP COPILOT ALTIMETER CB                             000000037777 000000037778 000000037810
XLB146CBCMPS1                   +EQU XLBCB                           +00000019 LB00001       POP COMPASS NO1 CB                                   000000037778 000000037779 000000037810
XLB146CBCMPS2                   +EQU XLBCB                           +00000020 LB00001       POP COMPASS NO2 CB                                   000000037779 000000037780 000000037810
XLB146CBCOMM1                   +EQU XLBCB                           +00000021 LB00001       POP COMM NO1 CB                                      000000037780 000000037781 000000037810
XLB146CBCOMM2                   +EQU XLBCB                           +00000022 LB00001       POP COMM NO2 CB                                      000000037781 000000037782 000000037810
XLB146CBDME1                    +EQU XLBCB                           +00000023 LB00001       POP DME NO1 CB                                       000000037782 000000037783 000000037810
XLB146CBDME2                    +EQU XLBCB                           +00000024 LB00001       POP DME NO2 CB                                       000000037783 000000037784 000000037810
XLB146CBDSP                     +EQU XLBCB                           +00000025 LB00001       POP DSP CB                                           000000037784 000000037785 000000037810
XLB146CBEADI                    +EQU XLBCB                           +00000026 LB00001       POP EADI CB                                          000000037785 000000037786 000000037810
XLB146CBFCSPWR                  +EQU XLBCB                           +00000027 LB00001       POP FCS POWER CB                                     000000037786 000000037787 000000037810
XLB146CBEHSI                    +EQU XLBCB                           +00000028 LB00001       POP EHSI CB                                          000000037787 000000037788 000000037810
XLB146CBNAV1                    +EQU XLBCB                           +00000029 LB00001       POP NAV NO1 CB                                       000000037788 000000037789 000000037810
XLB146CBNAV2                    +EQU XLBCB                           +00000030 LB00001       POP NAV NO2 CB                                       000000037789 000000037790 000000037810
XLB146CBRMI1                    +EQU XLBCB                           +00000031 LB00001       POP RMI NO1 CB                                       000000037790 000000037791 000000037810
XLB146CBRMI2                    +EQU XLBCB                           +00000032 LB00001       POP RMI NO2 CB
XLB221S177                      +EQU LBBUS                           +00000045 LB00001       MAIN BATTERY SWITCH 24-51-00-4
XLA221S136INV1                  +EQU LBBUS                           +00000049 LB00001       AC SELECT ON SWITCH INV 1
XLA221S136INV2                  +EQU LBBUS                           +00000050 LB00001       AC SELECT ON SWITCH INV 2
XLB224S121BRT                   +EQU LBBUS                           +00000056 LB00001       CABIN LIGHTS SW: BRIGHT
XLB224S121DIM                   +EQU LBBUS                           +00000057 LB00001       CABIN LIGHTS SW: DIM
XLB224S121OFF                   +EQU LBBUS                           +00000058 LB00001       CABIN LIGHTS SW: OFF
XLB224S180NSMK                  +EQU LBBUS                           +00000059 LB00001       ORDINANCE SIGN SW: FSB + NOSMK
XLB224S180OFF                   +EQU LBBUS                           +00000060 LB00001       ORDINANCE SIGN SW: OFF
XLB224S180FSB                   +EQU LBBUS                           +00000061 LB00001       ORDINANCE SIGN SW: FSB ONLY
XLB136S150                      +EQU LBBUS                           +00000062 LB00001       PROP SYNC CONTROL SW
XLB223S173                      +EQU LBBUS                           +00000063 LB00001       LEFT FUEL VENT HEAT SW
XLB223S174                      +EQU LBBUS                           +00000064 LB00001       RIGHT FUEL VENT HEAT SW
XLB221S162                      +EQU LBBUS                           +00000065 LB00001       L ENG ANTI-ICE ON/OFF SW
XLB221S163                      +EQU LBBUS                           +00000066 LB00001       R ENG ANTI-ICE ON/OFF SW
XLB221S263                      +EQU LBBUS                           +00000067 LB00001       L ENG ANTI-ICE MAIN/STBY SW
XLB221S264                      +EQU LBBUS                           +00000068 LB00001       R ENG ANTI-ICE MAIN/STBY SW
XLB245S2                        +EQU LBBUS                           +00000069 LB00001       RUDDER BOOST SW
XLB224S300A                     +EQU LBBUS                           +00000070 LB00001       FURN MSTR CONT SW: FURN ON
XLB224S300B                     +EQU LBBUS                           +00000071 LB00001       FURN MSTR CONT SW: COFFEE OFF
XLB221S147TST                   +EQU LBBUS                           +00000072 LB00001       PROP OVERSPEED GOV TEST SW

XCB221S177                       EQU LBBUS                           +00000045 LB00001       MAIN BATTERY SWITCH 24-51-00-4
XCB221S136INV1                   EQU LBBUS                           +00000049 LB00001       AC SELECT ON SWITCH INV 1
XCB221S136INV2                   EQU LBBUS                           +00000050 LB00001       AC SELECT ON SWITCH INV 2
XCB224S121BRT                    EQU LBBUS                           +00000056 LB00001       CABIN LIGHTS SW: BRIGHT
XCB224S121DIM                    EQU LBBUS                           +00000057 LB00001       CABIN LIGHTS SW: DIM                                 
XCB224S121OFF                    EQU LBBUS                           +00000058 LB00001       CABIN LIGHTS SW: OFF                                 
XCB224S180NSMK                   EQU LBBUS                           +00000059 LB00001       ORDINANCE SIGN SW: FSB + NOSMK                       
XCB224S180OFF                    EQU LBBUS                           +00000060 LB00001       ORDINANCE SIGN SW: OFF                               
XCB224S180FSB                    EQU LBBUS                           +00000061 LB00001       ORDINANCE SIGN SW: FSB ONLY                          
XCB136S150                       EQU LBBUS                           +00000062 LB00001       PROP SYNC CONTROL SW                                 
XCB223S173                       EQU LBBUS                           +00000063 LB00001       LEFT FUEL VENT HEAT SW                               
XCB223S174                       EQU LBBUS                           +00000064 LB00001       RIGHT FUEL VENT HEAT SW
XCB221S162                       EQU LBBUS                           +00000065 LB00001       L ENG ANTI-ICE ON/OFF SW
XCB221S163                       EQU LBBUS                           +00000066 LB00001       R ENG ANTI-ICE ON/OFF SW                             
XCB221S263                       EQU LBBUS                           +00000067 LB00001       L ENG ANTI-ICE MAIN/STBY SW                          
XCB221S264                       EQU LBBUS                           +00000068 LB00001       R ENG ANTI-ICE MAIN/STBY SW                          
XCB245S2                         EQU LBBUS                           +00000069 LB00001       RUDDER BOOST SW 
XCB224S300A                      EQU LBBUS                           +00000070 LB00001       FURN MSTR CONT SW: FURN ON 
XCB224S300B                      EQU LBBUS                           +00000071 LB00001       FURN MSTR CONT SW: COFFEE OFF                        
XCB221S147TST                    EQU LBBUS                           +00000072 LB00001       PROP OVERSPEED GOV TEST SW
XLB221S177                      -EQU LBBUS                           +00000045 LB00001       MAIN BATTERY SWITCH 24-51-00-4
XLA221S136INV1                  -EQU LBBUS                           +00000049 LB00001       AC SELECT ON SWITCH INV 1
XLA221S136INV2                  -EQU LBBUS                           +00000050 LB00001       AC SELECT ON SWITCH INV 2
XLB224S121BRT                   -EQU LBBUS                           +00000056 LB00001       CABIN LIGHTS SW: BRIGHT
XLB224S121DIM                   -EQU LBBUS                           +00000057 LB00001       CABIN LIGHTS SW: DIM
XLB224S121OFF                   -EQU LBBUS                           +00000058 LB00001       CABIN LIGHTS SW: OFF
XLB224S180NSMK                  -EQU LBBUS                           +00000059 LB00001       ORDINANCE SIGN SW: FSB + NOSMK
XLB224S180OFF                   -EQU LBBUS                           +00000060 LB00001       ORDINANCE SIGN SW: OFF
XLB224S180FSB                   -EQU LBBUS                           +00000061 LB00001       ORDINANCE SIGN SW: FSB ONLY
XLB136S150                      -EQU LBBUS                           +00000062 LB00001       PROP SYNC CONTROL SW
XLB223S173                      -EQU LBBUS                           +00000063 LB00001       LEFT FUEL VENT HEAT SW
XLB223S174                      -EQU LBBUS                           +00000064 LB00001       RIGHT FUEL VENT HEAT SW
XLB221S162                      -EQU LBBUS                           +00000065 LB00001       L ENG ANTI-ICE ON/OFF SW
XLB221S163                      -EQU LBBUS                           +00000066 LB00001       R ENG ANTI-ICE ON/OFF SW
XLB221S263                      -EQU LBBUS                           +00000067 LB00001       L ENG ANTI-ICE MAIN/STBY SW
XLB221S264                      -EQU LBBUS                           +00000068 LB00001       R ENG ANTI-ICE MAIN/STBY SW
XLB245S2                        -EQU LBBUS                           +00000069 LB00001       RUDDER BOOST SW
XLB224S300A                     -EQU LBBUS                           +00000070 LB00001       FURN MSTR CONT SW: FURN ON
XLB224S300B                     -EQU LBBUS                           +00000071 LB00001       FURN MSTR CONT SW: COFFEE OFF
XLB221S147TST                   -EQU LBBUS                           +00000072 LB00001       PROP OVERSPEED GOV TEST SW
XCB223S166N                      EQU LBBUS                           +00000073 LB00001       COPILOT WINDSHIELD ANTI-ICE CNTL SW NORM
XCB223S166H                      EQU LBBUS                           +00000074 LB00001       COPILOT WINDSHIELD ANTI-ICE CNTL SW HI
XCB223S140N                      EQU LBBUS                           +00000075 LB00001       PILOT WINDSHIELD ANTI-ICE CNTL SW NORM
XCB223S140H                      EQU LBBUS                           +00000076 LB00001       PILOT WINDSHIELD ANTI-ICE CNTL SW HI
UCLDENAK                        -EQU XCVB1                           +00001735 LB00001       LOWER CLOUD DENSITY ACK
UCLDENF                         -EQU XCVB1                           +00001736 LB00001       LOWER CLOUD DENSITY FLAG
UCLDEND                         -EQU XCVB1                           +00001737 IW00001       LOWER CLOUD DENSITY DEMAND
VCLDEN                          -EQU XCVB1                           +00001738 IW00001       LOWER CLOUD DENSITY ACTUAL
UCLDENUAK                       -EQU XCVB1                           +00001739 LB00001       UPPER CLOUD DENSITY ACK
UCLDENUF                        -EQU XCVB1                           +00001740 LB00001       UPPER CLOUD DENSITY FLAG
UCLDENUD                        -EQU XCVB1                           +00001741 IW00001       UPPER CLOUD DENSITY DEMAND
VCLDENU                         -EQU XCVB1                           +00001742 IW00001       UPPER CLOUD DENSITY ACTUAL
UCLDENAK                         EQU XCVB1                           +00001766 LB00001       LOWER CLOUD DENSITY ACK
UCLDENF                          EQU XCVB1                           +00001767 LB00001       LOWER CLOUD DENSITY FLAG
UCLDEND                          EQU XCVB1                           +00001768 IW00001       LOWER CLOUD DENSITY DEMAND
VCLDEN                           EQU XCVB1                           +00001772 IW00001       LOWER CLOUD DENSITY ACTUAL
UCLDENUAK                        EQU XCVB1                           +00001776 LB00001       UPPER CLOUD DENSITY ACK
UCLDENUF                         EQU XCVB1                           +00001777 LB00001       UPPER CLOUD DENSITY FLAG
UCLDENUD                         EQU XCVB1                           +00001778 IW00001       UPPER CLOUD DENSITY DEMAND
VCLDENU                          EQU XCVB1                           +00001782 IW00001       UPPER CLOUD DENSITY ACTUAL

XCB224S205HI                    +EQU FLY_MISC_BASE                   +00000476 LB00001       VENT BLOWER CNTL SW HIGH (S205)
XCB224S205LO                    +EQU FLY_MISC_BASE                   +00000477 LB00001       VENT BLOWER CNTL SW LO (S205)
XCB224S205AUTO                  +EQU FLY_MISC_BASE                   +00000478 LB00001       VENT BLOWER CNTL SW AUTO (S205)
XCB224S233OFF                   +EQU FLY_MISC_BASE                   +00000484 LB00001       AFT BLOWER SWITCH OFF POSITION
XCB224S233ON                    +EQU FLY_MISC_BASE                   +00000485 LB00001       AFT BLOWER SWITCH ON POSITION
XLB224S205HI                    -EQU FLY_MISC_BASE                   +00000476 LB00001       VENT BLOWER CNTL SW HIGH (S205)
XLB224S205LO                    -EQU FLY_MISC_BASE                   +00000477 LB00001       VENT BLOWER CNTL SW LO (S205)
XLB224S205AUTO                  -EQU FLY_MISC_BASE                   +00000478 LB00001       VENT BLOWER CNTL SW AUTO (S205)
XLB224S233OFF                   -EQU FLY_MISC_BASE                   +00000484 LB00001       AFT BLOWER SWITCH OFF POSITION
XLB224S233ON                    -EQU FLY_MISC_BASE                   +00000485 LB00001       AFT BLOWER SWITCH ON POSITION

XCB224S205HI                     EQU FLY_MISC_BASE                   +00000476 LB00001       VENT BLOWER CNTL SW HIGH (S205)
XCB224S205LO                     EQU FLY_MISC_BASE                   +00000477 LB00001       VENT BLOWER CNTL SW LO (S205)
XCB224S205AUTO                   EQU FLY_MISC_BASE                   +00000478 LB00001       VENT BLOWER CNTL SW AUTO (S205)
XCB224S233OFF                    EQU FLY_MISC_BASE                   +00000484 LB00001       AFT BLOWER SWITCH OFF POSITION
XCB224S233ON                     EQU FLY_MISC_BASE                   +00000485 LB00001       AFT BLOWER SWITCH ON POSITION
VISHTBIAS                        EQU XCVB1                           +00001786 EW00001       VISUAL HEIGHT BIAS
VGNDTRFACTIV                     EQU XCVB1                           +00001790 LB00001       GROUND TRAFFIC ACTIVE
VGNDTRFROUTE                     EQU XCVB1                           +00001791 IB00001       GROUND TRAFFIC ROUTE
VGNDTRFMODEL                     EQU XCVB1                           +00001792 IB00001       GROUND TRAFFIC MODEL
VGNDTRFSTATE                     EQU XCVB1                           +00001793 IB00001       GROUND TRAFFIC STATE
VGNDTRFACTIV                    +EQU XCVB1                           +00001790 LB00001       VISUAL GROUND TRAFFIC ACTIVE
VGNDTRFROUTE                    +EQU XCVB1                           +00001791 IB00001       VISUAL GROUND TRAFFIC ROUTE
VGNDTRFMODEL                    +EQU XCVB1                           +00001792 IB00001       VISUAL GROUND TRAFFIC MODEL
VGNDTRFSTATE                    +EQU XCVB1                           +00001793 IB00001       VISUAL GROUND TRAFFIC STATE
LCB146CBNOSMK                   -EQU LCBCB                           +00000138 LB00001       NO SMOKING & FSB SIGN CB
XCB146CBNOSMK                   -EQU XCBCB                           +00000138 LB00001       NO SMOKING & FSB SIGN CB
XCB224S121DIM                   -EQU LBBUS                           +00000057 LB00001       CABIN LIGHTS SW: DIM
XCB224S180OFF                   -EQU LBBUS                           +00000060 LB00001       ORDINANCE SIGN SW: OFF
XCB221S162                      -EQU LBBUS                           +00000065 LB00001       L ENG ANTI-ICE ON/OFF SW
XCB221S163                      -EQU LBBUS                           +00000066 LB00001       R ENG ANTI-ICE ON/OFF SW
XCB221S263                      -EQU LBBUS                           +00000067 LB00001       L ENG ANTI-ICE MAIN/STBY SW
XCB221S264                      -EQU LBBUS                           +00000068 LB00001       R ENG ANTI-ICE MAIN/STBY SW
XCB224S300B                     +EQU LBBUS                           +00000071 LB00001       FURN MSTR CONT SW: FURN + COFFEE OFF
XCB223S166N                     -EQU LBBUS                           +00000073 LB00001       COPILOT WINDSHIELD ANTI-ICE CNTL SW NORM
XCB223S166H                     -EQU LBBUS                           +00000074 LB00001       COPILOT WINDSHIELD ANTI-ICE CNTL SW HI
XCB223S140N                     -EQU LBBUS                           +00000075 LB00001       PILOT WINDSHIELD ANTI-ICE CNTL SW NORM
XCB223S140H                     -EQU LBBUS                           +00000076 LB00001       PILOT WINDSHIELD ANTI-ICE CNTL SW HI
XCB228CB9                       -EQU XCBCB                           +00000006 LB00001       NAV MEMORY CB 24-51-00-4
XCB228CB11                      -EQU XCBCB                           +00000004 LB00001       BATTERY RELAY CB 24-51-00-4
XCB228CB13                      -EQU XCBCB                           +00000002 LB00001       RT ENGINE FIRE EXT CB 24-51-00-4
XCB228CB12                      -EQU XCBCB                           +00000003 LB00001       LT ENGINE FIRE EXT CB 24-51-00-4
XCB228CB5                       -EQU XCBCB                           +******** LB00001       LT FW FUEL SHUTOFF VALVE CB 24-51-00-4
XCB228CB6                       -EQU XCBCB                           +00000001 LB00001       RT FW FUEL SHUTOFF VALVE CB 24-51-00-4
XCB228CB10                      -EQU XCBCB                           +00000005 LB00001       ENTRY LIGHTS CB 24-51-00-4
XCB145CB157                     -EQU XCBCB                           +00000016 LB00001       ENGINE INSTRUMENTS CB 24-52-00-3
XCB145CB243                     -EQU XCBCB                           +00000118 LB00001       NO. 1 INVERTER CONTROL CB 24-21-00-8
XCB145CB244                     -EQU XCBCB                           +00000119 LB00001       NO. 2 INVERTER CONTROL CB 24-21-00-8
XCB145CB224                     -EQU XCBCB                           +00000008 LB00001       ELEC HEAT FWD CB 24-52-00-3
XCB145CB153                     -EQU XCBCB                           +00000007 LB00001       PILOT WINDSHIELD ANTI-ICE CB 24-52-00-3
XCB145CB207                     -EQU XCBCB                           +00000014 LB00001       DC TEST JACK CB 24-52-00-3
XCB145CB150                     -EQU XCBCB                           +00000013 LB00001       AFT EVAPORATOR BLWR PWR CB 24-52-00-3
XCB145CB225                     -EQU XCBCB                           +00000011 LB00001       ELEC HEAT AFT CB 24-52-00-3
XCB145CB206                     -EQU XCBCB                           +00000015 LB00001       AIR CONDITIONER CLUTCH CB 24-52-00-3
XCB145CB195                     -EQU XCBCB                           +00000012 LB00001       VENT BLOWER POWER CB 24-52-00-3
XCB145CB181                     -EQU XCBCB                           +00000010 LB00001       COPILOT WINDSHIELD ANTI-ICE 24-52-00-3
XCB146CB139                     -EQU XCBCB                           +00000026 LB00001       LT OIL PRESSURE WARN CB 24-53-01-5
XCB146CB140                     -EQU XCBCB                           +00000063 LB00001       RT OIL PRESSURE WARN CB 24-53-02-5
XCB146CB159                     -EQU XCBCB                           +00000024 LB00001       LT GEN OVERHEAT CB 24-53-01-5
XCB146CB160                     -EQU XCBCB                           +00000061 LB00001       RT GEN OVERHEAT CB 24-53-02-5
LCB146CBACC                      EQU LCBCB                           +00000138 LB00001       AVIONICS ACC CB
XCB146CBACC                      EQU XCBCB                           +00000138 LB00001       AVIONICS ACC CB
LCB228CB15                       EQU LCBCB                           +00000186 LB00001       MOD CB 24-51-00-4
LCB228CB14                       EQU LCBCB                           +00000187 LB00001       BAGGAGE POD 24-51-00-4
VISUALRESET                      EQU XCVB1                           +00001794 LB00001       VISUAL RESET FLAG
LLGENLDSEL                       EQU RLOADAVG                        +00000104 LB00001       LEFT GEN LOAD/VOLT METER SELECT SW
LRGENLDSEL                       EQU RLOADAVG                        +00000105 LB00001       RIGHT GEN LOAD/VOLT METER SELECT SW
EPN1DMD                          EQU X_EBW1                          +00000968 EW00002       Demanded N1 for PLA driver
EPN1ERR                          EQU X_EBW1                          +00000976 EW00002       N1 Error for PLA driver
EPN1DOT                          EQU X_EBW1                          +00000984 EW00002       N1 Dot 
EPN1_PRO                         EQU X_EBW1                          +00000992 EW00002       N1 Proportional Term for PLA driver
EPN1_DIF                         EQU X_EBW1                          +00001000 EW00002       N1 Differential Term for PLA driver
EPN1_INT                         EQU X_EBW1                          +00001008 EW00002       N1 Integral Term for PLA driver
EPN1_PROGN                       EQU X_EBW1                          +00001012 EW00001       N1 Proportional Gain for PLA driver
EPN1_DIFGN                       EQU X_EBW1                          +00001016 EW00001       N1 Differential Gain for PLA driver
EPN1_INTGN                       EQU X_EBW1                          +00001020 EW00001       N1 Integral Gain for PLA driver
EPN1DMD_PLA                      EQU X_EBW1                          +00001024 EW00002       N1 Driver Demanded PLA 
EPN1_PROGN                      -EQU X_EBW1                          +00001012 EW00001       N1 Proportional Gain for PLA driver  
EPN1_PROGN                       EQU X_EBW1                          +00001032 EW00001       N1 Proportional Gain for PLA driver  
EPN1DRVR                         EQU X_EBB1                          +00000104 LB00001       N1 hold PLA driver active flag
ENG_DB_VAR                       EQU X_EBW1                          +00001036 IW00001       Engine debug integer
lbni_alt_alert_cbpwr             EQU X_ABB1                          +00000441 LB00001       Altitude Alerter CB                                  
MBDOVRTMP                        EQU locpp_cabinpressure             +00000021 LB00001       INSERT DUCT OVERTEMP MALF
MBDOVRTMP                       -EQU locpp_cabinpressure             +00000021 LB00001       INSERT DUCT OVERTEMP MALF
MB200MALF                        EQU DP                              +00037810 LB00060       B200 SPECIFIC MALFUNCTIONS
MBB146CB169                      EQU MB200MALF                       +******** LB00001       INSERT MASTER AVIONICS CB POP
MBDOVRTMP                        EQU MB200MALF                       +00000001 LB00001       INSERT DUCT OVERTEMP MALF
ENG_DB_VAR                      -EQU X_EBW1                          +00001036 IW00001       Engine debug integer   
ENG_DB_VAR                       EQU X_EBB1                          +00000105 IB00020       Engine Debug Integers
QDVORBACKUP222                   EQU Q_DITS                          +00027937 IW00001       VOR 2 ARINC LABEL 222                                
QDVORBACKUP222SSM                EQU Q_DITS                          +00027941 LB00004       VOR 2 ARINC LABEL 222                                
QDVORBACKUP173                   EQU Q_DITS                          +00027945 IW00001       VOR 2 ARINC LABEL 173                                
QDVORBACKUP173SSM                EQU Q_DITS                          +00027949 LB00004       VOR 2 ARINC LABEL 173                                
QDVORBACKUP174                   EQU Q_DITS                          +00027953 IW00001       VOR 2 ARINC LABEL 174                                
QDVORBACKUP174SSM                EQU Q_DITS                          +00027957 LB00004       VOR 2 ARINC LABEL 174                                
XLB146CBDPU                      EQU XLBCB                           +00000033 LB00001       POP DPU CB
EPN1_SS_END                      EQU X_EBW1                          +00001036 EW00002       N1 Steady State at end of Accel/Decel
EPN1_SS_START                    EQU X_EBW1                          +******** EW00002       N1 Steady State at start of Accel/Decel
EPN1_CRIT_START                  EQU X_EBW1                          +******** EW00002       N1 Crit at start of Accel/Decel
EPN1_CRIT_END                    EQU X_EBW1                          +******** EW00002       N1 Crit at end of Accel/Decel
EPN1_CT10P                       EQU X_EBW1                          +00001068 EW00002       Crit 10% eng response time from pla mv 
EPN1_CT90P                       EQU X_EBW1                          +00001076 EW00002       Crit 90% eng response time from pla mv 
EPN1_ST10P                       EQU X_EBW1                          +00001084 EW00002       Sim 10% eng response time from pla mv 
EPN1_ST90P                       EQU X_EBW1                          +00001092 EW00002       Sim 90% eng response time from pla mv 
ENG_TRANS_PASS                   EQU X_EBB1                          +00000125 LB00001       Engine Accel/Decel Test Pass Flag
ENG_TRANS_START                  EQU X_EBB1                          +00000126 LB00001       Engine Accel/Decel Test Start Flag
ENG_TRANS_TYPE                   EQU X_EBB1                          +00000127 LB00001       Eng Trans test type F=decel T=accel
ENG_TRANS_PASS                  -EQU X_EBB1                          +00000125 LB00001       Engine Accel/Decel Test Pass Flag 
ENG_TRANS_10P_PASS               EQU X_EBB1                          +00000125 LB00001       Eng Accel/Decel Test 10% Pass Flag 
ENG_TRANS_90P_PASS               EQU X_EBB1                          +00000128 LB00001       Eng Accel/Decel Test 90% Pass Flag 
ENG_TRANS_10P_PASS              -EQU X_EBB1                          +00000125 LB00001       Eng Accel/Decel Test 10% Pass Flag
ENG_TRANS_START                 -EQU X_EBB1                          +00000126 LB00001       Engine Accel/Decel Test Start Flag 
ENG_TRANS_TYPE                  -EQU X_EBB1                          +00000127 LB00001       Eng Trans test type F=decel T=accel 
ENG_TRANS_90P_PASS              -EQU X_EBB1                          +00000128 LB00001       Eng Accel/Decel Test 90% Pass Flag   
ENG_TRANS_10P_PASS               EQU X_EBB1                          +00000125 LB00002       Eng Accel/Decel Test 10% Pass Flag
ENG_TRANS_90P_PASS               EQU X_EBB1                          +00000127 LB00002       Eng Accel/Decel Test 90% Pass Flag   
ENG_TRANS_START                  EQU X_EBB1                          +00000129 LB00002       Engine Accel/Decel Test Start Flag 
ENG_TRANS_TYPE                   EQU X_EBB1                          +00000131 LB00002       Eng Trans test type F=decel T=accel 
ENG_TRANS_CHK_ON                 EQU X_EBB1                          +00000133 LB00001       Eng Trans test analyzer on
ENG_TRANS_START_TIME             EQU X_EBW1                          +00001100 EW00002       Eng trans test start time
ENG_TRANS_10P_RATIO              EQU X_EBW1                          +00001108 EW00002       10% eng resp time ratio (sim/crit)
ENG_TRANS_90P_RATIO              EQU X_EBW1                          +00001116 EW00002       90% eng resp time ratio (sim/crit)
EPLA_LAG_TAU                     EQU X_EBW1                          +00001124 EW00001       Engine PLA input lag time constant
EPLA_INST                        EQU X_EBW1                          +00001128 EW00002       Engine PLA instantaneous input
MBB146CBDPU                      EQU MB200MALF                       +00000002 LB00001       INSERT DPU CB POP
MBB146CBDSP                      EQU MB200MALF                       +00000003 LB00001       INSERT DSP CB POP
MBB146CBEADI                     EQU MB200MALF                       +00000004 LB00001       INSERT EADI CB POP
MBB146CBEHSI                     EQU MB200MALF                       +00000005 LB00001       INSERT EHSI CB POP
MBB146CBNAV1                     EQU MB200MALF                       +00000006 LB00001       INSERT NAV NO1 CB POP
MBB146CBNAV2                     EQU MB200MALF                       +00000007 LB00001       INSERT NAV NO2 CB POP
MBB146CBDME1                     EQU MB200MALF                       +00000008 LB00001       INSERT DME NO1 CB POP
MBB146CBDME2                     EQU MB200MALF                       +00000009 LB00001       INSERT DME NO2 CB POP
MBB146CBCOMM1                    EQU MB200MALF                       +00000010 LB00001       INSERT COMM NO1 CB POP
MBB146CBCOMM2                    EQU MB200MALF                       +00000011 LB00001       INSERT COMM NO2 CB POP
MBB146CBADF                      EQU MB200MALF                       +00000012 LB00001       INSERT ADF CB POP
MBB146CBALTAIRD                  EQU MB200MALF                       +00000013 LB00001       INSERT PILOT ALTM AIR DATA CB POP
MBB146CBCENCDALT                 EQU MB200MALF                       +00000014 LB00001       INSERT COPILOT ALTIMETER CB POP
MBB146CBFCSPWR                   EQU MB200MALF                       +00000015 LB00001       INSERT FCS POWER CB POP
MBB146CBCMPS1                    EQU MB200MALF                       +00000016 LB00001       INSERT CMPS NO1 CB POP
MBB146CBCMPS2                    EQU MB200MALF                       +00000017 LB00001       INSERT CMPS NO2 CB POP
MBB146CBRMI1                     EQU MB200MALF                       +00000018 LB00001       INSERT RMI NO1 CB POP
MBB146CBRMI2                     EQU MB200MALF                       +00000019 LB00001       INSERT RMI NO2 CB POP
MBB146CB145                      EQU MB200MALF                       +00000020 LB00001       INSERT STALL WARN CB POP
MBB124CB25                       EQU MB200MALF                       +00000021 LB00001       INSERT FLAP CONT & IND CB POP
MBB146CB166                      EQU MB200MALF                       +00000022 LB00001       INSERT PITCH TRIM CB POP
MBB124CB11                       EQU MB200MALF                       +00000023 LB00001       INSERT L STANDBY PUMP CB POP
MBB124CB3                        EQU MB200MALF                       +00000024 LB00001       INSERT R STANDBY PUMP CB POP
MBB124CB7                        EQU MB200MALF                       +00000025 LB00001       INSERT FUEL XFR VALVE CB POP
MBB124CB27                       EQU MB200MALF                       +00000026 LB00001       INSERT LEFT MAN PROP DE-ICE CB POP
XLB146CBARLWRN                  +EQU XLBCB                           +00000017 LB00001       POP AURAL WARNING CB
XLB124CB25                      +EQU XLBCB                           +00000006 LB00001       POP FLAPS CONTROL & IND CB
MBBALTWARN                       EQU MB200MALF                       +00000027 LB00001       INSERT ALT WARN MALF: CAB ALT TOO HIGH
EPN1DT                           EQU X_EBW1                          +00001136 EW00002       %N1 Dot demanded
EPN1DT                          -EQU X_EBW1                          +00001136 EW00002       %N1 Dot demanded            
EPN1DTD                          EQU X_EBW1                          +00001136 EW00002       %N1 Dot demanded            
EPN1DTTAU                        EQU X_EBW1                          +00001144 EW00002       %N1 Dot Tau for slow start accel effect
EPN1DT                           EQU X_EBW1                          +00001152 EW00002       %N1 Dot 
NGSLWACL                         EQU X_EBW1                          +00001160 IW00002       N1 Slow Accel Time Constant Table
PROOF_OF_MATCH                   EQU                                 +00708616 LB00001       PROOF OF MATCH Flag
rwctl_atg_ele_trim_pos           EQU X_EBW1                          +00001168 EW00001       Elevator Trim Position
lbctl_atg_pit_trim_up            EQU X_EBB1                          +00000134 LB00001       ATG pitch trim up flag
lbctl_atg_pit_trim_dn            EQU X_EBB1                          +00000135 LB00001       ATG pitch trim dn flag
lbctl_atg_pit_trim_tst           EQU X_EBB1                          +00000136 LB00001       ATG pitch trim test flag
MBB146CB108                      EQU MB200MALF                       +00000028 LB00001       INSERT LANDING GEAR WARN HORN CB POP
rwctl_atg_ele_trim_err           EQU X_EBW1                          +00001172 EW00001       Elevator Trim Position Error    
rhmchmadd                        EQU GCTLBUF_RHM                     +00010560 EW00040       additional hinge moments
TCHMADD0                         EQU CTLBUF_RHM_ADD                  +00004480 IW00010       additional hinge moment tables for ch 0
TCHMADD0                         EQU GCTLBUF_RHM_ADD                 +00004480 IW00010       additional hinge moment tables for ch 0
LZBPHLD                          EQU ATSTBASE                        +00005220 LB00001       BRK PRESS HLD FLAG
THLBPEDPOS                       EQU ATSTBASE                        +00005224 IW00001       TABLE BRK PED POS
THRBPEDPOS                       EQU ATSTBASE                        +00005228 IW00001       TABLE BRK PED POS
RZCBP                            EQU ATSTBASE                        +00005232 EW00002       COMMANDED BRAKE PRESSURE
rfmcdv                           EQU GCTLBUF_RFM                     +00009872 EW00020       Fwd mass variable damping coefficient 
rfmcdt                           EQU GCTLBUF_RFM                     +00009952 EW00020       Total fwd mass damping coefficient 
TCFMCDV                          EQU GCTLBUF_TABLES                  +00007184 IW00040       Fwd mass variable damp coeff table
RVSFNPMUL                        EQU X_EBW1                          +00001176 IW00001       Reverse Thrust Multiplier Table
ERVSFNP_MULT                     EQU X_EBW1                          +00001180 EW00001       Reverse Thrust Multiplier 
rfc_side_force_tuner             EQU                                 +00708632 EW00001       rfc_side_force_tuner     
rfc_side_force_tuner_k           EQU                                 +00708636 EW00001       rfc_side_force_tuner_k   
bfc_side_force_tuner             EQU                                 +00708640 LB00001       bfc_side_force_tuner     
RZFLAREK                         EQU ATSTBASE                        +00005240 EW00001       pitch command for flare gain
RZFLARETK                        EQU ATSTBASE                        +00005244 EW00001       pitch command for flare washout tau
RZFLAREH                         EQU ATSTBASE                        +00005248 EW00001       flare height
RZFLRFD                          EQU ATSTBASE                        +00005252 EW00001       flare fade in
RZFLRFDR                         EQU ATSTBASE                        +00005256 EW00001       flare fade in time
LZFLARE                          EQU ATSTBASE                        +00005260 LB00001       flare mode active
MBBALTWARN                      -EQU MB200MALF                       +00000027 LB00001       INSERT ALT WARN MALF: CAB ALT TOO HIGH
MBB146CB108                     -EQU MB200MALF                       +00000028 LB00001       INSERT LANDING GEAR WARN HORN CB POP

MBB146CB108                      EQU MB200MALF                       +00000027 LB00001       INSERT LANDING GEAR WARN HORN CB POP

rfmboni                         -EQU GCTLBUF_RFM                     +00009692 MB00020       Input flag, loader ON
rfmbbd1i                        -EQU GCTLBUF_RFM                     +00009772 MB00020       backdrive active #1 input
rfmbbd2i                        -EQU GCTLBUF_RFM                     +00009852 MB00020       backdrive active #2 input
rfmcdv                          -EQU GCTLBUF_RFM                     +00009872 EW00020       Fwd mass variable damping coefficient
rfmcdt                          -EQU GCTLBUF_RFM                     +00009952 EW00020       Total fwd mass damping coefficient
rfmboni                          EQU GCTLBUF_RFM                     +00009632 MB00020       Input flag, loader ON
rfmbbd1i                         EQU GCTLBUF_RFM                     +00009652 MB00020       backdrive active #1 input
rfmbbd2i                         EQU GCTLBUF_RFM                     +00009672 MB00020       backdrive active #2 input
rfmcdv                           EQU GCTLBUF_RFM                     +00009692 EW00020       Fwd mass variable damping coefficient
rfmcdt                           EQU GCTLBUF_RFM                     +00009772 EW00020       Total fwd mass damping coefficient
rfmcdv                          -EQU GCTLBUF_RFM                     +00009692 EW00020       Fwd mass variable damping coefficient 
rfmcdv                           EQU GCTLBUF_RFM                     +00009852 EW00020       Fwd mass variable damping coefficient 
lbaf_inflt_tst_active            EQU X_ABB1                          +00000442 LB00002       Inflt AP self-test active
lbaf_ap_fault_chk_active         EQU X_ABB1                          +00000444 LB00002       AP Fault checking system active
lbaf_faults_detected             EQU X_ABB1                          +00000446 LB00002       AP Faults Detected
rwaf_inflt_tst_timer             EQU X_ABW1                          +00001240 EW00002       Inflt test timer
XAICABTMPAUTO                    EQU FLY_MISC_BASE                   +00000488 EW00001       AI AUTO CABIN TEMP SELECTOR KNOB
XPCABTMPSEL                      EQU FLY_MISC_BASE                   +00000492 EW00001       CABIN TEMP SELECTOR KNOB: AUTO MODE
JGCABTP                          EQU AI_GAINS                        +00000256 EW00001       AI-GAIN for XAICABTMPAUTO
JZCABTP                          EQU AI_GAINS                        +00000260 EW00001       AI-OFFSET for XAICABTMPAUTO
rfmcdt                           EQU GCTLBUF_RFM                     +00009772 EW00020       Total fwd mass damping coefficient
rfmcdv                           EQU GCTLBUF_RFM                     +00009852 EW00020       Fwd mass variable damping coefficient
rfmboni                         +EQU GCTLBUF_RFM                     +00009692 MB00020       Input flag, loader ON                              
rfmbbd1i                        +EQU GCTLBUF_RFM                     +00009772 MB00020       backdrive active #1 input                          
rfmbbd2i                        +EQU GCTLBUF_RFM                     +00009852 MB00020       backdrive active #2 input                          
rfmcdt                          +EQU CTLBUF                          +00017112 EW00020       Total fwd mass damping coefficient                 
rfmcdv                          +EQU CTLBUF                          +00017192 EW00020       Fwd mass variable damping coefficient              
rwaf_pit_trim_max_error          EQU X_ABW1                          +00001248 EW00001       Pitch trim test max error
MBB146CB170                      EQU MB200MALF                       +00000029 LB00001       INSERT AVIONICS BUS NO1 CB POP
MBB146CB171                      EQU MB200MALF                       +00000030 LB00001       INSERT AVIONICS BUS NO2 CB POP
MBB146CB142                      EQU MB200MALF                       +00000028 LB00001       INSERT RUDDER BOOST CB POP
rfc_rolling_moment_tuner         EQU                                 +00708644 EW00001       rfc_rolling_moment_tuner      
rfc_rolling_moment_tuner_k       EQU                                 +00708648 EW00001       rfc_rolling_moment_tuner_k    
bfc_rolling_moment_tuner         EQU                                 +00708652 LB00001       bfc_rolling_moment_tuner      
QDVORBACKUP222                  +EQU Q_DITS                          +00027937 EW00001       VOR 2 ARINC LABEL 222                                000000377937 000000377941 000000378000
QDVORBACKUP173                  +EQU Q_DITS                          +00027945 EW00001       VOR 2 ARINC LABEL 173                                000000377945 000000377949 000000378000
QDVORBACKUP174                  +EQU Q_DITS                          +00027953 EW00001       VOR 2 ARINC LABEL 174                                000000377953 000000377957 000000378000
MBB124CB10                       EQU MB200MALF                       +00000031 LB00001       INSERT L AUX TRANSFER CB POP
MBB124CB4                        EQU MB200MALF                       +00000032 LB00001       INSERT R AUX TRANSFER CB POP
MBBAC26CBNAV1                    EQU MB200MALF                       +00000031 LB00001       INSERT NAV NO1 AC CB POP
MBBAC26CBNAV2                    EQU MB200MALF                       +00000032 LB00001       INSERT NAV NO2 AC CB POP
MBBAC26CBNAV1                   -EQU MB200MALF                       +00000031 LB00001       INSERT NAV NO1 AC CB POP
MBBAC26CBNAV2                   -EQU MB200MALF                       +00000032 LB00001       INSERT NAV NO2 AC CB POP
MBBAC26CBNAV1                    EQU MB200MALF                       +00000033 LB00001       INSERT NAV NO1 AC CB POP
MBBAC26CBNAV2                    EQU MB200MALF                       +00000034 LB00001       INSERT NAV NO2 AC CB POP
CLS_NW_CASTOR_VEL                EQU CLS_HOST_UNIQUE_F               +00000104 EW00001       NOSEWHEEL CASTOR VELOCITY 
NW_CASTOR_VEL                    EQU HOST_UNIQUE_F                   +00000104 EW00001       NOSEWHEEL CASTOR VELOCITY 
EGIFNP_MULT                      EQU X_EBW1                          +00001184 EW00001       Ground Idle Thrust Multiplier          
GIFNPMUL                         EQU X_EBW1                          +00001188 EW00001       Ground Idle Thrust Multiplier Table         
GIFNPMUL                        -EQU X_EBW1                          +00001188 EW00001       Ground Idle Thrust Multiplier Table  
GIFNPMUL                         EQU X_EBW1                          +00001188 IW00001       Ground Idle Thrust Multiplier Table  
GICT                             EQU X_EBW1                          +00001196 IW00002       Ground Idle CT Table     
MBB145CB243                      EQU MB200MALF                       +00000035 LB00001       INSERT INV NO1 CONTROL CB POP
MBB145CB244                      EQU MB200MALF                       +00000036 LB00001       INSERT INV NO2 CONTROL CB POP
XNIADFAUD                       -EQU XCNB1                           +00004058 LB00001NV     INSTRUCTOR ADF AUD   
XNIMKRAUD                       -EQU XCNB1                           +00004059 LB00001NV     INSTRUCTOR MKR AUD      
XNIMKR1AUD                       EQU XCNB1                           +00004066 IB00001NV     INSTRUCTOR MKR BCN1 AUDIO
XNIMKR2AUD                       EQU XCNB1                           +00004067 IB00001NV     INSTRUCTOR MKR BCN1 AUDIO
XNIADF1AUD                       EQU XCNB1                           +00004058 LB00001NV     INSTRUCTOR ADF1 AUD    
XNIADF2AUD                       EQU XCNB1                           +00004059 LB00001NV     INSTRUCTOR ADF2 AUD   
ioinstptt                        EQU X_QDI                           +00000191 LB00001       Interbus DI-Instructor Radio PTT
EGENPRPMSEFF                     EQU X_EBW1                          +00001204 IW00002       Generator online effect on prop RPM
rwe_prpms_gen_eff                EQU X_EBW1                          +00001212 EW00002       Generator online effect on prop RPM
rwe_trqs_gen_eff                 EQU X_EBW1                          +00001220 EW00002       Generator online effect on TRQ 
EGENTRQSEFF                      EQU X_EBW1                          +00001228 IW00002       Generator online effect on TRQ 
EBGENONL                         EQU X_EBB1                          +00000145 IB00002       Generator online (Load present on Eng)
XNVRERS                          EQU X_EQIVDI                         +0000113 LB00001       CKPT VOICE RCDR ERASE SW
LSERSTONE                        EQU X_EQIVDO                         +0000114 LB00001       CVR ERASE TONE ON
LSTSTTONE                        EQU X_EQIVDO                         +0000115 LB00001       CVR TEST TONE ON
JSERSTONTIM                      EQU LBBUS                            +0000073 EW00001       TIMER FOR CVR ERASE TONE
JSERSHLD                         EQU LBBUS                            +0000077 EW00001       TIMER FOR CVR ERASE HOLD
UBOPICAL                         EQU X_UBB1                          +00000107 LB00001UB     OBS PRIVATE INTERPHONE
UBIPICAL                         EQU X_UBB1                          +00000108 LB00001UB     INST PRIVATE INTERPHONE
RCSPARE                          EQU FLY_MISC_BASE                   +00000496 EW00010       SPARE REAL 
NW_CASTOR_ANG_N1                 EQU FLY_MISC_BASE                   +00000536 EW00001       Nose Wheel Castor Angle N1 
QPATCMD                         -EQU XCQFP                           +******** IB00001       PATCH CONTROL COMMANDS                               ********0400 ********0401 ********0401
QPATID                          -EQU XCQFP                           +00000001 IB00001       PATCH CPU ID                                         ********0401 ********0402 ********0402
QPATBUF                         -EQU XCQFP                           +00000004 IW00256       PATCH X-FER BUFFER                                   ********0404 ********1428 ********1428
XCQFP                           -EQU DP                              +00000400 IB00001       CMPTRSYS: FORTRAN PATCH            286               ********0400 ********0401 ********0401
CZCONFGMGT                       EQU DP                              +00000400 CB01028       SIMULATOR SUBSYSTEM LOAD VERSIONS               
CZHOSTVER                        EQU CZCONFGMGT                      +******** CB00030       HOST LOAD VERSION
CZCLDGVER                        EQU CZCONFGMGT                      +00000030 CB00030       CONTROL LOADING LOAD VERSION
CZCISVER                         EQU CZCONFGMGT                      +00000060 CB00030       COCKPIT INTERFACE SYSTEM LOAD VERSION
CZMOTVER                         EQU CZCONFGMGT                      +00000090 CB00030       MOTION LOAD VERSION
CZVISVER                         EQU CZCONFGMGT                      +00000120 CB00030       VISUAL LOAD VERSION
CZSNDVER                         EQU CZCONFGMGT                      +00000150 CB00030       SOUND LOAD VERSION
FNSQUAT                          EQU ATSTBASE                        +00005261 LB00001       NOSE STRUT SQT SWIT TRIG
FLSQUAT                          EQU ATSTBASE                        +00005262 LB00001       L MAIN STRUT SQT SWIT TRIG
FRSQUAT                          EQU ATSTBASE                        +00005263 LB00001       R MAIN STRUT SQT SWIT TRIG
RC_PED_POS                       EQU CTLBUF                          +00017272 EW00001       Pedal Position for NWS       
RC_PED_STR                       EQU CTLBUF                          +00017276 EW00001       Pedal Stretch Pass Pedal Limit       
RC_PED_STRl                      EQU CTLBUF                          +00017280 EW00001       Pedal Stretch at Pedal Limit       
RC_PED_STRl                     -EQU CTLBUF                          +00017280 EW00001       Pedal Stretch at Pedal Limit
RC_PED_STRL                      EQU CTLBUF                          +00017280 EW00001       Pedal Stretch at Pedal Limit
XDIA224S1B                      +EQU locpp_cabinpressure             +00000169 LB00001DI     LEFT INST & ENVIR OFF POSITION
XDIA224S2B                      +EQU locpp_cabinpressure             +00000171 LB00001DI     RIGHT INST & ENVIR OFF POSITION
CLS_YAW_ACC                      EQU CLS_HOST_UNIQUE_F               +00000108 EW00001       Aircraft Yaw Acceleration
YAW_ACC                          EQU HOST_UNIQUE_F                   +00000108 EW00001       Aircraft Yaw Acceleration
VGSALT                           EQU VIS_BASE                        +00005280 EW00001       Visual Ground Segment Demanded CG Alt 
VGSDLAT                          EQU VIS_BASE                        +00005288 ED00001       Visual Ground Segment computed latitude
VGSDLON                          EQU VIS_BASE                        +00005296 ED00001       Visual Ground Segment computed longitude 
RC_DYNGOOSE                      EQU CTLBUF                          +00017284 EW00012       Dynamic Goose Term            
TCGOOSE                          EQU CTLBUF                          +00017332 IW00012       Dynamic Goose Term Table 
RCSCUFF                          EQU FLY_MISC_BASE                   +00000540 EW00001       Nose Wheel Tire Scuff Force 
TCSCUFF                          EQU FLY_MISC_BASE                   +00000544 IW00001       Nose Wheel Tire Scuff Force Table 
CLS_YFRSVP                       EQU CLS_HOST_UNIQUE_F               +00000112 EW00001       STAB AXIS TURN YAW/TRU A/S
YFRSVP                           EQU HOST_UNIQUE_F                   +00000112 EW00001       STAB AXIS TURN YAW/TRU A/S
io_latency_signal                EQU X_EBW1                          +00001236 EW00001       Signal used for Latency Testing
RCNWGSP                          EQU FLY_MISC_BASE                   +00000548 EW00001       Nose Wheel Speed Gain   
RC_CASTOR_ANG                    EQU FLY_MISC_BASE                   +00000552 EW00001       Nose Wheel Conditioned Castor Angel   
RC_CASTOR_VEL                    EQU FLY_MISC_BASE                   +00000556 EW00001       Nose Wheel Conditioned Castor Velocity 
iw_alt_alert_fine_knob           EQU X_ABW1                          +00001252 IW00001       Alt alerter fine tune knob pos     
iw_alt_alert_fine_knob_prev      EQU X_ABW1                          +00001256 IW00001       Alt alerter fine tune knob prev pos 
iw_alt_alert_large_knob          EQU X_ABW1                          +00001260 IW00001       Alt alerter large tune knob pos    
iw_alt_alert_large_knob_prev     EQU X_ABW1                          +00001264 IW00001       Alt alerter large tune knob prev pos      
iw_alt_thousands_inc             EQU X_ABW1                          +00001268 IW00001       Alt alerter thousands increment cond
iw_alt_thousands_dec             EQU X_ABW1                          +00001272 IW00001       Alt alerter thousands decrement cond 
iw_alt_fine_inc                  EQU X_ABW1                          +00001276 IW00001       Alt alerter fine decrement cond     
iw_alt_fine_dec                  EQU X_ABW1                          +00001280 IW00001       Alt alerter fine increment cond    
iw_alt_alert_fine_knob          -EQU X_ABW1                          +00001252 IW00001       Alt alerter fine tune knob pos                       ********6596 ********6600 ********6600
iw_alt_alert_fine_knob_prev     -EQU X_ABW1                          +00001256 IW00001       Alt alerter fine tune knob prev pos                  ********6600 ********6604 ********6604
iw_alt_alert_large_knob         -EQU X_ABW1                          +00001260 IW00001       Alt alerter large tune knob pos                      ********6604 ********6608 ********6608
iw_alt_alert_large_knob_prev    -EQU X_ABW1                          +00001264 IW00001       Alt alerter large tune knob prev pos                 ********6608 ********6612 ********6612
iw_alt_thousands_inc            -EQU X_ABW1                          +00001268 IW00001       Alt alerter thousands increment cond                 ********6612 ********6616 ********6616
iw_alt_thousands_dec            -EQU X_ABW1                          +00001272 IW00001       Alt alerter thousands decrement cond                 ********6616 ********6620 ********6620
iw_alt_fine_inc                 -EQU X_ABW1                          +00001276 IW00001       Alt alerter fine decrement cond                      ********6620 ********6624 ********6624
iw_alt_fine_dec                 -EQU X_ABW1                          +00001280 IW00001       Alt alerter fine increment cond        
XLB146CBALTAIRD                 -EQU XLBCB                           +00000016 LB00001       POP PILOTS ALTIMETER-AIR DATA CB
MBB146CBALTAIRD                 -EQU MB200MALF                       +00000013 LB00001       INSERT PILOT ALTM AIR DATA CB POP
XLB146CBPALTAIRD                 EQU XLBCB                           +00000016 LB00001       POP PILOTS ALTIMETER-AIR DATA CB
MBB146CBPALTAIRD                 EQU MB200MALF                       +00000013 LB00001       INSERT PILOT ALTM AIR DATA CB POP
lbaf_atg_rudbst_arm              EQU X_ABB1                          +00000448 LB00001       ATG rudder boost arm flag
rwaf_rudbst_force_gn             EQU X_ABW1                          +00001252 EW00001       Rudder boost force gain
LCBCBSTDBYHRZ                    EQU LCBCB                           +00000188 LB00001       STANDBY HORIZON BACKUP BATTERY CB
CLS_REPOS                        EQU CLS_HOST_UNIQUE_F               +00000116 LB00001       Host Repositioning/Trim In Progress
REPOS                            EQU HOST_UNIQUE_F                   +00000116 LB00001       Host Repositioning/Trim In Progress
rfmbxstr                         EQU CTLBUF                          +00017440 LB00020       Model Stretch ON when true  
ST_UNIQUE_C                  
CLS_SKID_PLATE                   EQU CLS_HOST_UNIQUE_C               +00000005 IB00001       NOSE WHEEL ON SKID PLATE 
SKID_PLATE                       EQU HOST_UNIQUE_C                   +00000005 IB00001       NOSE WHEEL ON SKID PLATE  
RC_SKPL                          EQU CTLBUF                          +00017460 EW00001       Nose Wheel Skid Plate Friction Gain 
FSETSKPL                         EQU FLY_MISC_BASE                   +00000560 LB00001       SET NOSE WHEEK ON SKID PLAGE 
FSETSKPL                        -EQU FLY_MISC_BASE                   +00000560 LB00001       SET NOSE WHEEK ON SKID PLAGE
FSETSKPL                         EQU FLY_MISC_BASE                   +00000560 LB00001       SET NOSE WHEEL ON SKID PLATE
LPCABCLIMBIND                    EQU locpp_cabinpressure             +00000021 LB00001       ENABLE CABIN CLIMB INDICATOR

PDUMPVALVE                      -EQU locpp_cabinpressure             +00000308 LB00001       CABIN AIR DUMP VALVE ENGAGED
PDUMPVALVE                       EQU locpp_cabinpressure             +00000203 LB00001       CABIN AIR DUMP VALVE ENGAGED


PNVRATE                          EQU locpp_cabinpressure             +00000308 EW00001       VALVE RATE FOR PNEUMATIC SHUTOFF VALVES

FTDLAT                           EQU ATSTBASE                        +00005268 ED00001       QTG GENERIC RUNWAY TOUCH DOWN LATITIDUE 
FTDLON                           EQU ATSTBASE                        +00005276 ED00001       QTG GENERIC RUNWAY TOUCH DOWN LONGITUDE
FTDHDG                           EQU ATSTBASE                        +00005284 EW00001       QTG GENERIC RUNWAY HEADING
KXCASMAX                         EQU KXARY                           +00000432 EW00001       CAPTAIN MAX INDICATED AIRSPEED                       
KXFASMAX                         EQU KXARY                           +00000436 EW00001       F/O MAX INDICATED AIRSPEED                           
EITT_ADJ                         EQU X_EBW1                          +00001240 IW00002       ITT adjustment table
EITTADJ                          EQU X_EBW1                          +00001248 EW00002       ITT adjustment offset
rwaf_gnd_trim_dn_force           EQU X_ABW1                          +00001264 EW00001       AP Ground Trim Dn Force Threshold
rwaf_gnd_trim_up_force           EQU X_ABW1                          +00001268 EW00001       AP Ground Trim Up Force Threshold
EPROPTSTDRPM                     EQU X_EBW1                          +00001256 IW00002       Eng Prop Test Delta RPM Drop Table
rwe_proptst_drpm                 EQU X_EBW1                          +00001264 EW00002       Eng Prop Test Delta RPM Drop
ETRQLOWPIT                       EQU X_EBW1                          +00001272 IW00001       Low Pitch Torque Table (1500 RPM)
rwe_trqlowpit                    EQU X_EBW1                          +00001276 EW00001       Low Pitch Torque (1500 RPM)
rwe_trqlowpit_err                EQU X_EBW1                          +00001280 EW00002       Low Pitch Torque Error (1500 RPM)     
rwaf_ap_vor_dev_cmd              EQU X_EBW1                          +00001272 EW00001       AP VOR beam deviation roll cmd component
rwaf_ap_ils_dev_cmd              EQU X_EBW1                          +00001276 EW00001       AP ILS beam deviation roll cmd component
rwaf_ap_vor_dev_cmd             -EQU X_EBW1                          +00001272 EW00001       AP VOR beam deviation roll cmd component  
rwaf_ap_ils_dev_cmd             -EQU X_EBW1                          +00001276 EW00001       AP ILS beam deviation roll cmd component 
rwaf_ap_vor_dev_cmd              EQU X_ABW1                          +00001272 EW00001       AP VOR beam deviation roll cmd component  
rwaf_ap_ils_dev_cmd              EQU X_ABW1                          +00001276 EW00001       AP ILS beam deviation roll cmd component 


rwaf_beamrate_ignore             EQU X_ABW1                          +00001280 EW00001       AP NAV Beam rate ignore value
rwaf_pitch_cmd_gn1               EQU X_ABW1                          +00001284 EW00001       AP Pitch Cmd Gain for Stick Drive
KXBATAMP                         EQU KXARY                           +00000440 EW00001       BATTERY AMP METER
KXDCVAM                         +EQU KXARY                           +00000108 EW00001       SELECTED DC BUS VOLT
LPOUTFLWREG                      EQU locpp_cabinpressure             +00000204 LB00001       OUTFLOW VALVE REGULATING MAX PSI DIFF
rwaf_fd_hdg_hld_qgn              EQU X_ABW1                          +00001288 EW00001       FD Heading Hold Q Gain
rwaf_ils_latbeam_dev_rcmd        EQU X_ABW1                          +00001292 EW00002       FD ILS Lateral Beam Dev Rcmd Component
rwaf_limited_piterr             -EQU X_ABW1                          +00000420 EW00001       AP limited pitch rate error 
rwaf_pitch_rate_limit           -EQU X_ABW1                          +00000424 EW00001       AP pitch rate limit          
rwaf_pitch_rate_error           -EQU X_ABW1                          +00000428 EW00001       AP pitch rate error term      
rwaf_pitch_rate_gn              -EQU X_ABW1                          +00000432 EW00001       AP pitch rate gain         
rwaf_pitctlcmd_dot_limit        -EQU X_ABW1                          +00000440 EW00001       AP Pitch Control Velocity Cmd Limit    
rwaf_pitctl_intgr_term           EQU X_ABW1                          +00000420 EW00001       AP pitch control integrator term
rwaf_pitctl_pro_term             EQU X_ABW1                          +00000424 EW00001       AP pitch control proportional term
rwaf_pitctl_pro_gn               EQU X_ABW1                          +00000428 EW00001       AP pitch control proportional gain
rwaf_pitctl_damp_term            EQU X_ABW1                          +00000432 EW00001       AP pitch control damping term
rwaf_pitctl_damp_gn              EQU X_ABW1                          +00000440 EW00001       AP Pitch Control damping gain
rwaf_pitctl_intgr_lead           EQU X_ABW1                          +00001300 EW00001       AP Pitch Control integrator max lead
RC_SKPL                          EQU CTLBUF                          +00017460 EW00001       Nose Wheel Skid Plate Friction Gain                  000017971252 000017971256 000018625784
RCSPARE                         +EQU CTLBUF                          +00017464 EW00010       SPARE REAL                                          
RCSCUFF                         +EQU CTLBUF                          +00017504 EW00001       Nose Wheel Tire Scuff Force                         
TCSCUFF                         +EQU CTLBUF                          +00017508 IW00001       Nose Wheel Tire Scuff Force Table                   
RCNWGSP                         +EQU CTLBUF                          +00017512 EW00001       Nose Wheel Speed Gain                               
RC_CASTOR_ANG                   +EQU CTLBUF                          +00017516 EW00001       Nose Wheel Conditioned Castor Angel                 
RC_CASTOR_VEL                   +EQU CTLBUF                          +00017520 EW00001       Nose Wheel Conditioned Castor Velocity              
rwaf_ac_ias_rate                 EQU X_ABW1                          +00001304 EW00001       AP aircraft IAS rate of chg (kts/sec)
rwaf_fd_vs_hld_pit_rate_gn       EQU X_ABW1                          +00001308 EW00001       FD VS hold pitch rate damping gain
rwaf_fd_ias_hld_pit_rate_gn      EQU X_ABW1                          +00001312 EW00001       FD IAS hold pitch rate damping gain
rwaf_ac_ias_hld_rate_gn          EQU X_ABW1                          +00001316 EW00001       FD IAS hold airspd rate damping gain
iwaf_loc_radalt_gn               EQU X_ABW1                          +00001320 IW00001       FD Localizer radalt gain program table
iwaf_vor_cap_envlp               EQU X_ABW1                          +00001324 IW00001       FD VOR Capture Envelope table
iwaf_gs_roc_targ                 EQU X_ABW1                          +00001328 IW00001       FD GS ROC target table
rwaf_ap_pit_fade_in_rate         EQU X_ABW1                          +00001332 EW00001       AP engagement pitch sync fade in rate
rwaf_ap_rol_fade_in_rate         EQU X_ABW1                          +00001336 EW00001       AP engagement roll sync fade in rate
lbaf_ap_pitch_synced             EQU X_ABB1                          +00000449 LB00001       AP engagement pitch sync complete flag
lbaf_ap_roll_synced              EQU X_ABB1                          +00000450 LB00001       AP engagement roll sync complete flag
lbaf_ap_eng                      EQU X_ABB1                          +00000451 LB00002       AP engaged flags 
rwaf_fd_gs_altrate_targ         -EQU X_ABW1                          +00001180 EW00001       GS deviation ROC target                              ********6524 ********6528 ********6528
rwaf_fd_gs_err_lfi_input         EQU X_ABW1                          +00001180 EW00001       GS deviation for lfi table
rwaf_fd_gs_altrate_targ          EQU X_ABW1                          +00001340 EW00002       GS deviation ROC target                              ********6524 ********6528 ********6528

rwaf_loc_on_crs                  EQU X_ABW1                          +00001348 EW00002       Localizer on course ramp in effect
rwaf_loc_ocrs_xwnd_adj           EQU X_ABW1                          +00001356 EW00002       ILS on crs crosswind adjustment term
rwaf_loc_ocrs_xwnd_gn            EQU X_ABW1                          +00001364 EW00001       ILS on crs crosswind adjustment gain
rwaf_lfi_input1                  EQU X_ABW1                          +00001368 EW00001       Auto-flight Generic LFI Input 1
EIWF                             EQU X_EBW1                          +00001288 EW00002       Indicated Fuel Flow
u1_fcs_ehsi_dev_src_type         EQU X_ABB1                          +00000453 IB00001       EHSI-1 deviation source type
u2_fcs_ehsi_dev_src_type         EQU X_ABB1                          +00000454 IB00001       EHSI-1 deviation source type
ibaf_fcs_dev_src_type            EQU X_ABB1                          +00000455 IB00002       FCS deviation source type
rwaf_lfi_input2                  EQU X_ABW1                          +00001372 EW00001       Auto-flight Generic LFI Input 2    
RAQFRZCLK                        EQU X_UBB1                          +00000112 EW00001UB     AURAL CUES FREEZE CLOCK                              000000067236 000000067237 000000067237
FQRL                             EQU FCTEWBS                         +00000220 EW00001F7      DYNAMIC PRESSURE(LB/FT**2) rate limited
FQLRL                            EQU FCTEWBS                         +00000224 EW00001F7      DYNAMIC PRESSURE(LB/FT**2) low rate lim 
FQHRL                            EQU FCTEWBS                         +00000228 EW00001F7      DYNAMIC PRESSURE(LB/FT**2) highrate lim 
ditt4gn                          EQU X_ABW1                          +00001376 EW00001       ditt4 gain 
ditt4gn                         -EQU X_ABW1                          +00001376 EW00001       ditt4 gain                          
ditt4gn                          EQU X_EBW1                          +00001296 EW00001       ditt4 gain                          
rwaf_rolctlcmd_dot               EQU X_ABW1                          +00001376 EW00001       Roll Servo Integrator dot 
rwaf_rolctl_intgn                EQU X_ABW1                          +00001380 EW00001       Roll Servo Integrator Gain 
rwaf_rolctl_intgr_term           EQU X_ABW1                          +00001384 EW00001       Roll Servo Integrator Term 
rwaf_rolctl_intgr_lead           EQU X_ABW1                          +00001388 EW00001       Roll Servo Integrator Max Lead Value
rwaf_rolctl_pro_gn               EQU X_ABW1                          +00001396 EW00001       Roll Servo Proportional Gain
rwaf_rolctl_pro_term             EQU X_ABW1                          +00001400 EW00001       Roll Servo Proportional Term
rwaf_pitctl_damp_gn              EQU X_ABW1                          +00001404 EW00001       Roll Servo Damping Gain
rwaf_rolctl_damp_term            EQU X_ABW1                          +00001408 EW00001       Roll Servo Damping Term

rwaf_rolctl_damp_gn              EQU X_ABW1                          +00001404 EW00001       Roll Servo Damping Gain       
rwaf_loc_xwnd_adjlim             EQU X_ABW1                          +00001412 EW00001       LOC Crosswind Adj Integrator Limit
TBATAMPANN                       EQU locfc_misc                      +00000364 EW00001       LOCAL
FDAP_RCMD                        EQU X_ABW1                          +00001416 EW00002       FD to AP Roll Cmd
FDAP_PCMD                        EQU X_ABW1                          +00001424 EW00002       FD to AP Pitch Cmd
rwaf_vor_ocrs_xwnd_gn            EQU X_ABW1                          +00001432 EW00001       VOR on-course crosswind adjustment gain
rwaf_vor_ocrs_xwnd_adj           EQU X_ABW1                          +00001436 EW00002       VOR on-course crosswind adjustment term
rwaf_vor_xwnd_adjlim             EQU X_ABW1                          +00001444 EW00001       VOR on-course crosswind adjust limit
rwaf_fd_sel_crs                  EQU X_ABW1                          +00001448 EW00002       FD Selected Course
rwaf_vor_dr_crs                  EQU X_ABW1                          +00001456 EW00002       VOR Dead Reckoning Course
rwaf_fd_dr_crs_gn                EQU X_ABW1                          +00001464 EW00001       VOR Dead Reckoning Course Adjust Gain
rwaf_ac_mag_hdg_rate             EQU X_ABW1                          +00001468 EW00001       Aircraft Heading Rate of Change
rwaf_max_vor_hdg_dot             EQU X_ABW1                          +00001472 EW00001       Max VOR on crs hdg rate of change

iwaf_locbc_radalt_gn             EQU X_ABW1                          +00001476 IW00001       Loc BC gain program table
TAMPADD                          EQU locfc_misc                      +00000368 EW00001       LOCAL  
lbaf_vor_wptranslk               EQU X_ABB1                          +00000457 LB00002       VOR Waypoint Transition Locked
lbpitctl_ftrim                   EQU X_ABB1                          +00000459 LB00001       IP Pitch Trim Wheel Align in Progress
lbaf_vor_cap_hdg_trk             EQU X_ABB1                          +00000460 LB00002       VOR Capture Heading/Course Track
rwaf_vor_cap_timer               EQU X_ABW1                          +00001480 EW00002       VOR Capture Timer
rwaf_vor_cap_timer_limit         EQU X_ABW1                          +00001488 EW00001       VOR Capture Timer Limit
rwaf_vor_crs_mem                 EQU X_ABW1                          +00001492 EW00002       VOR Locked Selected Course Memory
FVPLRL                           EQU FCTEWBS                         +00000232 EW00001F7      True airspeed (FT/sec**2) low rate lim    
FVPHRL                           EQU FCTEWBS                         +00000236 EW00001F7      True airspeed (FT/sec**2) highrate lim    
FVPKNLRL                         EQU FCTEWBS                         +00000240 EW00001F7      True airspeed (KNOTS) low rate lim        
FVPKNHRL                         EQU FCTEWBS                         +00000244 EW00001F7      True airspeed (KNOTS) highrate lim         
FVPRL                            EQU FCTEWBS                         +00000248 EW00001F7      True airspeed (FT/SEC) rate limited   
FVPKNRL                          EQU FCTEWBS                         +00000252 EW00001F7      True airspeed (KNOTS) rate limited   
UECSRESET                        EQU FLY_MISC_BASE                   +00000561 LB00001       IOS ECS Reset Flag
UECSPRSRST                       EQU FLY_MISC_BASE                   +00000562 LB00001       ECS Cabin Pressure Reset Flag
UECSTEMPRST                      EQU FLY_MISC_BASE                   +00000563 LB00001       ECS Cabin Temperature Reset Flag
FHPPREV                          EQU FLY_MISC_BASE                   +00000540 EW00001       ECS FHP previous pass value
ICABPRS_RST                      EQU FLY_MISC_BASE                   +00000544 IW00001       ECS Cabin pressure reset table
PCABPRS_RSTFT                    EQU FLY_MISC_BASE                   +00000548 EW00001       ECS Cabin pressure reset value in (ft)
PCABPRS_RSTPSI                   EQU FLY_MISC_BASE                   +00000552 EW00001       ECS Cabin pressure reset value in (psi)
POUTFLWVFAST                     EQU FLY_MISC_BASE                   +00000556 EW00001       ECS Outflow Valve Reposition Max Rate
POUTFLWVNORM                     EQU FLY_MISC_BASE                   +00000564 EW00001       ECS Outflow Valve Normal Max Rate
FPRPLX                           EQU F23EWBS                         +00000272 IW00001F2     PROP TIP X-MOMENT ARM
FPRPLY                           EQU F23EWBS                         +00000276 IW00001F2     PROP TIP Y-MOMENT ARM
FPRPLZ                           EQU F23EWBS                         +00000280 IW00001F2     PROP TIP Z-MOMENT ARM
FRPRPLX0                         EQU F01EWBS                         +00000600 EW00001       LX - PROP TIP
FRPRPLY0                         EQU F01EWBS                         +00000604 EW00001       LY - PROP TIP
FRPRPLZ0                         EQU F01EWBS                         +00000608 EW00001       LZ - PROP TIP

FPRPLX0                         -EQU F01EWBS                         +00000600 EW00001       LX - PROP TIP  
FPRPLY0                         -EQU F01EWBS                         +00000604 EW00001       LY - PROP TIP   
FPRPLZ0                         -EQU F01EWBS                         +00000608 EW00001       LZ - PROP TIP  
lb_atg_emerg_gr_tst              EQU ATSTBASE                        +00005288 LB00001       ATG EMERGENCY GEAR TEST IN PROG
FPRPLX0                          EQU F01EWBS                         +00000600 EW00001       LX - PROP TIP 
FPRPLY0                          EQU F01EWBS                         +00000604 EW00001       LY - PROP TIP 
FPRPLZ0                          EQU F01EWBS                         +00000608 EW00001       LZ - PROP TIP
FPRPLX                           EQU F01EWBS                         +00000612 EW00001       PROP TIP X-MOMENT ARM
FPRPLY                           EQU F01EWBS                         +00000616 EW00001       PROP TIP Y-MOMENT ARM
FPRPLZ                           EQU F01EWBS                         +00000620 EW00001       PROP TIP Z-MOMENT ARM
TVRATE                           EQU X_UBB1                          +00000116 IW00001UB     SCALE FACTOR GEN RESET      

lbaf_vor_fms_appr                EQU X_ABB1                          +00000462 LB00002       VOR or FMS Appr Selected
lbaf_gs_disp_on                  EQU X_ABB1                          +00000464 LB00002       Glideslope Data Displayed on EFIS
lbaf_fms_active                  EQU X_ABB1                          +00000466 LB00002       FMS Mode is active
lbaf_ap_faults_latched           EQU X_ABB1                          +00000468 LB00002       AP Faults Latched
lbaf_ahrs_attitude_valid         EQU X_ABB1                          +00000470 LB00002       AP AHRS Data Valid
lbaf_pitch_high_force            EQU X_ABB1                          +00000472 LB00002       AP High Forces Sensed on Pitch Servo
lbaf_pitch_mistrim_fault         EQU X_ABB1                          +00000474 LB00002       AP Pitch Mistrim Fault Detected
lbaf_ios_ap_fail                 EQU X_ABB1                          +00000476 LB00001       AP IOS Fail Flag
lbaf_ap_pitch_servo_fail         EQU X_ABB1                          +00000477 LB00001       AP Pitch Servo Fail Flag
lbaf_ap_roll_servo_fail          EQU X_ABB1                          +00000478 LB00001       AP Roll Servo Fail Flag
lbaf_ap_yaw_servo_fail           EQU X_ABB1                          +00000479 LB00001       AP Yaw Servo Fail Flag

rwaf_fcs_lat_dev_chg_timer       EQU X_ABW1                          +00001500 EW00002       Lateral deviation Change Timer
rwaf_fcs_act_lat_dev_prev        EQU X_ABW1                          +00001508 EW00002       Lateral deviation Last Pass
rwaf_lat_dev_avg_rate            EQU X_ABW1                          +00001516 EW00002       Lateral deviation avg rate of change
rwaf_pitch_mistrim_timer         EQU X_ABW1                          +00001524 EW00002       Pitch Mistrim Timer
rwaf_fms_appr_lat_dev_gn         EQU X_ABW1                          +00001532 EW00001       FMS Appr Lateral Deviation Gain
lbaf_apservo_cbpwr               EQU X_ABB1                          +00000434 LB00001       AP Servo CB Power Flag        
lbaf_ap_man_trim_fault           EQU X_ABB1                          +00000480 LB00002       AP Manual Trim during AP Op Fault
rwaf_ele_trim_timer              EQU X_ABW1                          +00001536 EW00001       AP Elevator Trim Timer
lbaf_ap_man_trim_fault           EQU X_ABB1                          +00000480 LB00002       AP Manual Trim during AP Op Fault 
lbaf_trimsw_fail                 EQU X_ABB1                          +00000482 LB00002       AP Trim Servo Invalid Input
rwaf_yd_eng_timer                EQU X_ABW1                          +00001540 EW00001       YD Engaged Timer
rwaf_trimsw_fail_timer           EQU X_ABW1                          +00001544 EW00001       Trim Servo Invalid Input Timer 
rwaf_fms_appr_xwnd_gn            EQU X_ABW1                          +00001548 EW00001       FMS Appr Crosswind Correction Gain
rwaf_fms_appr_beam_rate_gn       EQU X_ABW1                          +00001552 EW00001       FMS Appr Beam Rate Gain
rwaf_fms_beam_rate_term          EQU X_ABW1                          +00001556 EW00002       FMS Appr Beam Rate Term        
rwaf_trimsw_fail_timer          -EQU X_ABW1                          +00001544 EW00001       Trim Servo Invalid Input Timer              
rwaf_trimsw_fail_timer           EQU X_ABW1                          +00001544 EW00002       Trim Servo Invalid Input Timer              
rwaf_fms_appr_xwnd_gn           -EQU X_ABW1                          +00001548 EW00001       FMS Appr Crosswind Correction Gain    
rwaf_fms_appr_xwnd_gn            EQU X_ABW1                          +00001564 EW00001       FMS Appr Crosswind Correction Gain    
XKFTURN                          EQU X_QAO1                          +00001028 EW00001       F/O TURN RATE INDICATOR                              
XKCTURN                          EQU X_QAO1                          +00001032 EW00001       CAPTAIN TURN RATE INDICATOR                          
rwaf_trim_dmd_fail               EQU X_ABW1                          +00001568 EW00002       Trim demanded but no motion fail timer
lbaf_trim_dmd_fail               EQU X_ABB1                          +00000484 LB00002       AP Trim Demanded but no motion failure
rwe_engoff_prop_decelrate        EQU X_EBW1                          +00001300 EW00001       Eng Off (not feathered) prop decel rate
XLSTBYADILGT                     EQU LBBUSA                          +00000084 LB00001       STANDBY ADI BACK-LIGHT
CLS_NOSE_OFFSET                  EQU CLS_HOST_UNIQUE_F               +00000124 EW00001       Flt test nose angle offset
NOSE_OFFSET                      EQU HOST_UNIQUE_F                   +00000124 EW00001       Flt test nose angle offset
rwe_engoff_prop_decelrate       -EQU X_EBW1                          +00001300 EW00001       Eng Off (not feathered) prop decel rate 
rwe_propfreespin_tau             EQU X_EBW1                          +00001300 EW00002       Free Spinning Prop Time Constant
PROPFREESPIN                     EQU X_EBW1                          +00001308 IW00002       Free Spinning Prop Time Constant Table
rwe_atg_starter_voltage          EQU X_EBW1                          +00001316 EW00002       ATG Engine Starter Voltage
DFASCORR                         EQU loctab_dpd                      +00003140 IW00001       Corrected Airspeed 
DFASCORR                        -EQU loctab_dpd                      +00003140 IW00001       Corrected Airspeed                
DFASCORR                         EQU loctab_dpd                      +00003140 IW00001       Idicated Airspeed Correction Factor
RCREPOSD                         EQU CTLBUF                          +00017524 EW00006       Reposition damping constant
EVPKN                            EQU X_EBW1                          +00001324 EW00001       Eng True Airspeed Environment Var
EN2_WINDMILL                     EQU X_EBW1                          +00001328 EW00002       N2 Steady State Windmilling RPM Value
EN2_FTHRSTRT                     EQU X_EBW1                          +00001336 IW00002       N2 Feathered Start RPM Table
DKIO                            +EQU loctab_dpd                      +00002692 IW00120ta     DC94 to DC95 I/O Conversion Tables                   
TMSPTERR                        +EQU loctab_dpd                      +00003172 IW00001       terrain around msp                                   
TMSPWEST                        +EQU loctab_dpd                      +00003176 IW00001       msp west boundary                                    
TMSPEAST                        +EQU loctab_dpd                      +00003180 IW00001       msp east boundary                                    
DN1303T                         +EQU loctab_dpd                      +00003184 IW00001nv     adf1band-3 freq decode                               
DN1304T                         +EQU loctab_dpd                      +00003188 IW00001nv     adf2band-1 freq decode                               
DN1305T                         +EQU loctab_dpd                      +00003192 IW00001nv     adf2band-2 freq decode                               
DN1306T                         +EQU loctab_dpd                      +00003196 IW00001nv     adf2band-3 freq decode                               
DK0529T                         +EQU loctab_dpd                      +00003200 IW00001       dc volt/amp meter volts table                        
DK0530T                         +EQU loctab_dpd                      +00003204 IW00001       dc volt/amp meter amps  table                        
DK05RATS                        +EQU loctab_dpd                      +00003208 IW00001       stby ram air temp                                    
DF1302A                         +EQU loctab_dpd                      +00003212 IW00002       ly lt & rt aux tanks (in.)                           
DFASCORR                        +EQU loctab_dpd                      +00003220 IW00001       Idicated Airspeed Correction Factor                  
MBB146CBARLWRN                   EQU MB200MALF                       +00000037 LB00001       INSERT AURAL WARNING CB POP
RRAIRPRT                        -EQU SM_ATIS                         +00000060 EW00001       ATIS AIRPORT NAME CODE                       
RRUNWAYN1                       -EQU SM_ATIS                         +00000064 EW00001       ATIS RUNWAY 1st NUMBER                        
RRUNWAYN2                       -EQU SM_ATIS                         +00000068 EW00001       ATIS RUNWAY 2nd NUMBER                         
RRUNWAYLR                       -EQU SM_ATIS                         +00000072 EW00001       ATIS RUNWAY L/R CODE                            
RRUNWAYT                        -EQU SM_ATIS                         +00000076 EW00001AQ     ATIS RUNWAY APP/DEP                              
TNGSDSF                          EQU SM_ATIS                         +00000084 EW00001NV     GLIDESLOPE DEVIATION SCALE FACTOR                 
rrairprt                         EQU SM_ATIS                         +00000088 EW00004NV     ATIS AIRPORT INDEX NO TEXT-SPEECH
radio_gain                       EQU SM_ATIS                         +00000108 EW00004NV     ATIS RADIO GAIN
krunwayn1                        EQU SM_ATIS                         +00000128 IB00004NV     ATIS RUNWAY NUMBER 
krunwayn2                        EQU SM_ATIS                         +00000132 IB00004NV     ATIS RUNWAY NUMBER 
krunwaylr                        EQU SM_ATIS                         +00000136 IB00004NV     ATIS RUNWAY Left/Right 
kratisbit2                       EQU SM_ATIS                         +00000140 IB00004NV     ATIS File Number       
kratisbit1                       EQU SM_ATIS                         +00000144 IB00004NV     ATIS File Number       
uatis_freq                       EQU SM_ATIS                         +00000148 IB00004NV     ATIS Frequency Selected
icao_found                       EQU SM_ATIS                         +00000060 IB00004       ATIS AIRPORT ICAO FOUND  
RRATISBIT1                      -EQU NAV_COMM                        +00000112 EW00001AQ     ATIS FILE NAME LSB                                   000000613456 000000613460 000000613544
RRATISBIT2                      -EQU NAV_COMM                        +00000116 EW00001AQ     ATIS FILE NAME MSB                                   000000613460 000000613464 000000613544



uatis_freq                      -EQU SM_ATIS                         +00000148 IB00004NV     ATIS Frequency Selected                              000000691960 000000691964 000000693412
uatis_freq                       EQU SM_ATIS                         +00000148 EW00004NV     ATIS Frequency Selected                              000000691960 000000691964 000000693412

RRUNWAYT                         EQU SM_ATIS                         +00000076 EW00001AQ     ATIS RUNWAY DEPART ARRIVE TYPE
radio_freq                       EQU SM_ATIS                         +00000168 EW00004NV     Radio Frequency Selected  
RSAXFRAQ                        -EQU                                 +00611344 EW00500AQ     SIMPHONICS AURAL CUE TRANSFER ARRAY                  000000611344 000000613344 000000
RSAXFRAQ                         EQU                                 +00610944 EW00600AQ     SIMPHONICS AURAL CUE TRANSFER ARRAY                  000000611344 000000613344 000000
XCB136S150                      -EQU LBBUS                           +00000062 LB00001       PROP SYNC CONTROL SW
XCB223S172                       EQU LBBUS                           +00000062 LB00001       BRAKE DEICE CONTROL SW
XKELEVATORTRIMSPEED              EQU X_QSO2                          +00000172 EW00001       ELEVATOR TRIM MOTOR SPEED CONTROL                    
XLAPTRIMFAIL                     EQU LTGBASE                         +00000229 LB00001       B200 A/P TRIM FAIL ANNUN
XLAPFAIL                         EQU LTGBASE                         +00000230 LB00001       B200 AUTO PILOT FAIL ANNUN
LCB225CB193                      EQU LCBCB                           +00000189 LB00001       WINDSHIELD ANTI-ICE CTL - CO-PILOT CB
LCB225CB194                      EQU LCBCB                           +00000190 LB00001       WINDSHIELD ANTI-ICE CTL - PILOT CB
lbhy_fluid_sensor_test          +EQU lochg_gear_fly                  +00000144 LB00001       HYD FLUID SENSOR TEST BUTTON                         
iwhy_fluid_sensor_test_timer     EQU lochg_gear_fly                  +00000140 IW00001       HYD FLUID SENSOR TEST TIMER                          
iwhy_fluid_sensor_test_timer     EQU lochg_gear_fly                  -00000140 IW00001       HYD FLUID SENSOR TEST TIMER                          000000654180 000000654184 000000654440
ewhy_fluid_sensor_test_timer     EQU lochg_gear_fly                   00000140 EW00001       HYD FLUID SENSOR TEST TIMER                          000000654180 000000654184 000000654440
iwhy_fluid_sensor_test_timer    -EQU lochg_gear_fly                  +00000140 IW00001       HYD FLUID SENSOR TEST TIMER                          
LLFOIMANN                       -EQU LTGBASE                         +00000231 LB00001       B200 F/O INNER MRR ANNUN INTERMEDIATE
LLFOMMANN                       -EQU LTGBASE                         +00000232 LB00001       B200 F/O MIDDLE MRR ANNUN INTERMEDIATE
LLFOOMANN                       -EQU LTGBASE                         +00000233 LB00001       B200 F/O OUTER MRR ANNUN INTERMEDIATE
LLSANODHANN                     -EQU LTGBASE                         +00000234 LB00001       B200 DECISION HEIGHT ANNUN INTERMEDIATE
XLFOIMANN                        EQU LTGBASE                         +00000231 LB00001       B200 F/O INNER MRR ANNUN INTERMEDIATE
XLFOMMANN                        EQU LTGBASE                         +00000232 LB00001       B200 F/O MIDDLE MRR ANNUN INTERMEDIATE
XLFOOMANN                        EQU LTGBASE                         +00000233 LB00001       B200 F/O OUTER MRR ANNUN INTERMEDIATE
XLSANODHANN                      EQU LTGBASE                         +00000234 LB00001       B200 DECISION HEIGHT ANNUN INTERMEDIATE
XCBLPWRLIFT                      EQU LBBUS                           +00000087 LB00001       LEFT POWER LEVER MICRO SWITCH
XCBRPWRLIFT                      EQU LBBUS                           +00000088 LB00001       RIGHT POWER LEVER MICRO SWITCH
MBLCURLIMF                       EQU MB200MALF                       +00000038 LB00001       INSERT RIGHT CURRENT LIMITER FAILURE
MBRCURLIMF                       EQU MB200MALF                       +00000039 LB00001       INSERT RIGHT CURRENT LIMITER FAILURE
iolcabinlightswfull              EQU X_QDI                           +00000389 LB00001       Interbus DI-CABIN LIGHT SWITCH FULL                  000000646513 000000646514 000000646636
ioencodedaltselect               EQU X_QDI                           +00000390 LB00001       Interbus DI-Encoded altimeter select sw              
iomarkerbeaconselectsw           EQU X_QDI                           +00000391 LB00001       Interbus DI-Marker beacon ant select sw              
rwn_copilot_baro_knob            EQU X_ABW1                          +00001576 EW00001       Copilot selected baro knob  fail timer               
RC_BKDR                          EQU CTLBUF                          +00017560 EW00006       Backdrive Active Word
rwn_ele_trim_rate_tau            EQU                                 +00708900 EW00001       trim rate lag tau
rwn_ele_trim_ictl_lim            EQU                                 +00708904 EW00001       trim rate io integral limit
PREGPRSTAU                       EQU                                 +00708908 EW00001       Pressure Regulator Time Const         
PREGVRATE                        EQU                                 +00708912 EW00001       Pressure Regulator Valve Rate          
VSTOP                            EQU XCVB1                           +00001795 IB00001       STOP BAR LIGHT INTENSITY                             000000088187 000000088188 000000088188
USTOPD                           EQU XCVB1                           +00001796 IB00001       DEMANDED STOP BAR LIGHT INTENSITY                    000000088188 000000088189 000000088189
USTOPAK                          EQU XCVB1                           +00001797 IB00001       ACKNOWLEDGED STOP BAR LIGHT INTENSITY                000000088189 000000088190 000000088190
USTOPF                           EQU XCVB1                           +00001798 IB00001       STOP BAR LIGHT INTENSITY FLAG                        000000088190 000000088191 000000088191
@


1.2
log
@empty message
@
text
@d2812 4
@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@d2810 2
@
