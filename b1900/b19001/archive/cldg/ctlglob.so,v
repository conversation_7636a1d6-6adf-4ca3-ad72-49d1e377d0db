head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.43.15;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@/* 
C&    INTEGER*1     AFT_JAM(12)                             ! CLS aft quadrant jam              
C&    REAL*4        AF_SPARE(10)                            ! AF Spares                         
C&    REAL*4        AIR_SPEED                               ! CLS airspeed                      
C&    REAL*4        APG                                     ! PITCH ACCEL FOR GRAD              
C&    LOGICAL*1     APPITENG                                ! A/P PITCH CH. ENGAGED             
C&    LOGICAL*1     APROLENG                                ! A/P ROLL CH. ENGAGED              
C&    REAL*4        APSTKCMD                                ! A/P STICK COMMAND OUTPUT          
C&    REAL*4        APWHLCMD                                ! A/P WHEEL COMMAND OUTPUT          
C&    LOGICAL*1     APYDE                                   ! YAW DAMPER ENGAGED                
C&    REAL*4        AP_CMD(12)                              ! CLS autopilot command             
C&    INTEGER*1     AP_ENG(12)                              ! CLS Autopilot Engage (1=A/P, 2=CWS
C&    REAL*4        ARG                                     ! ROLL ACCEL FOR GRAD               
C&    REAL*4        AXPBWG                                  ! X ACCEL FORCE GRAD (Long)         
C&    REAL*4        AXRBWG                                  ! X ACCEL FORCE GRAD (Lat)          
C&    REAL*4        AX_ACC                                  ! CLS cockpit x accel (g)           
C&    REAL*4        AYG                                     ! YAW ACCEL FOR GRAD                
C&    REAL*4        AYPBWG                                  ! Y ACCEL FORCE GRAD (Long)         
C&    REAL*4        AYRBWG                                  ! Y ACCEL FORCE GRAD (Lat)          
C&    REAL*4        AY_ACC                                  ! CLS cockpit Y accel (g)           
C&    REAL*4        AZPBWG                                  ! Z ACCEL FORCE GRAD (Long)         
C&    REAL*4        AZRBWG                                  ! Z ACCEL FORCE GRAD (Lat)          
C&    REAL*4        AZ_ACC                                  ! CLS cockpit z accel (g)           
C&    REAL*4        BKDR_CMD(12)                            ! CLS backdrive position command    
C&    REAL*4        BKDR_FOR_LIMIT(12)                      ! CLS backdrive force limit         
C&    INTEGER*4     CLDGXFER_BASE(1000)                     ! CLDG (REAL FEEL) RFM XFER BASE    
C&    REAL*4        CLS_AFT_FOR(4)                          ! TOTAL AFT MASS FORCE              
C&    INTEGER*1     CLS_AFT_JAM(12)                         ! CLS aft quad jam                  
C&    REAL*4        CLS_AFT_POS(12)                         ! CLS aft quadrant pos              
C&    REAL*4        CLS_AFT_VEL(12)                         ! CLS aft quadrant vel              
C&    REAL*4        CLS_AIR_SPEED                           ! CLS airspeed                      
C&    REAL*4        CLS_AP_CMD(12)                          ! CLS autopilot command             
C&    INTEGER*1     CLS_AP_ENG(12)                          ! CLS Autopilot Engage (1=A/P, 2=CWS
C&    REAL*4        CLS_AP_STQ(3)                           ! A/P SERVO TORQUE                  
C&    REAL*4        CLS_AX                                  ! CLS cockpit x accel (g)           
C&    REAL*4        CLS_AY                                  ! CLS cockpit Y accel (g)           
C&    REAL*4        CLS_AZ                                  ! CLS cockpit z accel (g)           
C&    REAL*4        CLS_BKDR_CMD(12)                        ! CLS backdrive position command    
C&    REAL*4        CLS_BKDR_FOR_LIMIT(12)                  ! CLS backdrive force limit         
C&    INTEGER*1     CLS_CWS_ENG(12)                         ! CLS CWS Active 1=Active           
C&    REAL*4        CLS_DAMP(12)                            ! CLS aero damping coef             
C&    INTEGER*1     CLS_DORT_ON                             ! CLS dort on flag                  
C&    INTEGER*1     CLS_DORT_STAT                           ! CLS dort status                   
C&    INTEGER*4     CLS_FAULT(12)                           ! CLS fault                         
C&    REAL*4        CLS_FEEL_PRESS(12)                      ! CLS feel pressure                 
C&    REAL*4        CLS_FGRAD(12)                           ! CLS aero force grad               
C&    REAL*4        CLS_FLAP                                ! Avg Flap Position                 
C&    REAL*4        CLS_FOFF(12)                            ! CLS aero force offset             
C&    REAL*4        CLS_FPC(12)                             ! CLS force                         
C&    INTEGER*1     CLS_FWD_JAM(12)                         ! CLS fwd quad jam                  
C&    INTEGER*1     CLS_GENSIM_C(700)                       ! CLS T0 HOST generic sim buffer fla
C&    REAL*4        CLS_GENSIM_F(175)                       ! CLS T0 HOST generic sim buffer flo
C&    INTEGER*4     CLS_GENSIM_I(175)                       ! CLS T0 HOST generic sim buffer int
C&    INTEGER*4     CLS_HOST_CT                             ! CLS host return count             
C&    INTEGER*1     CLS_HOST_GENSIM_C(700)                  ! HOST T0 CLS generic sim buffer fla
C&    REAL*4        CLS_HOST_GENSIM_F(175)                  ! HOST T0 CLS generic sim buffer flo
C&    INTEGER*4     CLS_HOST_GENSIM_I(175)                  ! HOST T0 CLS generic sim buffer int
C&    INTEGER*1     CLS_HOST_STD_C(400)                     ! HOST T0 CLS std buffer flag       
C&    REAL*4        CLS_HOST_STD_F(100)                     ! HOST T0 CLS std buffer float      
C&    INTEGER*4     CLS_HOST_STD_I(100)                     ! HOST T0 CLS std buffer integer    
C&    INTEGER*1     CLS_HOST_UNIQUE_C(400)                  ! HOST T0 CLS unique buffer flag    
C&    REAL*4        CLS_HOST_UNIQUE_F(100)                  ! HOST T0 CLS unique buffer float   
C&    INTEGER*4     CLS_HOST_UNIQUE_I(100)                  ! HOST T0 CLS unique buffer integer 
C&    REAL*4        CLS_H_AGL                               ! height above ground               
C&    INTEGER*4     CLS_INPUT_TMP(400)                      ! CLS inter buffer base             
C&    REAL*4        CLS_LATENCY_IN                          ! CLS latency input test variable   
C&    INTEGER*1     CLS_LATENCY_OUT                         ! CLS latency output test flag      
C&    REAL*4        CLS_MACH_NO                             ! CLS Mach Number                   
C&    INTEGER*1     CLS_MODE(12)                            ! CLS mode                          
C&    INTEGER*1     CLS_MODE_CMD(12)                        ! CLS mode command                  
C&    REAL*4        CLS_NOSE_OFFSET                         ! Flt test nose angle offset        
C&    REAL*4        CLS_NW_CASTOR_ANG                       ! NOSEWHEEL CASTOR ANGLE            
C&    REAL*4        CLS_NW_CASTOR_GAIN                      ! NOSEWHEEL CASTOR GAIN             
C&    REAL*4        CLS_NW_CASTOR_VEL                       ! NOSEWHEEL CASTOR VELOCITY         
C&    REAL*4        CLS_NW_FOR                              ! NOSE WHEEL CASTERING FORCE        
C&    REAL*4        CLS_NW_LIM                              ! NOSEWHEEL LIMIT (PEDAL UNITS - INC
C&    REAL*4        CLS_NW_SPEED                            ! NOSEWHEEL SPEED                   
C&    INTEGER*1     CLS_ON_OFF(12)                          ! CLS on off flag                   
C&    INTEGER*1     CLS_ON_OFF_CMD(12)                      ! CLS on off flag command           
C&    INTEGER*1     CLS_PARK_BRK                            ! PARKING BRAKE FLAG                
C&    REAL*4        CLS_PED_STEER                           ! CLS pedal steering                
C&    REAL*4        CLS_PITCH_ACC                           ! AIRCRAFT PITCH ACCELERATION       
C&    REAL*4        CLS_RAMVM(12)                           ! CLS aft quadrant vel              
C&    REAL*4        CLS_RAMXM(12)                           ! CLS aft quadrant pos              
C&    LOGICAL*1     CLS_RBDFASTOFF(3)                       ! fast force release rate enable    
C&    REAL*4        CLS_RBDFBD(6)                           ! backdrive force                   
C&    REAL*4        CLS_RBDGFASTOFF(3)                      ! fast force release rate           
C&    REAL*4        CLS_RCAENCX(6)                          ! loader encoder position in loader 
C&    REAL*4        CLS_RCAFC(6)                            ! loader force in control units     
C&    REAL*4        CLS_RCAFL(6)                            ! loader force in loader units      
C&    REAL*4        CLS_RCAXC(6)                            ! loader position in control units  
C&    REAL*4        CLS_RCAXL(6)                            ! loader position in loader units   
C&    REAL*4        CLS_RDFORCE                             ! digital force measure output      
C&    REAL*4        CLS_RDLASERDIST                         ! digital laser dist output         
C&    REAL*4        CLS_RDPROTRACTOR                        ! digital protractor output         
C&    LOGICAL*1     CLS_REPOS                               ! Host Repositioning/Trim In Progres
C&    INTEGER*1     CLS_RESET_CMD(12)                       ! CLS reset command                 
C&    INTEGER*4     CLS_RFC_CT                              ! CLS return count                  
C&    REAL*4        CLS_RFMFP(6)                            ! Fwd mass pilot applied force      
C&    REAL*4        CLS_RFMFPC(12)                          ! control force                     
C&    REAL*4        CLS_RFMVM(6)                            ! CONTROL VELOCITY                  
C&    REAL*4        CLS_RFMXC(12)                           ! control position                  
C&    REAL*4        CLS_RFMXM(6)                            ! Forward mass position             
C&    REAL*4        CLS_ROLL_ACC                            ! AIRCRAFT ROLL ACCELERATION        
C&    REAL*4        CLS_RUD_BOOST_FOR                       ! Rudder Boost Force From Autopilot 
C&    INTEGER*1     CLS_SKID_PLATE                          ! NOSE WHEEL ON SKID PLATE          
C&    INTEGER*1     CLS_STD_C(400)                          ! CLS T0 HOST std buffer flag       
C&    REAL*4        CLS_STD_F(100)                          ! CLS T0 HOST std buffer float      
C&    INTEGER*4     CLS_STD_I(100)                          ! CLS T0 HOST std buffer integer    
C&    REAL*4        CLS_STICK_PUSHER                        ! Longitudinal Stick Pusher         
C&    REAL*4        CLS_SURF_ALPHA(3)                       ! SURFACE ANGLE OF ATTACK           
C&    REAL*4        CLS_SURF_AS_INV(3)                      ! SURFACE INVERSE TRUE AIRSPEED     
C&    REAL*4        CLS_SURF_POS(8)                         ! SURFACE POSITION                  
C&    REAL*4        CLS_SURF_Q(3)                           ! SURFACE DYNAMIC PRESSURE          
C&    REAL*4        CLS_SURF_VEL(8)                         ! SURFACE VELOCITY                  
C&    INTEGER*1     CLS_TOW_ENG                             ! NOSE WHEEL TOW ENGAGED            
C&    REAL*4        CLS_TOW_POS                             ! NOSE WHEEL TOE POSITION           
C&    INTEGER*1     CLS_TRM_REL_SW(12)                      ! CLS trim release sw               
C&    INTEGER*1     CLS_TRM_RST(3)                          ! TRIM RESET                        
C&    INTEGER*1     CLS_UNIQUE_C(400)                       ! CLS T0 HOST unique buffer flag    
C&    REAL*4        CLS_UNIQUE_F(100)                       ! CLS T0 HOST unique buffer float   
C&    INTEGER*4     CLS_UNIQUE_I(100)                       ! CLS T0 HOST unique buffer integer 
C&    REAL*4        CLS_VC(6)                               ! CONTROL VELOCITY                  
C&    REAL*4        CLS_XC(12)                              ! CLS position                      
C&    REAL*4        CLS_XC_IN(6)                            ! CONTROL POSITION, INCHES          
C&    REAL*4        CLS_XTRM(12)                            ! CLS trim position                 
C&    REAL*4        CLS_XTRM_CMD(12)                        ! CLS trim command                  
C&    REAL*4        CLS_YAW_ACC                             ! Aircraft Yaw Acceleration         
C&    REAL*4        CLS_YFRSVP                              ! STAB AXIS TURN YAW/TRU A/S        
C&    REAL*4        CTLBUF(6000)                            ! controls misc base                
C&    INTEGER*4     CTL_PXFER_BASE(1000)                    ! Controls phy xfer base            
C&    INTEGER*4     CTL_PXFER_IN(400)                       ! Controls phy xfer output int      
C&    INTEGER*4     CTL_PXFER_OUT(400)                      ! Controls phy xfer output int      
C&    REAL*4        DAMP(12)                                ! CLS aero damping coef             
C&    INTEGER*1     DORT_ON                                 ! CLS dort on flag                  
C&    REAL*8        DZDRTSK(10)                             ! ACTUAL TASK RATE SEC              
C&    REAL*8        DZDRTSKMAX(10)                          ! MIN ACTUAL TASK RATE SEC          
C&    REAL*8        DZDRTSKMIN(10)                          ! MAX ACTUAL TASK RATE SEC          
C&    REAL*8        DZRTSKTM(10)                            ! TASK TIME SEC                     
C&    REAL*8        DZRTSKTMMAX(10)                         ! MAX TASK TIME SEC                 
C&    REAL*8        DZRTSKTMMIN(10)                         ! MIN TASK TIME SEC                 
C&    REAL*4        FACTAIL                                 ! ACTUAL  TOTAL  AILERON            
C&    REAL*4        FACTELEV                                ! ACTUAL ELEVATOR POSITION          
C&    REAL*4        FACTFLAP                                ! ACTUAL FLAP POS                   
C&    REAL*4        FACTPFOR                                ! ACTUAL STRETCHED PEDAL FORCE      
C&    REAL*4        FACTPPOS                                ! ACTUAL UNSTRETCHED PEDAL POS      
C&    REAL*4        FACTRUDD                                ! ACTUAL RUDDER POSITION            
C&    REAL*4        FACTSFOR                                ! ACTUAL STRETCHED STAB TRIM WHEEL F
C&    REAL*4        FACTSPOS                                ! ACTUAL STICK POS                  
C&    REAL*4        FACTSPP                                 ! ACTUAL STRETCHED PEDAL POS        
C&    REAL*4        FACTSSP                                 ! ACTUAL STRETCHED STICK POS        
C&    REAL*4        FACTSTAB                                ! ACTUAL STABIIZER POS              
C&    REAL*4        FACTSWP                                 ! ACTUAL STRETCHED WHEEL POS        
C&    REAL*4        FACTTBFL                                ! ACTUAL STRETCHED LT TOE BRAKE FORC
C&    REAL*4        FACTTBFR                                ! ACTUAL STRETCHED RT TOE BRAKE FORC
C&    REAL*4        FACTTOEB                                ! ACTUAL STRETCHED LEFT TOE BRAKE PO
C&    REAL*4        FACTTOER                                ! ACTUAL STRETCHED RIGHT TOE BRAKE P
C&    REAL*4        FACTWFOR                                ! ACTUAL WHEEL FORCE                
C&    REAL*4        FACTWPOS                                ! ACTUAL WHEEL POSITION             
C&    REAL*4        FALPD                                   ! A/C ANGLE OF ATTACK  (DEG)        
C&    REAL*4        FAXA                                    ! X  A/C AXIS ACCEL    (G'S)        
C&    REAL*4        FAYA                                    ! Y  A/C AXIS ACCEL    (G'S)        
C&    REAL*4        FBETAD                                  ! SIDESLIP ANGLE(BETA) (DEG)        
C&    LOGICAL*1     FBLCONT                                 ! CTLS LAT. HDSHK FLAG (ATG ON)     
C&    LOGICAL*1     FBPUSHBK                                ! PUSHBACK IN PROGRESS              
C&    REAL*4        FDEMNW                                  ! DEMANDED NOSE WHEEL POSITION      
C&    REAL*4        FDEMPDL                                 ! DEMANDED PEDAL POSITION           
C&    REAL*4        FDEMSTK                                 ! DEMANDED STICK POSITION           
C&    REAL*4        FDEMTOEB                                ! LEFT DEMANDED TOE BRAKE           
C&    REAL*4        FDEMTOER                                ! RIGHT DEMANDED RIGHT TOE BRAKE    
C&    REAL*4        FDEMWHL                                 ! DEMANDED WHEEL POSITION           
C&    REAL*4        FDPADEG                                 ! ROLL ACCEL      (DEG/S**2)        
C&    REAL*4        FDQADEG                                 ! PITCH ACCEL     (DEG/S**2)        
C&    REAL*4        FDRADEG                                 ! YAW ACCEL       (DEG/S**2)        
C&    REAL*4        FEEL_PRESS(12)                          ! CLS feel pressure                 
C&    REAL*4        FGRAD(12)                               ! CLS aero force grad               
C&    LOGICAL*1     FIPACTIV                                ! IP ACTIVE                         
C&    REAL*4        FLAP                                    ! Avg Flap Position                 
C&    LOGICAL*1     FLBWO                                   ! LEFT  TIRE ON GROUND              
C&    REAL*4        FMAXPFOR                                ! MAX PEDAL FORCE                   
C&    REAL*4        FMAXSFOR                                ! MAX STICK FORCE                   
C&    REAL*4        FMAXWFOR                                ! MAX WHEEL FORCE                   
C&    REAL*4        FNAZA                                   ! NEGATIVE  AZA        (G'S)        
C&    REAL*4        FNMUREF                                 ! REFERENCE FRICT. COEF. -NOSE      
C&    REAL*4        FNPPB                                   ! PUSHBACK DEM. NSW ANGLE (DEG)     
C&    REAL*4        FNU                                     ! STRUT U-VEL         - NOSE        
C&    REAL*4        FNUT                                    ! TIRE  U-VEL         - NOSE        
C&    REAL*4        FNV                                     ! STRUT V-VEL          - NOSE       
C&    REAL*4        FOFF(12)                                ! CLS aero force offset             
C&    LOGICAL*1     FPRESET                                 ! PEDAL DRIVE RESET FLAG            
C&    REAL*4        FQ                                      ! DYNAMIC PRESSURE(LB/FT**2)        
C&    REAL*4        FQADEG                                  ! A/C AXIS PITCH RATE(DEG/S)        
C&    REAL*4        FQHRL                                   !  DYNAMIC PRESSURE(LB/FT**2) highra
C&    REAL*4        FQLRL                                   !  DYNAMIC PRESSURE(LB/FT**2) low ra
C&    REAL*4        FQRL                                    !  DYNAMIC PRESSURE(LB/FT**2) rate l
C&    REAL*4        FRADEG                                  ! A/C AXIS YAW RATE  (DEG/S)        
C&    REAL*4        FRSVP                                   ! STAB AXIS TURN YAW/TRU A/S        
C&    LOGICAL*1     FSETAIL                                 ! SET AILERON POSITION              
C&    LOGICAL*1     FSETATAB                                ! SET AILERON TAB                   
C&    LOGICAL*1     FSETELEV                                ! SET ELEVATOR POSITION             
C&    LOGICAL*1     FSETNFOR                                ! SET NOSE WHEEL TILLER FORCE       
C&    LOGICAL*1     FSETNW                                  ! SET NOSE WHEEL                    
C&    LOGICAL*1     FSETNWT                                 ! SET NOSE WHEEL TILLER             
C&    LOGICAL*1     FSETPFOR                                ! SET STRETCHED PEDAL FORCE         
C&    LOGICAL*1     FSETPPOS                                ! SET UNSTRETCHED PEDAL POSITION    
C&    LOGICAL*1     FSETRTAB                                ! SET RUDDER  TAB                   
C&    LOGICAL*1     FSETRUDD                                ! SET RUDDER POSITION               
C&    LOGICAL*1     FSETSFOR                                ! STICK DRIVE BY FORCE              
C&    LOGICAL*1     FSETSKPL                                ! SET NOSE WHEEL ON SKID PLATE      
C&    LOGICAL*1     FSETSPOS                                ! USE STICK POSITION INPUT          
C&    LOGICAL*1     FSETSPP                                 ! SET STRETCHED PEDAL POS           
C&    LOGICAL*1     FSETSSP                                 ! SET STRETCHED STICK POS           
C&    LOGICAL*1     FSETSTAB                                ! SET STAB POS FLAG                 
C&    LOGICAL*1     FSETSWP                                 ! SET STRETCHED WHEEL POS           
C&    LOGICAL*1     FSETTBFL                                ! SET STRETCHED LT TOE BRAKE PEDAL F
C&    LOGICAL*1     FSETTBFR                                ! SET STRETCHED RT TOE BRAKE PEDAL F
C&    LOGICAL*1     FSETTOEB                                ! SET STRETCHED LEFT TOE BRAKE PED P
C&    LOGICAL*1     FSETTOER                                ! SET STRETCHED LEFT TOE BRAKE PED P
C&    LOGICAL*1     FSETWFOR                                ! WHEEL DRIVE BY FORCE              
C&    LOGICAL*1     FSETWPOS                                ! USE WHEEL POSITION INPUT          
C&    LOGICAL*1     FSRESET                                 ! STICK DRIVE RESET FLAG            
C&    LOGICAL*1     FTGACTIV                                ! ACTIVATED                         
C&    REAL*4        FTGLATT                                 ! LATENCY TYPE                      
C&    LOGICAL*1     FTORST                                  ! TAKEOFF RESET FLAG                
C&    REAL*4        FVCAL                                   ! CALIBRATED AIRSPEED(KNOTS)        
C&    REAL*4        FVP                                     ! TRUE AIRSPEED,E-AXIS(FT/S)        
C&    REAL*4        FVPHRL                                  !  True airspeed (FT/sec**2) highrat
C&    REAL*4        FVPKN                                   ! TRUE AIRSPEED,E-AXIS(KNOT)        
C&    REAL*4        FVPKNHRL                                !  True airspeed (KNOTS) highrate li
C&    REAL*4        FVPKNLRL                                !  True airspeed (KNOTS) low rate li
C&    REAL*4        FVPKNRL                                 !  True airspeed (KNOTS) rate limite
C&    REAL*4        FVPLRL                                  !  True airspeed (FT/sec**2) low rat
C&    REAL*4        FVPRL                                   !  True airspeed (FT/SEC) rate limit
C&    INTEGER*1     FWD_JAM(12)                             ! CLS fwd quadrant jam              
C&    LOGICAL*1     FWRESET                                 ! WHEEL DRIVE RESET FLAG            
C&    REAL*4        GCTLBUF(32000)                          ! controls generic base             
C&    REAL*4        GCTLBUF_RAM(1500)                       ! generic ram buffer start          
C&    REAL*4        GCTLBUF_RAP(1000)                       ! generic rap buffer start          
C&    REAL*4        GCTLBUF_RBD(1000)                       ! generic rbd buffer start          
C&    REAL*4        GCTLBUF_RCA(6500)                       ! generic rca buffer start          
C&    REAL*4        GCTLBUF_RCB(750)                        ! generic rcb buffer start          
C&    REAL*4        GCTLBUF_RCS(1250)                       ! generic rcs buffer start          
C&    REAL*4        GCTLBUF_RFG(1250)                       ! generic rfg buffer start          
C&    REAL*4        GCTLBUF_RFM(3000)                       ! generic rfm buffer start          
C&    REAL*4        GCTLBUF_RFS(1000)                       ! generic rfs buffer start          
C&    REAL*4        GCTLBUF_RGR(125)                        ! generic rgr buffer start          
C&    REAL*4        GCTLBUF_RGS(750)                        ! generic rgs buffer start          
C&    REAL*4        GCTLBUF_RHM(2750)                       ! generic rhm buffer start          
C&    REAL*4        GCTLBUF_RHM_ADD(1200)                   ! additional hm effect base         
C&    REAL*4        GCTLBUF_RMR(1000)                       ! generic rmr buffer start          
C&    REAL*4        GCTLBUF_RPS(125)                        ! generic rps buffer start          
C&    REAL*4        GCTLBUF_RSA(6500)                       ! generic rsa buffer start          
C&    REAL*4        GCTLBUF_RTA(1000)                       ! generic rta buffer start          
C&    REAL*4        GCTLBUF_TABLES(2000)                    ! generic table buffer start        
C&    INTEGER*4     HCLS(40)                                ! HCLS                              
C&    INTEGER*4     HCLS_FAULT(6)                           ! HCLS FAULT WORD                   
C&    INTEGER*4     HCLS_HOST_CT                            ! HOST RETURN COUNT                 
C&    INTEGER*4     HCLS_MODE(6)                            ! HCLS MODE                         
C&    INTEGER*4     HCLS_MODE_CMD(6)                        ! HCLS MODE CMD                     
C&    INTEGER*4     HCLS_ON_OFF(6)                          ! HCLS ON OFF                       
C&    INTEGER*4     HCLS_ON_OFF_CMD(6)                      ! HCLS ON OFF CMD                   
C&    INTEGER*4     HCLS_RESET_CMD(6)                       ! HCLS HARDWARE RESET CMD           
C&    INTEGER*4     HCLS_RFC_CT                             ! HCLS COUNTER                      
C&    INTEGER*4     HOST_CT                                 ! CLS host return count             
C&    INTEGER*1     HOST_GENSIM_C(700)                      ! HOST T0 CLS INTER. gen sim buffer 
C&    REAL*4        HOST_GENSIM_F(175)                      ! HOST T0 CLS INTER. gen sim buffer 
C&    INTEGER*4     HOST_GENSIM_I(175)                      ! HOST T0 CLS INTER. gen sim buffer 
C&    INTEGER*1     HOST_STD_C(400)                         ! HOST T0 CLS INTER. std buffer flag
C&    REAL*4        HOST_STD_F(100)                         ! HOST T0 CLS INTER. std buffer floa
C&    INTEGER*4     HOST_STD_I(100)                         ! HOST T0 CLS INTER. std buffer inte
C&    INTEGER*1     HOST_UNIQUE_C(400)                      ! HOST T0 CLS INTER. unique buffer f
C&    REAL*4        HOST_UNIQUE_F(100)                      ! HOST T0 CLS INTER. unique buffer f
C&    INTEGER*4     HOST_UNIQUE_I(100)                      ! HOST T0 CLS INTER. unique buffer i
C&    REAL*4        H_AGL                                   ! height above ground               
C&    INTEGER*4     KC10CMD                                 ! txfer cmd word                    
C&    INTEGER*4     KC10SAFE                                ! rxfer safety status               
C&    INTEGER*4     KC10STATUS                              ! rxfer chan status                 
C&    INTEGER*4     KC11CMD                                 ! txfer cmd word                    
C&    INTEGER*4     KC11SAFE                                ! rxfer safety status               
C&    INTEGER*4     KC11STATUS                              ! rxfer chan status                 
C&    INTEGER*4     KC12CMD                                 ! txfer cmd word                    
C&    INTEGER*4     KC12SAFE                                ! rxfer safety status               
C&    INTEGER*4     KC12STATUS                              ! rxfer chan status                 
C&    INTEGER*4     KC1CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC1SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC1STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC2CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC2SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC2STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC3CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC3SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC3STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC4CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC4SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC4STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC5CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC5SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC5STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC6CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC6SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC6STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC7CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC7SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC7STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC8CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC8SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC8STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KC9CMD                                  ! txfer cmd word                    
C&    INTEGER*4     KC9SAFE                                 ! rxfer safety status               
C&    INTEGER*4     KC9STATUS                               ! rxfer chan status                 
C&    INTEGER*4     KCRSTATUS                               ! rxfer status                      
C&    INTEGER*4     KCZCRBUF(400)                           ! CLDG INTEGER READ BUFFER          
C&    INTEGER*4     KCZCWBUF(400)                           ! CLDG INTEGER WRITE BUFFER         
C&    INTEGER*4     KCZHRBUF(400)                           ! HOST INTEGER READ BUFFER          
C&    INTEGER*4     KCZHWBUF(400)                           ! HOST INTEGER WRITE BUFFER         
C&    INTEGER*4     KRT1MODTM(20)                           ! RT MODULE ACTUAL TIME IN CLOCK CYC
C&    INTEGER*4     KRT1RATE                                ! RT TASK ACTUAL RATE IN CLOCK CYCLE
C&    INTEGER*4     KRT1TASKTM                              ! RT TASK ACTUAL TIME IN CLOCK CYCLE
C&    REAL*4        LATENCY_IN                              ! CLS latency input test variable   
C&    LOGICAL*1     LAXMAN                                  ! MANUAL SPEED TRIM ENABLE          
C&    INTEGER*4     LB                                      ! LOCAL                             
C&    LOGICAL*1     LCAPCWS(20)                             ! AP in CWS mode                    
C&    LOGICAL*1     LCAPCWSA(20)                            ! CWS ARM                           
C&    LOGICAL*1     LCAPOR(20)                              ! AP in Override mode               
C&    LOGICAL*1     LCAPPSV1                                ! Autopilot, Pitch Servo #1         
C&    LOGICAL*1     LCAPPSV2                                ! Autopilot, Pitch Servo #2         
C&    LOGICAL*1     LCAPPSV3                                ! Autopilot, Pitch Servo #3         
C&    LOGICAL*1     LCAPRSV1                                ! Autopilot, Roll Servo #1          
C&    LOGICAL*1     LCAPRSV2                                ! Autopilot, Roll Servo #2          
C&    LOGICAL*1     LCAPRSV3                                ! Autopilot, Roll Servo #3          
C&    LOGICAL*1     LCAPYSV1                                ! Autopilot, Yaw Servo #1           
C&    LOGICAL*1     LCAPYSV2                                ! Autopilot, Yaw Servo #2           
C&    LOGICAL*1     LCAPYSV3                                ! Autopilot, Yaw Servo #3           
C&    LOGICAL*1     LCBUFFOUT                               ! output buffer enable flag         
C&    LOGICAL*1     LCBUFFOVRD                              ! buffer override flag              
C&    LOGICAL*1     LCCMD                                   ! CONTROLS COMMAND ON FLAG          
C&    LOGICAL*1     LCHOSTOVRD                              ! host override flag                
C&    LOGICAL*1     LCIELEV                                 ! Init elev pos for static plot     
C&    LOGICAL*1     LCLFAIL                                 ! C/L fail flag                     
C&    LOGICAL*1     LCLOFF                                  ! C/L off flag                      
C&    LOGICAL*1     LCLON                                   ! C/L on flag                       
C&    LOGICAL*1     LCLSWITCH                               ! C/L on switch                     
C&    LOGICAL*1     LCQUIT(20)                              ! QUIT FLAGS                        
C&    LOGICAL*1     LCSETAIL                                ! SET AILERON POSITION              
C&    LOGICAL*1     LCSETCSFOR                              ! Set Co-Pilot's stick force        
C&    LOGICAL*1     LCSETCSSP                               ! Set Co-Pilot's stretched stick pos
C&    LOGICAL*1     LCSETCSWP                               ! Set Co-Pilot's stretched wheel pos
C&    LOGICAL*1     LCSETCWFOR                              ! Set Co-Pilot's wheel force        
C&    LOGICAL*1     LCSETELEV                               ! SET ELEVATOR POSITION             
C&    LOGICAL*1     LCSETLG                                 ! SET ELG LT. MAIN GEAR POSITION    
C&    LOGICAL*1     LCSETLGH                                ! SET ELG LT. MAIN GEAR HANDLE POSIT
C&    LOGICAL*1     LCSETLGHF                               ! SET ELG LT. MAIN GEAR HANDLE FORCE
C&    LOGICAL*1     LCSETLTB                                ! SET LT. TOE BRAKE PEDAL POSITION  
C&    LOGICAL*1     LCSETLTBF                               ! SET LT. TOE BRAKE PEDAL FORCE     
C&    LOGICAL*1     LCSETNFOR                               ! SET NOSE WHEEL TILLER FORCE       
C&    LOGICAL*1     LCSETNG                                 ! SET ELG NOSE GEAR POSITION        
C&    LOGICAL*1     LCSETNGH                                ! SET ELG NOSE GEAR HANDLE POSITION 
C&    LOGICAL*1     LCSETNGHF                               ! SET ELG NOSE GEAR HANDLE FORCE    
C&    LOGICAL*1     LCSETNW                                 ! SET NOSE WHEEL                    
C&    LOGICAL*1     LCSETNWT                                ! SET NOSE WHEEL TILLER             
C&    LOGICAL*1     LCSETPFOR                               ! SET STRETCHED PEDAL FORCE         
C&    LOGICAL*1     LCSETRAIL                               ! Set Rt. Aileron pos.              
C&    LOGICAL*1     LCSETRELEV                              ! Set Rt. Elevator pos.             
C&    LOGICAL*1     LCSETRG                                 ! SET ELG RT. MAIN GEAR POSITION    
C&    LOGICAL*1     LCSETRGH                                ! SET ELG RT. MAIN GEAR HANDLE POSIT
C&    LOGICAL*1     LCSETRGHF                               ! SET ELG LT. MAIN GEAR HANDLE FORCE
C&    LOGICAL*1     LCSETRTB                                ! SET RT. TOE BRAKE PEDAL POSITION  
C&    LOGICAL*1     LCSETRTBF                               ! SET RT. TOE BRAKE PEDAL FORCE     
C&    LOGICAL*1     LCSETRUDD                               ! SET RUDDER POSITION               
C&    LOGICAL*1     LCSETSFOR                               ! STICK DRIVE BY FORCE              
C&    LOGICAL*1     LCSETSPP                                ! SET STRETCHED PEDAL POS           
C&    LOGICAL*1     LCSETSSP                                ! SET STRETCHED STICK POS           
C&    LOGICAL*1     LCSETSWP                                ! SET STRETCHED WHEEL POS           
C&    LOGICAL*1     LCSETWFOR                               ! WHEEL DRIVE BY FORCE              
C&    LOGICAL*1     LCTL_PXFER_IN(1600)                     ! Controls phy xfer output boolean  
C&    LOGICAL*1     LCTL_PXFER_OUT(1600)                    ! Controls phy xfer output boolean  
C&    LOGICAL*1     LCZCRBUF(1600)                          ! CLDG LOGICAL READ BUFFER          
C&    LOGICAL*1     LCZCWBUF(1600)                          ! CLDG LOGICAL WRITE BUFFER         
C&    LOGICAL*1     LCZHRBUF(1600)                          ! HOST LOGICAL READ BUFFER          
C&    LOGICAL*1     LCZHWBUF(1600)                          ! HOST LOGICAL WRITE BUFFER         
C&    LOGICAL*1     LCZONLINE                               ! online flag                       
C&    LOGICAL*1     LC_START(12)                            ! CLS Start Without HW              
C&    LOGICAL*1     LFXFASTM                                ! FASTLTRIM FLAG                    
C&    LOGICAL*1     LIXINDIS                                ! INTERMEDIATE VARIABLE             
C&    REAL*4        LVTABLEC(32768)                         ! TABLE DATA AREA IN PHYSICAL MEMORY
C&    REAL*4        LVTABLEP(32768)                         ! TABLE DATA AREA IN PHYSICAL MEMORY
C&    LOGICAL*1     LZONLINE                                ! HOST IS ONLINE (CONTROLLING SIMULA
C&    LOGICAL*1     LZRTSKTM                                ! task timing flag                  
C&    REAL*4        MACH_NO                                 ! CLS Mach number                   
C&    LOGICAL*1     MBRSTJAM                                ! JAMMED STABILIZER                 
C&    INTEGER*4     MODEL_TYPE                              ! model type                        
C&    INTEGER*1     MODE_CMD(12)                            ! CLS mode command                  
C&    REAL*4        NOSE_OFFSET                             ! Flt test nose angle offset        
C&    REAL*4        NW_CASTOR_ANG                           ! NOSEWHEEL CASTOR ANGLE            
C&    REAL*4        NW_CASTOR_ANG_N1                        ! Nose Wheel Castor Angle N1        
C&    REAL*4        NW_CASTOR_GAIN                          ! NOSEWHEEL CASTOR GAIN             
C&    REAL*4        NW_CASTOR_VEL                           ! NOSEWHEEL CASTOR VELOCITY         
C&    REAL*4        NW_FOR                                  ! NOSE WHEEL CASTERING FORCE        
C&    REAL*4        NW_LIM                                  ! NOSEWHEEL LIMIT (PEDAL UNITS - INC
C&    REAL*4        NW_SPEED                                ! NOSEWHEEL SPEED                   
C&    INTEGER*1     ON_OFF_CMD(12)                          ! CLS on off flag command           
C&    INTEGER*1     PARK_BRK                                ! PARKING BRAKE FLAG                
C&    REAL*4        PARK_BRK_N1                             ! Parking Brake Flag n-1            
C&    REAL*4        PED_STEER                               ! CLS pedal steering                
C&    REAL*4        PITCH_ACC                               ! AIRCRAFT PITCH ACCELERATION       
C&    LOGICAL*1     RAMBMJ(20)                              ! AFT MASS JAM INPUT FLAG           
C&    REAL*4        RAMF2A(20)                              ! AFT MASS INPUT FORCE BUFFER 2A (lb
C&    REAL*4        RAMF2F(20)                              ! AFT MASS INPUT FORCE BUFFER 2F (lb
C&    REAL*4        RAMF3A(20)                              ! AFT MASS INPUT FORCE BUFFER 3A (lb
C&    REAL*4        RAMF4A(20)                              ! AFT MASS INPUT FORCE BUFFER 4A (lb
C&    REAL*4        RAMF5A(20)                              ! AFT MASS INPUT FORCE BUFFER 5A (lb
C&    INTEGER*1     RAPENG(20)                              ! Autopilot/CWS Engaged (1=A/P, 2=CW
C&    REAL*4        RAPF1(20)                               ! Input Force #1                    
C&    REAL*4        RAPF2(20)                               ! Input Force #2                    
C&    REAL*4        RAPRCMD                                 ! YAW A/P COMMAND                   
C&    REAL*4        RAPX1(20)                               ! Autopilot Position Input #1 (Comma
C&    REAL*4        RAPX2(20)                               ! Autopilot Position Input #2 (Feedb
C&    LOGICAL*1     RBDBFTG(20)                             ! atg force back drive flag         
C&    LOGICAL*1     RBDBXTG(20)                             ! atg position back drive flag      
C&    REAL*4        RBDFATG(20)                             ! atg force back drive command      
C&    REAL*4        RBDXATG(20)                             ! atg position back drive command   
C&    REAL*4        RC1POS                                  ! table position independant variabl
C&    REAL*4        RCACTBDD(40)                            ! Actuator blow down pos T.E.D.     
C&    REAL*4        RCACTBDU(40)                            ! Actuator blow down pos T.E.U.     
C&    REAL*4        RCACTVL(40)                             ! Actuator rate limit               
C&    REAL*4        RCAGBOB(4)                              ! AFT MASS BOB WEIGHT GAIN          
C&    REAL*4        RCAHYD                                  ! # of Hyd Sys At Ail for rate table
C&    REAL*4        RCAILTG                                 ! Aileron Trim gain                 
C&    REAL*4        RCAILTX                                 ! Aileron Trim cmd pos              
C&    REAL*4        RCALTGF                                 ! Otbd ail reduction gain           
C&    REAL*4        RCANGLE(3)                              ! Stick Angle (rad)                 
C&    LOGICAL*1     RCAON1                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON10                                 ! Enable channel flag               
C&    LOGICAL*1     RCAON11                                 ! Enable channel flag               
C&    LOGICAL*1     RCAON12                                 ! Enable channel flag               
C&    LOGICAL*1     RCAON2                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON3                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON4                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON5                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON6                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON7                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON8                                  ! Enable channel flag               
C&    LOGICAL*1     RCAON9                                  ! Enable channel flag               
C&    LOGICAL*1     RCAONA(12)                              ! Enable channel flag               
C&    REAL*4        RCAPCENG                                ! collective autopilot engage       
C&    REAL*4        RCAPCMDC(20)                            ! AP command                        
C&    REAL*4        RCAPCWSC(20)                            ! CWS command                       
C&    REAL*4        RCAPCWST(20)                            ! CWS Force threshold               
C&    REAL*4        RCAPDTG(20)                             ! Time constant                     
C&    REAL*4        RCAPFCWS(20)                            ! CWS Force                         
C&    REAL*4        RCAPFOR(20)                             ! AP override force                 
C&    REAL*4        RCAPK(20)                               ! Ap/ctl vel gain                   
C&    REAL*4        RCAPKCWS(20)                            ! CWS Force to Vel gain             
C&    REAL*4        RCAPKG(20)                              ! Ap/ctl vel gain rate              
C&    REAL*4        RCAPKXE(20)                             ! AP position error gain            
C&    REAL*4        RCAPLCMD                                ! Lower Rud Par A/P cmd             
C&    LOGICAL*1     RCAPLENG                                ! Lower Rud Par A/P eng             
C&    REAL*4        RCAPMG(20)                              ! Mach gain                         
C&    REAL*4        RCAPPCMD                                ! AP pitch command                  
C&    LOGICAL*1     RCAPPENG                                ! AP pitch engage                   
C&    REAL*4        RCAPPLG                                 ! Lower Rud Par A/P gain            
C&    REAL*4        RCAPPUG                                 ! Upper Rud Par A/P gain            
C&    REAL*4        RCAPPVMX                                ! Max pitch AP ctl vel              
C&    REAL*4        RCAPRCMD                                ! AP roll command                   
C&    LOGICAL*1     RCAPRENG                                ! AP roll engage                    
C&    REAL*4        RCAPRVMX                                ! Max roll AP ctl vel               
C&    REAL*4        RCAPUCMD                                ! Upper Rud Par A/P cmd             
C&    LOGICAL*1     RCAPUENG                                ! Upper Rud Par A/P eng             
C&    REAL*4        RCAPVMX(20)                             ! Max Ap/ctl rate                   
C&    REAL*4        RCAPYCMD                                ! AP yaw command                    
C&    LOGICAL*1     RCAPYENG                                ! AP yaw engage                     
C&    REAL*4        RCAPYVMX                                ! Max yaw AP ctl vel                
C&    REAL*4        RCAXA                                   ! ACC ALONG X A/C AXIS (to controls)
C&    REAL*4        RCAZA                                   ! ACC ALONG Z A/C AXIS (to controls)
C&    LOGICAL*1     RCBLCONT                                ! CTLS LAT. HDSHK FLAG (ATG ON)     
C&    LOGICAL*1     RCBLCONTM                               ! CTLS LAT. HDSHK FLG (MAN FLY)     
C&    REAL*4        RCBOOST                                 ! RUD BOOST TRANSITION FACTOR (norma
C&    REAL*4        RCBUFFET(6)                             ! BUFFET                            
C&    REAL*4        RCCOLTRM                                ! Column neutral shift pos          
C&    REAL*4        RCCWSPF                                 ! CWS FORCE PITCH                   
C&    REAL*4        RCCWSRF                                 ! CWS FORCE ROLL                    
C&    REAL*4        RCDEMLATX                               ! latency demand flag               
C&    REAL*4        RCDEMLG                                 ! DEMANDED LT. MAIN ELG (pos or forc
C&    REAL*4        RCDEMLTB                                ! DEMANDED LT. TOE BRAKE (pos or for
C&    REAL*4        RCDEMNG                                 ! DEMANDED NOSE ELG (pos or force)  
C&    REAL*4        RCDEMNW                                 ! DEMANDED NOSE WHEEL POSITION      
C&    REAL*4        RCDEMPDL                                ! DEMANDED PEDAL POSITION           
C&    REAL*4        RCDEMRG                                 ! DEMANDED RT. MAIN ELG (pos or forc
C&    REAL*4        RCDEMRTB                                ! DEMANDED RT. TOE BRAKE (pos or for
C&    REAL*4        RCDEMSTK                                ! DEMANDED STICK POSITION           
C&    REAL*4        RCDEMWHL                                ! DEMANDED WHEEL POSITION           
C&    REAL*4        RCEALP                                  ! Elev. assy. limit pos             
C&    REAL*4        RCELFIAS                                ! ELEV FEEL IAS                     
C&    LOGICAL*1     RCEXBCKV(40)                            ! EXEC BOOLEAN BACKFLOW VALVE       
C&    LOGICAL*1     RCEXBCLI(20)                            ! EXEC LOADER ROTARY SW             
C&    LOGICAL*1     RCEXBFTG(20)                            ! EXEC BOOLEAN ATG FORCE            
C&    LOGICAL*1     RCEXBMJ(20)                             ! EXEC BOOLEAN MASS JAM             
C&    LOGICAL*1     RCEXBPF(40)                             ! EXEC BOOLEAN PISTON FLOAT         
C&    LOGICAL*1     RCEXBPJ(40)                             ! EXEC BOOLEAN PISTON JAM           
C&    LOGICAL*1     RCEXBVJ(40)                             ! EXEC BOOLEAN VALVE JAM            
C&    LOGICAL*1     RCEXBXTG(20)                            ! EXEC BOOLEAN ATG POSITION         
C&    REAL*4        RCEXFHM(40)                             ! HINGE MOMENT FORCE (LBS)          
C&    REAL*4        RCEXGSV(20)                             ! EXEC GENERIC SPRING VEL           
C&    REAL*4        RCEXGSX(20)                             ! EXEC GENERIC SPRING POS           
C&    REAL*4        RCEXPHM(40)                             ! EXEC HINGE MOMENT PRESSURE        
C&    REAL*4        RCEXPHY(40)                             ! EXEC HYDRAULIC PRESSURE           
C&    REAL*4        RCEXQIN                                 ! EXEC Q (DYNAMIC PRESSURE)         
C&    REAL*4        RCEXTRIMN(20)                           ! EXEC TRIM NORMALIZED              
C&    LOGICAL*1     RCEXTRL(20)                             ! EXEC TRIM RELEASE                 
C&    REAL*4        RCFABOB(4)                              ! AFT MASS BOBWEIGHT                
C&    REAL*4        RCFBOB(10)                              ! bob wt.                           
C&    REAL*4        RCFBOBLN(3)                             ! Bob Wt neg limit (lbs)            
C&    REAL*4        RCFBOBLP(3)                             ! Bob Wt pos limit (lbs)            
C&    REAL*4        RCFEELP                                 ! Column Feel Pressure              
C&    REAL*4        RCFELPL                                 ! Left Column Feel Pressure         
C&    REAL*4        RCFELPR                                 ! Right Column Feel Pressure        
C&    REAL*4        RCFEMU                                  ! elev mass unbalance force         
C&    REAL*4        RCFETMU                                 ! tab mass unbalance force          
C&    REAL*4        RCFFBOB(4)                              ! FWD MASS BOBWEIGHT                
C&    REAL*4        RCFGBOB(4)                              ! FWD MASS BOB WEIGHT GAIN          
C&    REAL*4        RCFMXGE                                 ! elev mass xe g G                  
C&    REAL*4        RCFMXGET                                ! tab mass xe g G                   
C&    REAL*4        RCFOFF(20)                              ! bob weight offset                 
C&    REAL*4        RCFSVRUD                                ! Rudder PCU servo valve force      
C&    REAL*4        RCGDTQDB                                ! Nose wheel gnd torque Dead Band   
C&    REAL*4        RCGFBOB(3)                              ! G sensing Bob Wt.(lbs)            
C&    REAL*4        RCGFBOBC(20)                            ! Column bob wt. gain               
C&    REAL*4        RCGNDTQ                                 ! Nose wheel gnd torque from flt    
C&    REAL*4        RCGNDTQG                                ! Nose wheel gnd torque gain        
C&    REAL*4        RCHPTC                                  ! Center sys hyd press, tail        
C&    REAL*4        RCHPTL                                  ! Left sys hyd press, tail          
C&    REAL*4        RCHPTR                                  ! Right sys hyd press, tail         
C&    REAL*4        RCHPWC                                  ! Center sys hyd press, wing        
C&    REAL*4        RCHPWL                                  ! Left sys hyd press, wing          
C&    REAL*4        RCHPWR                                  ! Right sys hyd press, wing         
C&    REAL*4        RCIAGN                                  ! Inbd Ail to Wheel gearing (neg)   
C&    REAL*4        RCIAGP                                  ! Inbd Ail to Wheel gearing (pos)   
C&    REAL*4        RCIAL                                   ! Inbd ail TED & TEU limit          
C&    REAL*4        RCIEGN                                  ! Inbd  Elev to Column gearing (neg)
C&    REAL*4        RCIEGP                                  ! Inbd  Elev to Column gearing (pos)
C&    REAL*4        RCIELMAX                                ! Max inbd Elev limit               
C&    REAL*4        RCKBOOST                                ! RATE OF CHANGE, RUD BOOST ON TO BO
C&    REAL*4        RCLAHYD                                 ! # of Hyd Sys Operational, Lt. Ail 
C&    REAL*4        RCLIAILV                                ! LT. INBOARD AILERON VEL (deg/sec) 
C&    REAL*4        RCLIAILX                                ! LT. INBOARD AILERON POS (deg)     
C&    REAL*4        RCLOAILV                                ! LT. OUTBOARD AILERON VEL (deg/sec)
C&    REAL*4        RCLOAILX                                ! LT. OUTBOARD AILERON POS (deg)    
C&    REAL*4        RCLRGN                                  ! Lower Rud to Pedal gearing (neg)  
C&    REAL*4        RCLRGP                                  ! Lower Rud to Pedal gearing (pos)  
C&    REAL*4        RCNFC                                   ! NoseWheel caster force (lbs)      
C&    REAL*4        RCNFCDB                                 ! NoseWheel caster force db (lbs)   
C&    REAL*4        RCNFCDTG                                ! Time constant, caster force lag   
C&    REAL*4        RCNFCL                                  ! NoseWheel caster force limit (lbs)
C&    REAL*4        RCNGEAR                                 ! Nornalized nose gear pos for nose 
C&    REAL*4        RCNLG                                   ! Nose wheel limit gain rate        
C&    REAL*4        RCNLMDTG                                ! NWS Limit delta T gain            
C&    REAL*4        RCNLN                                   ! Nose wheel neg limit              
C&    REAL*4        RCNLP                                   ! Nose wheel pos limit              
C&    REAL*4        RCNPHDTG                                ! Time constant, Hyd press lag      
C&    REAL*4        RCNPHY                                  ! Hyd press to nosewheel actuators  
C&    REAL*4        RCNPTG                                  ! Pedal/tiller gain                 
C&    REAL*4        RCNPTG1                                 ! Pedal/tiller gain rate            
C&    REAL*4        RCNPTGM                                 ! Max Pedal/tiller gain             
C&    REAL*4        RCNPTVCD                                ! Tiller vel cmd from pedals        
C&    REAL*4        RCNPTXCD                                ! Pedal to tiller pos cmd           
C&    REAL*4        RCNTF                                   ! Tiller force                      
C&    REAL*4        RCNTNG                                  ! Tiller/nosewheel gain             
C&    REAL*4        RCNTNG1                                 ! Tiller/nosewheel gain rate        
C&    REAL*4        RCNTNGM                                 ! Max tiller/noewheel gain          
C&    REAL*4        RCNTPU                                  ! Unstretched tiller pos            
C&    REAL*4        RCNVCD                                  ! NW vel cmd                        
C&    REAL*4        RCNWAPL                                 ! NWS act press effectivity limit (d
C&    REAL*4        RCNWGN                                  ! NoseWheel to Tiller gearing (neg) 
C&    REAL*4        RCNWGP                                  ! NoseWheel to Tiller gearing (pos) 
C&    REAL*4        RCNWHPR1                                ! NWS act supply press from sys 1   
C&    REAL*4        RCNWHPR3                                ! NWS act supply press form sys 3   
C&    REAL*4        RCNWLIM                                 ! Normal Nose Wheel mass limit      
C&    REAL*4        RCNWTL                                  ! Normalized and filtered tiller pos
C&    REAL*4        RCNXCD                                  ! NW pos cmd                        
C&    REAL*4        RCOAGN                                  ! Outbd Ail to Wheel gearing (neg)  
C&    REAL*4        RCOAGP                                  ! Outbd Ail to Wheel gearing (pos)  
C&    REAL*4        RCOAILG                                 ! OUTBOARD AILERON LOCKOUT GAIN (DIM
C&    REAL*4        RCOALTED                                ! Otbd ail TED limit                
C&    REAL*4        RCOALTEU                                ! Otbd ail TEU limit                
C&    REAL*4        RCOAMAX                                 ! MAXIMUN OUTBOARD AILERON DEFLECTIO
C&    REAL*4        RCOEGN                                  ! Outbd Elev to Column gearing (neg)
C&    REAL*4        RCOEGP                                  ! Outbd Elev to Column gearing (pos)
C&    REAL*4        RCOELMAX                                ! Max otbd Elev limit               
C&    REAL*4        RCOLEO                                  ! Oleo compression for nws          
C&    REAL*4        RCONTIMER                               ! C/L on switch timer               
C&    REAL*4        RCPAFBOB                                ! Pitch Acc Bob Wt (lbs)            
C&    REAL*4        RCPEDTDB                                ! Pedal trim deadband               
C&    REAL*4        RCPEDTG                                 ! Pedal trim scaling gain           
C&    REAL*4        RCPEDTRM                                ! Pedal trim (deg)                  
C&    REAL*4        RCPEDTU                                 ! RUDDER TRIM UNITS                 
C&    REAL*4        RCPF(20)                                ! Scaled Pilot Force                
C&    REAL*4        RCPFDTG(20)                             ! Time Constant                     
C&    REAL*4        RCPFG(20)                               ! Pilot Force Gain                  
C&    REAL*4        RCPFKAP(20)                             ! Pilot Force Scaling for AP        
C&    REAL*4        RCPTRM                                  ! Rudder trim offset                
C&    REAL*4        RCPTRMG                                 ! Rudder trim gain                  
C&    REAL*4        RCRAFBOB                                ! Roll Acc Bob Wt (lbs)             
C&    REAL*4        RCRAHYD                                 ! # of Hyd Sys Operational, Rt. Ail 
C&    REAL*4        RCRCEV                                  ! Cal air speed (kts) for Rud Ratio 
C&    REAL*4        RCRCGAIN                                ! Rudder ratio changer gain         
C&    REAL*4        RCREPOSD(6)                             ! Reposition damping constant       
C&    REAL*4        RCRIAILV                                ! RT. INBOARD AILERON VEL (deg/sec) 
C&    REAL*4        RCRIAILX                                ! RT. INBOARD AILERON POS (deg)     
C&    REAL*4        RCROAILV                                ! RT. OUTBOARD AILERON VEL (deg/sec)
C&    REAL*4        RCROAILX                                ! RT. OUTBOARD AILERON POS (deg)    
C&    REAL*4        RCROAVCD                                ! Otbd ail vel cmd                  
C&    REAL*4        RCROAXCD                                ! Otbd ail pos cmd                  
C&    REAL*4        RCRPXL                                  ! RUD POS LIMIT DUE TO AIRSPPED & HY
C&    REAL*4        RCRPXLB                                 ! SLIDE SLIP EFFECT ON RUD POS LIMIT
C&    REAL*4        RCRPXLMAX                               ! MAXIMUN RUD ACT DEFLECTION (DEG)  
C&    REAL*4        RCRPXLN                                 ! NEG RUD ACT POSITION LIMIT (DEG)  
C&    REAL*4        RCRPXLP                                 ! POS RUD ACT POSITION LIMIT (DEG)  
C&    REAL*4        RCRUDG                                  ! Average Rud to Pedal gearing      
C&    REAL*4        RCRUDMDH                                ! Upper & Lower rudder limits       
C&    REAL*4        RCRUDTG                                 ! Rudder Trim gain                  
C&    REAL*4        RCRUDTX                                 ! Rudder Trim cmd pos               
C&    LOGICAL*1     RCSABPJ(40)                             ! PISTON JAM FLAG                   
C&    LOGICAL*1     RCSABVJ1(40)                            ! servo valve jam input 1           
C&    LOGICAL*1     RCSABVJ2(40)                            ! servo valve jam input 2           
C&    LOGICAL*1     RCSABVJ3(40)                            ! servo valve jam input 3           
C&    REAL*4        RCSAFHM(40)                             ! actuator hinge moment input       
C&    REAL*4        RCSAPHY1(40)                            ! servo act hyd press input 1       
C&    REAL*4        RCSAPHY2(40)                            ! servo act hyd press input 2       
C&    REAL*4        RCSAPHY3(40)                            ! servo act hyd press input 3       
C&    REAL*4        RCSAV1(40)                              ! servo valve velocity cmd 1 input  
C&    REAL*4        RCSAV2(40)                              ! servo valve velocity cmd 2 input  
C&    REAL*4        RCSAX1(40)                              ! servo valve position cmd 1 input  
C&    REAL*4        RCSAX2(40)                              ! servo valve position cmd 2 input  
C&    REAL*4        RCSCALE(3)                              ! Scale (inches to degs)            
C&    REAL*4        RCSPARE(10)                             ! SPARE REAL                        
C&    REAL*4        RCTESTINK1                              ! test input gains 1                
C&    REAL*4        RCTESTINK2                              ! test input gains 2                
C&    REAL*4        RCTGLATT                                ! latency test type                 
C&    REAL*4        RCTHYD                                  ! # of Hyd Sys Operational, Tail    
C&    REAL*4        RCTLTIME                                ! ctl time                          
C&    REAL*4        RCTL_PXFER_IN(400)                      ! Controls phy xfer output float    
C&    REAL*4        RCTL_PXFER_OUT(400)                     ! Controls phy xfer output float    
C&    REAL*4        RCTRESET(12)                            ! controls reset timer              
C&    REAL*4        RCUNLOAD(6)                             ! unloaded mode                     
C&    REAL*4        RCURGN                                  ! Upper Rud to Pedal gearing (neg)  
C&    REAL*4        RCURGP                                  ! Upper Rud to Pedal gearing (pos)  
C&    REAL*4        RCVCKTS                                 ! Cal air speed (kts)               
C&    REAL*4        RCVIAS                                  ! AIRSPEED                          
C&    REAL*4        RCVNW                                   ! Nose wheel fwd vel                
C&    REAL*4        RCWHLTDB                                ! Wheel trim deadband               
C&    REAL*4        RCWHLTG                                 ! Wheel trim scaling gain           
C&    REAL*4        RCWHLTRM                                ! Wheel trim (deg)                  
C&    REAL*4        RCWHLTRMU                               ! WHEEL TRIM UNITS                  
C&    REAL*4        RCWTRM                                  ! Wheel trim offset                 
C&    REAL*4        RCWTRMG                                 ! Wheel trim gain                   
C&    REAL*4        RCXLATSK_TRM                            ! LAT CONTROL TRIM POSITION ATG     
C&    REAL*4        RCXLONSK_TRM                            ! LONG CONTROL TRIM POSITION AT     
C&    REAL*4        RCXRUPED_TRM                            ! RUDDER CONTROL TRIM POSITION      
C&    REAL*4        RCYAFBOB                                ! Yaw Acc Bob Wt (lbs)              
C&    REAL*4        RCYDLRV                                 ! Lower rud yaw damp vel cmd        
C&    REAL*4        RCYDLRX                                 ! Lower rud yaw damp pos cmd        
C&    REAL*4        RCYDURV                                 ! Upper rud yaw damp vel cmd        
C&    REAL*4        RCYDURX                                 ! Upper rud yaw damp pos cmd        
C&    REAL*4        RCYLRVCD                                ! Lower rud act vel cmd             
C&    REAL*4        RCYLRXCD                                ! Lower rud act pos cmd             
C&    REAL*4        RCYURVCD                                ! Upper rud act vel cmd             
C&    REAL*4        RCYURXCD                                ! Upper rud act pos cmd             
C&    REAL*4        RCYVCMD                                 ! RUDDER ACT VEL CMD (DEG/SEC)      
C&    REAL*4        RCYXCMD                                 ! RUDDER ACT POS CMD (DEG)          
C&    REAL*4        RCZCRBUF(400)                           ! CLDG FLOAT READ BUFFER            
C&    REAL*4        RCZCWBUF(400)                           ! CLDG FLOAT WRITE BUFFER           
C&    REAL*4        RCZHRBUF(400)                           ! HOST FLOAT READ BUFFER            
C&    REAL*4        RCZSTFRL                                ! STAB POSITION                     
C&    REAL*4        RC_A2R_POS                              ! Ail_Rud interconnect position     
C&    REAL*4        RC_A2R_VEL                              ! Ail_Rud interconnect velocity     
C&    REAL*4        RC_BKDR(6)                              ! Backdrive Active Word             
C&    REAL*4        RC_BRK_OFF_POS(2)                       ! Parking Brake Off Position (Back S
C&    REAL*4        RC_BRK_SET_POS(2)                       ! Parking Brake Set Position        
C&    REAL*4        RC_CASTOR_ANG                           ! Nose Wheel Conditioned Castor Ange
C&    REAL*4        RC_CASTOR_VEL                           ! Nose Wheel Conditioned Castor Velo
C&    REAL*4        RC_DELTA_AOA                            ! Delta AOA                         
C&    REAL*4        RC_DELTA_CAL                            ! Delta Elev AOA Cal                
C&    REAL*4        RC_DWASH                                ! Dn Wash Ang                       
C&    REAL*4        RC_DWASH_ZERO_AOA                       ! Dn Wash Ang at zero AOA           
C&    REAL*4        RC_DYNGOOSE(12)                         ! Dynamic Goose Term                
C&    REAL*4        RC_ELV_FDS                              ! Elevator HM Downspring Force      
C&    REAL*4        RC_ELV_FDSI                             ! Intermediate Elevator HM Downsprin
C&    REAL*4        RC_ELV_FDSK                             ! Gain for Elevator HM Downspring Fo
C&    REAL*4        RC_ELV_TRIM                             ! Avg Elevator Trim Tab Position    
C&    REAL*4        RC_HORZ_STAB_AOA                        ! Horizontal Stab AOA               
C&    REAL*4        RC_NWS_POS                              ! NWS Position                      
C&    REAL*4        RC_NWS_POS_N1                           ! NWS Position n-1                  
C&    REAL*4        RC_NWS_VEL                              ! NWS Position velocity             
C&    REAL*4        RC_R2A_FOR                              ! Ail_Rud interconnect force        
C&    REAL*4        RC_TRM_ERR(12)                          ! TRIM ERROR                        
C&    REAL*4        RC_VERT_STAB_AOA                        ! Vertical Stab AOA                 
C&    INTEGER*4     REALFEEL_RESERVED                       ! Reserved for Kernel Mem. Virt. add
C&    LOGICAL*1     REPOS                                   ! Host Repositioning/Trim In Progres
C&    INTEGER*1     RESET_CMD(12)                           ! CLS reset command                 
C&    REAL*4        RFCLGALT                                ! GEAR HEIGHT ABOVE GROUND          
C&    LOGICAL*1     RFMBMJ(20)                              ! FWD MASS JAM INPUT FLAG           
C&    REAL*4        RFMF1BD(20)                             ! BACK DRIVE FORCE No. 1            
C&    REAL*4        RFMF1F(20)                              ! FWD MASS INPUT FORCE BUFFER 1F (lb
C&    REAL*4        RFMF2A(20)                              ! FWD MASS INPUT FORCE BUFFER 2A (lb
C&    REAL*4        RFMF2F(20)                              ! FWD FORCE NO. 2                   
C&    REAL*4        RFMFBW(20)                              ! FWD MASS UNBALANCE (bob wt.) INPUT
C&    REAL*4        RFMXCIN(6)                              ! int var, control pos              
C&    REAL*4        RLXATTAB(4)                             ! AILERON TRIM TAB - # ANGLE        
C&    REAL*4        RLXETTAB(4)                             ! ELEVATOR TRIM TAB - # ANGLE       
C&    REAL*4        RLXNWANG                                ! NOSEWHEEL ANGLE                   
C&    REAL*4        RLXRTTAB                                ! RUDDER TRIM TAB ANGLE             
C&    REAL*4        RLZAIL                                  ! LEFT AIL POS.                     
C&    REAL*4        RLZEL                                   ! LEFT ELEVATOR POSITION            
C&    REAL*4        ROLL_ACC                                ! AIRCRAFT ROLL ACCELERATION        
C&    LOGICAL*1     ROVERCON                                ! rover connected                   
C&    REAL*4        RPAPPCOM                                ! A/P PITCH CMD                     
C&    REAL*4        RPSTCOM(2)                              ! PRIME STAB TRIM COMMD DELTA       
C&    REAL*4        RRAPRC                                  ! AUTOPILOT ROLL COMMAND            
C&    REAL*4        RRAPRR                                  ! A/P ROLL RATE                     
C&    REAL*4        RRCFBDSF(20)                            ! DAMPING SCALE FACTOR              
C&    REAL*4        RRCFBFSF(20)                            ! FORCE SCALE FACTOR                
C&    REAL*4        RRCFBMSF(20)                            ! MASS SCALE FACTOR                 
C&    REAL*4        RRCFBNSPP(40)                           ! NORMALIZED SURFACE POS.           
C&    REAL*4        RRCFBXOF(20)                            ! POSITION OFFSET                   
C&    REAL*4        RRCFBXSF(20)                            ! POSITION SCALE FACTOR             
C&    REAL*4        RRZAIL                                  ! RIGHT AIL POS.                    
C&    REAL*4        RRZEL                                   ! RIGHT ELEV. POSITION              
C&    LOGICAL*1     RSABCKV(40)                             ! SURFACE ACT CHECK VALVE INPUT FLAG
C&    LOGICAL*1     RSABPJ(40)                              ! SURFACE ACT PISTION JAM INPUT FLAG
C&    LOGICAL*1     RSABVJ(40)                              ! SURFACE ACT VALVE JAM INPUT FLAG  
C&    REAL*4        RTATTM1                                 ! Trim Switch Test Input            
C&    REAL*4        RTAXT1                                  ! Trim Position CMD Test Input      
C&    INTEGER*4     RTTASKCNT(10)                           ! RT TASK COUNT                     
C&    LOGICAL*1     RUDBST_ENG                              ! Rudder Boost engaged flag         
C&    REAL*4        RUD_BOOST_FOR                           ! Rudder Boost Force From Autopilot 
C&    REAL*4        RZAICTH                                 ! AIL CONTROL TAB HW POS            
C&    REAL*4        RZCF                                    ! COLUMN FORCE                      
C&    REAL*4        RZCFA                                   ! AFT QUADRANT FORCE                
C&    REAL*4        RZCPS                                   ! STRETCHED COL. POS (DEG)          
C&    REAL*4        RZCPU                                   ! UNSTRETCH COLUMN POSITION         
C&    REAL*4        RZELA                                   ! AVERAGE ELEVATOR POSITION         
C&    REAL*4        RZNWA                                   ! NOSEWHEEL ANGLE                   
C&    REAL*4        RZPF                                    ! PEDAL PILOT FORCE LB              
C&    REAL*4        RZPFA                                   ! RUDDER AFT QUADRANT FORCE         
C&    REAL*4        RZPPS                                   ! PEDAL STRETCHED POSITION          
C&    REAL*4        RZPPU                                   ! PEDAL UNSTRETCHED POSITION        
C&    REAL*4        RZRUD                                   ! RUDDER POSITION                   
C&    REAL*4        RZRUT                                   ! RUDDER TRIM POSITION              
C&    REAL*4        RZWF                                    ! WHEEL FORCE                       
C&    REAL*4        RZWFA                                   ! WHEEL AERO FORCE                  
C&    REAL*4        RZWPS                                   ! STRETCHED WHEEL POSITION          
C&    REAL*4        RZWPU                                   ! UNSTRETCHED WHEEL POSITION        
C&    INTEGER*1     SKID_PLATE                              ! NOSE WHEEL ON SKID PLATE          
C&    REAL*4        STICK_PUSHER                            ! Longitudinal Stick Pusher         
C&    REAL*4        SURF_ALPHA(3)                           ! SURFACE ANGLE OF ATTACK           
C&    REAL*4        SURF_AS_INV(3)                          ! SURFACE INVERSE TRUE AIRSPEED     
C&    REAL*4        SURF_Q(3)                               ! SURFACE DYNAMIC PRESSURE          
C&    INTEGER*4     TCA2RV                                  ! Ail_Rud velocity gearing table    
C&    INTEGER*4     TCA2RX                                  ! Ail_Rud position gearing table    
C&    INTEGER*4     TCAGFLG(20)                             ! Force scaling loader to pilot unit
C&    INTEGER*4     TCAMGM(20)                              ! Aft mass variable mass gain table 
C&    INTEGER*4     TCAPCMD(20)                             ! Table, Velocity Cmd Due to A/P Pos
C&    INTEGER*4     TCAPCWS(20)                             ! Table, CWS Velocity Command       
C&    INTEGER*4     TCASTOR                                 ! castor gain table                 
C&    INTEGER*4     TCAXLGO(20)                             ! Geared loader position offset tabl
C&    INTEGER*4     TCCDV(20)                               ! variable damping coeff table      
C&    INTEGER*4     TCDWASH                                 ! LFI Table                         
C&    INTEGER*4     TCDWASHZ                                ! LFI Table                         
C&    INTEGER*4     TCEMU                                   ! elev mass unbalance table         
C&    INTEGER*4     TCFFFV(20)                              ! fwd mass friction table           
C&    INTEGER*4     TCFFKV(20)                              ! fwd mass kinetic friction table   
C&    INTEGER*4     TCFFV(20)                               ! fwd friction table                
C&    INTEGER*4     TCFGFD(20)                              ! general spring damping table      
C&    INTEGER*4     TCFGFS(20)                              ! general spring table              
C&    INTEGER*4     TCFMCDV(20)                             ! Fwd mass variable damp coeff table
C&    INTEGER*4     TCFMFFLK(20)                            ! Simulator linkage friction lfi    
C&    INTEGER*4     TCFMGFK(20)                             ! Table,kinetic friction gain       
C&    INTEGER*4     TCFMGFKR(20)                            ! Table,clutch/AP kinetic friction g
C&    INTEGER*4     TCFMGXC(20)                             ! Unit conversion lfi               
C&    INTEGER*4     TCFMUBC(20)                             ! force mass unbalance correction ta
C&    INTEGER*4     TCFMXSTR(20)                            ! Stretch lfi                       
C&    INTEGER*4     TCFSCD(20)                              ! feel spring damping table         
C&    INTEGER*4     TCFSFD(20)                              ! feel spring damping table         
C&    INTEGER*4     TCFSFL(20)                              ! feel spring force limit table     
C&    INTEGER*4     TCFSFS(20)                              ! feel spring table                 
C&    INTEGER*4     TCGFFKR(20)                             ! Kinetic friction gain table       
C&    INTEGER*4     TCGFK(20)                               ! Table, Kinetic friction gain (Var 
C&    INTEGER*4     TCGFKR(20)                              ! Table, Kinetic friction gain (Ref 
C&    INTEGER*4     TCGOOSE(12)                             ! Dynamic Goose Term Table          
C&    INTEGER*4     TCGSCD(20)                              ! general spring damping table      
C&    INTEGER*4     TCGSFD(40)                              ! general spring damping tables     
C&    INTEGER*4     TCGSFS(40)                              ! general spring tables             
C&    INTEGER*4     TCHMADD0(10)                            ! additional hinge moment tables for
C&    INTEGER*4     TCHMCHSA1(40)                           ! surf Chm vs aoa table             
C&    INTEGER*4     TCHMCHSA2(40)                           ! surf Chm vs aoa table             
C&    INTEGER*4     TCHMCHSCD(40)                           ! surf hm aero damping coeff table  
C&    INTEGER*4     TCHMCHSO1(40)                           ! additional hm effect term #1      
C&    INTEGER*4     TCHMCHSO2(40)                           ! additional hm effect term #2      
C&    INTEGER*4     TCHMCHSO3(40)                           ! additional hm effect term #3      
C&    INTEGER*4     TCHMCHSXA(40)                           ! surf Chm vs surf aux pos table    
C&    INTEGER*4     TCHMCHSXS(40)                           ! surf Chm vs surf pos table        
C&    INTEGER*4     TCHMDS                                  ! Elevator HM Downspring Table      
C&    INTEGER*4     TCHMFHM(40)                             ! cntl frc vs surf hinge moment tabl
C&    INTEGER*4     TCHMIXO(40)                             ! hinge moment table                
C&    INTEGER*4     TCHMK(40)                               ! surf effectiveness factor table   
C&    INTEGER*4     TCHMKCHSA1(40)                          ! surf Chm vs aoa Factor table      
C&    INTEGER*4     TCHMKCHSA2(40)                          ! surf Chm vs aoa Factor table      
C&    INTEGER*4     TCHMKCHSO1(40)                          ! additional hm effect gain on term 
C&    INTEGER*4     TCHMKCHSO2(40)                          ! additional hm effect gain on term 
C&    INTEGER*4     TCHMKCHSO3(40)                          ! additional hm effect gain on term 
C&    INTEGER*4     TCHMKCHSXA(40)                          ! surf Chm vs surf aux pos Factor ta
C&    INTEGER*4     TCHMKCHSXS(40)                          ! surf Chm vs surf pos Factor table 
C&    INTEGER*4     TCHMKDS                                 ! Elevator HM Downspring Gain       
C&    INTEGER*4     TCHMKFHM(40)                            ! hm gain term                      
C&    INTEGER*4     TCHMXS(40)                              ! control to surf gearing table     
C&    INTEGER*4     TCMU(6)                                 ! On ground mass unbal table        
C&    INTEGER*4     TCNPTXCD                                ! Pedal/Tiller Link lfi             
C&    INTEGER*4     TCNWSX                                  ! NWS Gearing Table                 
C&    INTEGER*4     TCOAILG                                 ! xlfi, OUTBOARD AIL MOTION GAIN (di
C&    INTEGER*4     TCR2AF                                  ! Ail_Rud force gearing table       
C&    INTEGER*4     TCRCGAIN                                ! Rudder ratio changer table        
C&    INTEGER*4     TCRFMGM(20)                             ! mass gain table                   
C&    INTEGER*4     TCRMRF1(20)                             ! mixer force input table 1         
C&    INTEGER*4     TCRMRF2(20)                             ! mixer force input table 2         
C&    INTEGER*4     TCRMRF3(20)                             ! mixer force input table 3         
C&    INTEGER*4     TCRMRF4(20)                             ! mixer force input table 4         
C&    INTEGER*4     TCRMRX1(20)                             ! mixer position input table 1      
C&    INTEGER*4     TCRMRX2(20)                             ! mixer position input table 2      
C&    INTEGER*4     TCRMRX3(20)                             ! mixer position input table 3      
C&    INTEGER*4     TCRMRX4(20)                             ! mixer position input table 4      
C&    INTEGER*4     TCRPXL                                  ! xlfi, RUD LIMIT DUE TO AIR SPEED &
C&    INTEGER*4     TCRPXLB                                 ! xlfi, EFFECT OF SLIDE SLIP ON RUD 
C&    INTEGER*4     TCSACD(40)                              ! Actuator Damping Coeff. Table     
C&    INTEGER*4     TCSACDV1(40)                            ! Act Damp. Coeff. Table,Servo Valve
C&    INTEGER*4     TCSACDV2(40)                            ! Act Damp. Coeff. Table,Servo Valve
C&    INTEGER*4     TCSACDV3(40)                            ! Act Damp. Coeff. Table,Servo Valve
C&    INTEGER*4     TCSACV(40)                              ! control valve scaling table       
C&    INTEGER*4     TCSAFFV(40)                             ! Actuator Friction Table           
C&    INTEGER*4     TCSAFSV1(40)                            ! Actuator Spring Table,Servo Valve 
C&    INTEGER*4     TCSAFSV2(40)                            ! Actuator Spring Table,Servo Valve 
C&    INTEGER*4     TCSAFSV3(40)                            ! Actuator Spring Table,Servo Valve 
C&    INTEGER*4     TCSAVPVL                                ! xlfi, rudder rate limit           
C&    INTEGER*4     TCSAXS(40)                              ! surface position scaling table    
C&    INTEGER*4     TCSF1POS(20)                            ! inverse position scale factor tabl
C&    INTEGER*4     TCSFFRC(20)                             ! force scale factor table          
C&    INTEGER*4     TCSFPOS(20)                             ! position scale factor table       
C&    INTEGER*4     TCTACD(20)                              ! trim actuator damping table       
C&    INTEGER*4     TCTAFS(20)                              ! trim actuator spring table        
C&    INTEGER*4     TCTAXC(20)                              ! commanded trim pos scaling table  
C&    INTEGER*4     TCVAPCU                                 ! PCU Rate Schedule, Ail            
C&    INTEGER*4     TCVEPCU                                 ! PCU Rate Schedule, Elev           
C&    INTEGER*4     TCVRPCU                                 ! PCU Rate Schedule, Rudd           
C&    REAL*4        TLNFCNRG                                ! LOCAL                             
C&    REAL*4        TLNFYMX                                 ! LOCAL                             
C&    REAL*4        TLNFZPT                                 ! LOCAL                             
C&    INTEGER*1     TOW_ENG                                 ! NOSE WHEEL TOW ENGAGED            
C&    REAL*4        TOW_POS                                 ! NOSE WHEEL TOE POSITION           
C&    INTEGER*1     TRM_REL_SW(12)                          ! CLS trim release sw               
C&    INTEGER*1     TRM_RST(3)                              ! TRIM RESET                        
C&    INTEGER*4     TSTBHM                                  ! stab hinge moment table           
C&    LOGICAL*1     UCLDON                                  !    REQUEST CONTROL LOADING ON     
C&    LOGICAL*1     XHPKBRK                                 ! PARKING BRAKE       DI0.2 32-41/00
C&    REAL*4        XTRM_CMD(12)                            ! CLS trim command                  
C&    REAL*4        Y                                       ! LOCAL                             
C&    REAL*4        YAW_ACC                                 ! Aircraft Yaw Acceleration         
C&    REAL*4        YFRSVP                                  ! STAB AXIS TURN YAW/TRU A/S        
C&    INTEGER*4     cqhosttimer                             ! Stab exec host transfer timer     
C&    INTEGER*4     cqhostup                                ! Stab exec host up flag            
C&    INTEGER*4     cqhsdtoggle                             ! Stab exec host transfer toggle    
C&    INTEGER*4     cqreadupdt                              ! Stab exec host new data to read fl
C&    INTEGER*4     ctliook                                 ! Ctl i/o ok flag for real-time     
C&    REAL*4        dortamp(20)                             ! dort channel sine wave amplitude  
C&    REAL*4        dortfmaxn(20)                           ! dort force max pos                
C&    REAL*4        dortfmaxp(20)                           ! dort force max pos                
C&    REAL*4        dortpos(20)                             ! dort channel start position       
C&    INTEGER*1     dortstat                                ! dort status                       
C&    REAL*4        drccount                                ! DRC card counter input float      
C&    INTEGER*4     drcicount                               ! DRC card counter input integer    
C&    LOGICAL*1     electrimdown                            ! Interbus DO-ElecTrimDown          
C&    LOGICAL*1     electrimup                              ! Interbus DO-ElecTrimUp            
C&    INTEGER*4     kcaftm(20)                              ! config index aft mass             
C&    INTEGER*4     kcbd(20)                                ! config index back drive           
C&    INTEGER*4     kccable(20)                             ! config index cable                
C&    INTEGER*4     kcclp1(40)                              ! config gen spr 1 index            
C&    INTEGER*4     kcclp2(40)                              ! config gen spr 2 index            
C&    INTEGER*4     kcclp3(40)                              ! config gen spr 3 index            
C&    INTEGER*4     kcclp4(40)                              ! config gen spr 4 index            
C&    INTEGER*4     kcfeelspr(20)                           ! config index feel spring          
C&    INTEGER*4     kcfwdm(20)                              ! config index fwd mass             
C&    INTEGER*4     kcindx(40)                              ! config index                      
C&    INTEGER*4     kcsact1(20)                             ! config index surface act1         
C&    INTEGER*4     kcsact2(20)                             ! config index surface act2         
C&    INTEGER*4     kcsact3(20)                             ! config index surface act3         
C&    INTEGER*4     kcsact4(20)                             ! config index surface act4         
C&    INTEGER*4     kctable(20)                             ! lfi table pointer                 
C&    INTEGER*4     kctablep(20)                            ! lfi table ind var pointer         
C&    INTEGER*4     kctact(20)                              ! config index trim act             
C&    INTEGER*4     lchwemul(20)                            ! pntr to emulator flag             
C&    INTEGER*4     osnapbuf(24000)                         ! Oscope data snap buffer           
C&    REAL*4        phymem(99999)                           ! phymem  area                      
C&    REAL*8        phymemd(99999)                          ! phymem  area                      
C&    REAL*4        phymemf(99999)                          ! phymem  area                      
C&    INTEGER*4     phymemi(99999)                          ! phymem  area                      
C&    REAL*4        ramafr(20)                              ! Aft mass acceleration due to Refer
C&    REAL*4        ramafrn1(20)                            ! Aft mass acc due to Reference Fri 
C&    REAL*4        ramafs(20)                              ! Aft mass acceleration due to stati
C&    REAL*4        ramafsn1(20)                            ! Aft mass acc due to static Variabl
C&    REAL*4        ramam(20)                               ! MASS ACCELERATION                 
C&    REAL*4        ramamn1(20)                             ! Aft mass acceleration (n-1)       
C&    LOGICAL*1     rambcmd(20)                             ! COMMAND BOOLEAN                   
C&    INTEGER*4     rambmji(20)                             ! MASS JAM BOOLEAN                  
C&    LOGICAL*4     rambxln(20)                             ! aft mass at negative position limi
C&    LOGICAL*4     rambxlp(20)                             ! aft mass at positive position limi
C&    REAL*4        ramcd(20)                               ! DAMPING COEFFICIENT               
C&    REAL*4        ramcdt(20)                              ! total damping coeff               
C&    REAL*4        ramcdv(20)                              ! variable damping coeff            
C&    REAL*4        ramdelt(20)                             ! DELTAT                            
C&    INTEGER*4     ramf1ai(20)                             ! AFT FORCE 1                       
C&    INTEGER*4     ramf1fi(20)                             ! FWD FORCE 1                       
C&    INTEGER*4     ramf2ai(20)                             ! AFT FORCE 2                       
C&    INTEGER*4     ramf2fi(20)                             ! FWD FORCE 2                       
C&    INTEGER*4     ramf3ai(20)                             ! AFT FORCE 3                       
C&    INTEGER*4     ramf4ai(20)                             ! AFT FORCE 4                       
C&    INTEGER*4     ramf5ai(20)                             ! AFT FORCE 5                       
C&    REAL*4        ramfd(20)                               ! DAMPING FORCE                     
C&    REAL*4        ramff(20)                               ! constant static friction force    
C&    REAL*4        ramffst(20)                             ! TOTAL STATIC FRICTION F           
C&    REAL*4        ramfft(20)                              ! total static friction force       
C&    REAL*4        ramffv(20)                              ! VARIABLE FRICTION FORCE           
C&    REAL*4        ramfref(20)                             ! REFERENCE FRICTION FORC           
C&    INTEGER*4     ramfrefi(20)                            ! aft mass reference force          
C&    REAL*4        ramft(20)                               ! TOTAL FORCE ACTING ON M           
C&    REAL*4        ramgf(20)                               ! CURRENT SEGMENT GAIN              
C&    REAL*4        ramgfk(20)                              ! KINETIC FRICTION GAIN             
C&    REAL*4        ramgfkr(20)                             ! Kinetic friction gain (Ref Fri)   
C&    REAL*4        ramgm(20)                               ! 1/MASS OF AFT SYSTEM              
C&    REAL*4        ramgvdrive(20)                          ! Aft Mass AP Drive Vel filter gain 
C&    REAL*4        ramigm(20)                              ! Aft mass intermediate mass gain   
C&    REAL*4        ramivm(20)                              ! INTERM MASS VELOCITY              
C&    REAL*4        ramixm(20)                              ! INTERM MASS POSITION              
C&    INTEGER*4     ramtrli(20)                             ! AFT MASS TRIM RELEASE             
C&    REAL*4        ramvcmd(20)                             ! CMD'D MASS VELOCITY               
C&    REAL*4        ramvcr(20)                              ! CMD'D RED MASS VEL.               
C&    REAL*4        ramvdrive(20)                           ! Aft Mass AP Drive Velocity        
C&    REAL*4        ramvf(20)                               ! VELOCITY MAGNITUDE VFS            
C&    REAL*4        ramvfk(20)                              ! VELOCITY MAGNITUDE VFS            
C&    REAL*4        ramvfs(20)                              ! VELOCITY MAGNITUDE FFST           
C&    REAL*4        ramvfsr(20)                             ! Velocity Magnitude of Fref        
C&    REAL*4        ramvm(20)                               ! MASS VELOCITY                     
C&    REAL*4        ramvmax(20)                             ! Maximun allowed aft mass velocity 
C&    REAL*4        ramvmn1(20)                             ! Aft mass velocity (n-1)           
C&    REAL*4        ramvr(20)                               ! VELOCITY REDUCTION VF             
C&    REAL*4        ramvref(20)                             ! RECFERENCE VELOCITY               
C&    REAL*4        ramvrefe(20)                            ! Reference velocity error          
C&    INTEGER*4     ramvrefi(20)                            ! aft mass reference velocity       
C&    REAL*4        ramvslp(20)                             ! Clutch Slip Velocity              
C&    INTEGER*4     ramxfi(20)                              ! AFT MASS FWD POS                  
C&    REAL*4        ramxm(20)                               ! AFT MASS POSITION                 
C&    REAL*4        ramxmln(20)                             ! MASS POSITION LIMIT NEG           
C&    REAL*4        ramxmlp(20)                             ! MASS POSITION LIMIT POS           
C&    LOGICAL*1     rapbcws(20)                             ! CWS Active 1=Active               
C&    REAL*4        rapdelt(20)                             ! Delta Time, 1/iteration rate      
C&    INTEGER*4     rapengi(20)                             ! Autopilot/CWS Engaged (1=A/P, 2=CW
C&    INTEGER*4     rapf1i(20)                              ! Input Force #1                    
C&    INTEGER*4     rapf2i(20)                              ! Input Force #2                    
C&    REAL*4        rapfcws(20)                             ! Control Wheel Steering (CWS) Force
C&    REAL*4        rapfmax(20)                             ! Maximun Override Force            
C&    REAL*4        rapfor(20)                              ! A/P Actuator Override Force       
C&    REAL*4        rapgf1(20)                              ! Gain, Input Force #1              
C&    REAL*4        rapgf2(20)                              ! Gain, Input Force #2              
C&    REAL*4        rapgramp(20)                            ! Gain, Autopilot Engage Ramp       
C&    REAL*4        rapgtau(20)                             ! Gain, Time Constant               
C&    REAL*4        rapgv(20)                               ! A/P Input Velocity Gain           
C&    REAL*4        rapgx1(20)                              ! Gain, Autopilot Position Input #1 
C&    REAL*4        rapgx2(20)                              ! Gain, Autopilot Position Input #2 
C&    REAL*4        rapramp(20)                             ! Autopilot Engage Ramp             
C&    REAL*4        rapvap(20)                              ! Autopilot Actuator Velocity Comman
C&    REAL*4        rapvapln(20)                            ! A/P Act Velocity Command Nagative 
C&    REAL*4        rapvaplp(20)                            ! A/P Act Velocity Command Positive 
C&    REAL*4        rapvcmd(20)                             ! Velocity Cmd Due to A/P Position e
C&    REAL*4        rapvcws(20)                             ! CWS Velocity Command              
C&    INTEGER*4     rapvi(20)                               ! autopilot velocity input          
C&    REAL*4        rapx1(20)                               ! Limited autopilot (A/P) command po
C&    INTEGER*4     rapx1i(20)                              ! Autopilot Position Input #1 (Comma
C&    INTEGER*4     rapx2i(20)                              ! Autopilot Position Input #2 (Feedb
C&    REAL*4        rapxapln(20)                            ! A/P negative command limit        
C&    REAL*4        rapxaplp(20)                            ! A/P positive command limit        
C&    REAL*4        rapxcmd(20)                             ! Autopilot Position Command        
C&    REAL*4        rapxe(20)                               ! Autopilot Position Error          
C&    LOGICAL*1     rbdbatg(20)                             ! ATG ACTIVE BOOLEAN                
C&    INTEGER*4     rbdbftgi(20)                            ! ATG ACTIVE BOOLEAN FOR            
C&    INTEGER*4     rbdbxtgi(20)                            ! ATG ACTIVE BOOLEAN POS            
C&    REAL*4        rbddelt(20)                             ! DELTA TIME                        
C&    LOGICAL*1     rbdfastoff(20)                          ! fast fade out for back drive      
C&    INTEGER*4     rbdfatgi(20)                            ! ATG FORCE COMMAND                 
C&    REAL*4        rbdfbd(20)                              ! BACKDRIVE FORCE                   
C&    REAL*4        rbdfder(20)                             ! Back drive derivitive force term  
C&    REAL*4        rbdfint(20)                             ! Back drive integeral force term   
C&    REAL*4        rbdflbd(20)                             ! BACKDRIVE FORCE LIMIT             
C&    REAL*4        rbdflmax(20)                            ! Max atg force                     
C&    INTEGER*4     rbdflmaxi(20)                           ! normalized max back drive force   
C&    INTEGER*4     rbdfmi(20)                              ! FORCE ON MASS                     
C&    REAL*4        rbdfor(20)                              ! ATG OVERRIDE FORCE                
C&    REAL*4        rbdfpro(20)                             ! Back drive proportional force term
C&    REAL*4        rbdftgm(20)                             ! MAXIMUM ATG FORCE                 
C&    REAL*4        rbdg2(20)                               ! FILTER STATE NO.2 GAIN            
C&    REAL*4        rbdgder(20)                             ! Derivitive gain                   
C&    REAL*4        rbdgf(20)                               ! backdrive output force gain       
C&    REAL*4        rbdgfastoff(20)                         ! fast fade out 1/time constant     
C&    REAL*4        rbdgint(20)                             ! Integral gain                     
C&    REAL*4        rbdgloff(20)                            ! Atg off gain                      
C&    REAL*4        rbdglon(20)                             ! Atg on gain                       
C&    REAL*4        rbdgpro(20)                             ! Proportional gain                 
C&    REAL*4        rbdgv(20)                               ! velocity input gain               
C&    REAL*4        rbdgx(20)                               ! position input gain               
C&    REAL*4        rbdgxe(20)                              ! POSITION ERROR GAIN               
C&    REAL*4        rbdifbd(20)                             ! INTERM BACKDRIVE FORCE            
C&    REAL*4        rbdlagtau                               ! lag tau for atg input command     
C&    INTEGER*4     rbdvmi(20)                              ! Backdrive vel input               
C&    REAL*4        rbdvmn1(20)                             ! Back drive velocity (n-1)         
C&    REAL*4        rbdx1(20)                               ! FILTER STATE 1                    
C&    REAL*4        rbdx2(20)                               ! FILTER STATE 2                    
C&    INTEGER*4     rbdxatgi(20)                            ! ATG POSITION COMMAND              
C&    REAL*4        rbdxatgl(20)                            ! lagged atg input command          
C&    REAL*4        rbdxe(20)                               ! Backdrive error                   
C&    REAL*4        rbdxen1(20)                             ! Back drive position error (n-1)   
C&    INTEGER*4     rbdxmi(20)                              ! MASS POSITION                     
C&    REAL*4        rbrkcdn(2)                              ! Normal Toe Brake Cd (fwd mass) neg
C&    REAL*4        rbrkcdp(2)                              ! Normal Toe Brake Cd (fwd mass) pos
C&    REAL*4        rcaAc_f(120)                            ! Accel... filter array             
C&    INTEGER*4     rcaAc_ft(20)                            ! rca accel filter table index 0-7  
C&    REAL*4        rcaFa_f(120)                            ! Force    filter array abort       
C&    INTEGER*4     rcaFa_ft(20)                            ! rca force abort filter index 0-7  
C&    REAL*4        rcaFc_f(120)                            ! Force    filter array             
C&    INTEGER*4     rcaFc_ft(20)                            ! rca force filter table index 0-7  
C&    REAL*4        rcaVa_f(120)                            ! Velocity filter array abort       
C&    INTEGER*4     rcaVa_ft(20)                            ! rca velocity abort filter index 0-
C&    REAL*4        rcaVc_f(120)                            ! Velocity filter array             
C&    INTEGER*4     rcaVc_ft(20)                            ! rca velocity filter table index 0-
C&    REAL*4        rcaXa_f(120)                            ! Position filter array abort       
C&    INTEGER*4     rcaXa_ft(20)                            ! rca position error abort filter in
C&    REAL*4        rcaXc_f(120)                            ! Position filter array             
C&    INTEGER*4     rcaXc_ft(20)                            ! rca position filter table index 0-
C&    REAL*4        rcaaacc(20)                             ! actuator angular accel            
C&    INTEGER*4     rcaabort(20)                            ! servo drive abort flags.          
C&    REAL*4        rcaac(20)                               ! Acceleration in cockpit units     
C&    REAL*4        rcaacn1(20)                             ! Acceleration in cockpit units N-1 
C&    REAL*4        rcaae(20)                               ! Servo accel..  error              
C&    LOGICAL*1     rcaafcs_rel                             ! afcs release switch               
C&    REAL*4        rcaalg(20)                              ! Geared loader acceleration        
C&    REAL*4        rcaalgn1(20)                            ! Geared loader acceleration N-1    
C&    REAL*4        rcaamg(20)                              ! actuator mass gain                
C&    INTEGER*4     rcaami(20)                              ! Pointer mass accel    input       
C&    REAL*4        rcaamp_volt(20)                         ! current scaling gain              
C&    LOGICAL*1     rcaampfail(20)                          ! channel motor amp fail            
C&    REAL*4        rcaao(20)                               ! servo drive output i/o.           
C&    REAL*4        rcaaoc(20)                              ! command drive ao                  
C&    REAL*4        rcaaoelim(20)                           ! command drive ao easy on limit    
C&    REAL*4        rcaaolim(20)                            ! servo drive output limit.         
C&    REAL*4        rcaaolimt(20)                           ! test max ao limit                 
C&    INTEGER*4     rcaaovrd(20)                            ! servo drive abort overide flag.   
C&    REAL*4        rcaapos(20)                             ! actuator angular pos              
C&    REAL*4        rcaaposc(20)                            ! actuator pos correction           
C&    REAL*4        rcaaposcg(20)                           ! actuator pos correction gain      
C&    REAL*4        rcaate(20)                              ! actuator applied torque           
C&    REAL*4        rcaateg(20)                             ! actuator applied torque gain      
C&    REAL*4        rcaati(20)                              ! actuator torque from load cell    
C&    REAL*4        rcaatig(20)                             ! actuator torque from load cell gai
C&    LOGICAL*1     rcaattd_dn                              ! attd down switch                  
C&    LOGICAL*1     rcaattd_up                              ! attd up switch                    
C&    REAL*4        rcaavel(20)                             ! actuator angular vel              
C&    REAL*4        rcaavelc(20)                            ! actuator angular vel correction   
C&    REAL*4        rcaavelcg(20)                           ! actuator angular vel correction ga
C&    INTEGER*2     rcabufai(120)                           ! io ai buffer                      
C&    INTEGER*2     rcabufao(120)                           ! io ao buffer                      
C&    LOGICAL*1     rcabufdi(128)                           ! io di buffer                      
C&    LOGICAL*1     rcabufdo(128)                           ! io do buffer                      
C&    INTEGER*1     rcachantype(12)                         ! channel type                      
C&    REAL*4        rcacurrent(20)                          ! current to motor                  
C&    REAL*4        rcacurrentc(20)                         ! calculated current                
C&    INTEGER*4     rcacurrenti(20)                         ! current input pointer             
C&    REAL*4        rcacurrentlag                           ! lag on current                    
C&    INTEGER*4     rcadidomask                             ! dido test mask                    
C&    INTEGER*4     rcadidomaskt                            ! dido intermediate test mask       
C&    INTEGER*4     rcadidonum                              ! dido test number                  
C&    INTEGER*4     rcadidotest                             ! dido test mode                    
C&    REAL*4        rcaeasy(20)                             ! servo drive easy on ramp.         
C&    REAL*4        rcaeasy2(20)                            ! servo drive easy on ramp second st
C&    REAL*4        rcaeasyl(20)                            ! easy on limit until pos sync      
C&    REAL*4        rcaenctraverr(20)                       ! test allowable encoder travel erro
C&    REAL*4        rcaencv(20)                             ! encoder velocity in control units 
C&    REAL*4        rcaencx(20)                             ! encoder position in control units 
C&    REAL*4        rcaf2amp(20)                            ! force to amps conversion          
C&    REAL*4        rcafa(20)                               ! Abort velocity                    
C&    INTEGER*4     rcafai(20)                              ! Pointer Force transducer input i/o
C&    REAL*4        rcafaio(20)                             ! Force transducer input i/o offset 
C&    REAL*4        rcafalag                                ! lag on force error in servo       
C&    REAL*4        rcafc(20)                               ! Force in cockpit units            
C&    REAL*4        rcafclag(20)                            ! Servo control lag in frc input    
C&    REAL*4        rcafclf(20)                             ! Force lim during fade in          
C&    REAL*4        rcafclim(20)                            ! servo drive force limit for abort.
C&    REAL*4        rcafclon(20)                            ! Force lim during nor operation    
C&    REAL*4        rcafcn1(20)                             ! Force in cockpit units N-1        
C&    REAL*4        rcafl(20)                               ! Force transducer in loader units. 
C&    REAL*4        rcaflg(20)                              ! Geared loader force               
C&    REAL*4        rcaflgn1(20)                            ! Geared loader force N-1           
C&    REAL*4        rcaflgo(20)                             ! Geared loader force offset.       
C&    REAL*4        rcaflin(20)                             ! Force transducer input biased.    
C&    REAL*4        rcafltai(120)                           ! io ai buffer float                
C&    REAL*4        rcafltao(120)                           ! io ao buffer float                
C&    REAL*4        rcafmubc(20)                            ! force mass unbalance correction   
C&    REAL*4        rcafnlim(20)                            ! min attained force                
C&    REAL*4        rcafplim(20)                            ! max attained force                
C&    REAL*4        rcagcount(20)                           ! encoder count scale factor (counts
C&    REAL*4        rcagflg(20)                             ! Geared loader force gain          
C&    REAL*4        rcagfln(20)                             ! Force transducer input gain negati
C&    REAL*4        rcagflp(20)                             ! Force transducer input gain positi
C&    REAL*4        rcagmtr(20)                             ! mtr position gain                 
C&    LOGICAL*1     rcagrnthumb                             ! rover green thumb                 
C&    REAL*4        rcagvcmd(20)                            ! Fade in vel command gain          
C&    REAL*4        rcagxl(20)                              ! Position transducer input gain    
C&    REAL*4        rcagxlg(20)                             ! Geared loader position gain       
C&    INTEGER*4     rcaicount(20)                           ! encoder count                     
C&    INTEGER*4     rcaicount1(20)                          ! encoder count last pass           
C&    INTEGER*4     rcaiframe(20)                           ! lag frame                         
C&    LOGICAL*1     rcainhibit(20)                          ! channel inhibit                   
C&    INTEGER*4     rcaiobuf(1000)                          ! io buffer                         
C&    REAL*4        rcairate(20)                            ! Module itteration rate            
C&    REAL*4        rcaitime(20)                            ! Module itteration time interval.  
C&    REAL*4        rcaixc(20)                              ! Intermediate Servo Position       
C&    INTEGER*1     rcakfilt(20)                            ! filter select                     
C&    REAL*4        rcalag(20)                              ! leadlag lag term                  
C&    REAL*4        rcalead(20)                             ! leadlag lead term                 
C&    LOGICAL*1     rcamaint(20)                            ! channel maintenance switch        
C&    REAL*4        rcamaxpwr(20)                           ! max power for current limiting    
C&    REAL*4        rcamaxvolt(20)                          ! max voltage to motor amp for curre
C&    LOGICAL*1     rcamidthumb                             ! rover mid thumb                   
C&    REAL*4        rcamutab(80)                            ! mass unbalance temp data          
C&    REAL*4        rcanullfai(20)                          ! force ai null                     
C&    REAL*4        rcanullfail(20)                         ! force ai null lagged              
C&    REAL*4        rcanullpai(20)                          ! test computed pot ai offset       
C&    LOGICAL*1     rcaon(20)                               ! Loader ON                         
C&    INTEGER*4     rcaoncnt(20)                            ! host cmd reset/start chan time cou
C&    LOGICAL*1     rcaovrtmp(20)                           ! over temp flag                    
C&    REAL*4        rcap1(20)                               ! leadlag term                      
C&    REAL*4        rcap2(20)                               ! leadlag term                      
C&    REAL*4        rcapaf(20)                              ! Pilot's applied force.            
C&    LOGICAL*1     rcapitch_dn                             ! rover pitch down                  
C&    LOGICAL*1     rcapitch_up                             ! rover pitch up                    
C&    INTEGER*4     rcapkdido(16)                           ! raw dido packed buffer            
C&    REAL*4        rcapkramp                               ! auto pilot fadein time            
C&    REAL*4        rcapotctr(20)                           ! test allowable pot center error   
C&    REAL*4        rcapotctrai(20)                         ! test allowable pot ai center error
C&    REAL*4        rcapotenctrackerr(20)                   ! test allowable pot/encoder trackin
C&    REAL*4        rcapottraverr(20)                       ! test allowable pot travel error   
C&    REAL*4        rcapower(20)                            ! power used                        
C&    REAL*4        rcapowerc(20)                           ! calculated power used             
C&    REAL*4        rcapowerout(20)                         ! power dissapated                  
C&    REAL*4        rcapramp(20)                            ! auto pilot fadein                 
C&    REAL*4        rcapwrbase(180)                         ! power limits and abort base       
C&    REAL*4        rcapwrlim(20)                           ! motor power limit                 
C&    LOGICAL*1     rcapwrok(20)                            ! channel power ok                  
C&    REAL*4        rcaratedcurr(20)                        ! motor rated current               
C&    LOGICAL*1     rcaredthumb                             ! rover red thumb                   
C&    LOGICAL*1     rcareset(20)                            ! reset flag                        
C&    LOGICAL*1     rcaroll_lt                              ! rover roll left                   
C&    LOGICAL*1     rcaroll_rt                              ! rover roll right                  
C&    INTEGER*4     rcarsettm(20)                           ! reset counter                     
C&    LOGICAL*1     rcarun(20)                              ! servo drive run flag.             
C&    INTEGER*4     rcaruncnt(20)                           ! channel run count                 
C&    REAL*4        rcaruntime(20)                          ! channel run time                  
C&    REAL*4        rcascmd(20)                             ! Servo drive commanded.            
C&    REAL*4        rcascmdf(20)                            ! Servo drive fadein commanded.     
C&    REAL*4        rcascmdn(20)                            ! Servo drive normal commanded.     
C&    REAL*4        rcaskd(20)                              ! Servo derivative gain   (accel..  
C&    REAL*4        rcaskf(20)                              ! Servo feed forward      (force    
C&    REAL*4        rcaski(20)                              ! Servo integral gain     (position 
C&    REAL*4        rcaskif(20)                             ! Servo integral fadein gain (positi
C&    REAL*4        rcaskife(20)                            ! Fade in pot pos err gain          
C&    REAL*4        rcaskit(20)                             ! test loop position error gain     
C&    REAL*4        rcaskp(20)                              ! Servo proportional gain (velocity 
C&    REAL*4        rcasnull(20)                            ! servo drive output offset null.   
C&    LOGICAL*4     rcastart(20)                            ! servo drive startup flag.         
C&    REAL*4        rcatampol(20)                           ! test open loop ao cmd             
C&    REAL*4        rcatampsol(20)                          ! test open loop ao step cmd        
C&    INTEGER*4     rcaterror(20)                           ! test status error                 
C&    LOGICAL*1     rcatestdido(64)                         ! dido test result                  
C&    REAL*4        rcatime(20)                             ! servo drive easy on timer incremen
C&    INTEGER*4     rcatno(20)                              ! test number                       
C&    INTEGER*4     rcatnop(20)                             ! test number previous              
C&    LOGICAL*1     rcatoggle(20)                           ! channel toggle                    
C&    LOGICAL*1     rcatrigger                              ! rover trigger                     
C&    LOGICAL*1     rcatriggerh                             ! trgger half switch                
C&    REAL*4        rcatstgn(2)                             ! test ai scale gain                
C&    REAL*4        rcatstin(2)                             ! test ai scaled input              
C&    REAL*4        rcatsttime(20)                          ! test time                         
C&    LOGICAL*1     rcausepot(20)                           ! Use pot only (no encoder)         
C&    REAL*4        rcava(20)                               ! Abort velocity                    
C&    INTEGER*4     rcavai(20)                              ! VELOCITY AI INPUT POINTER         
C&    REAL*4        rcavalag                                ! lag on vel error in servo         
C&    REAL*4        rcavc(20)                               ! Velocity in cockpit units         
C&    REAL*4        rcavcg(20)                              ! Servo control actuator vel gain   
C&    REAL*4        rcavclag(20)                            ! lag const on actuator vel in cntrl
C&    REAL*4        rcavclf(20)                             ! Vel lim during fade in            
C&    REAL*4        rcavclim(20)                            ! servo drive velocity limit for abo
C&    REAL*4        rcavclon(20)                            ! Vel lim during normal operation   
C&    REAL*4        rcavcmd(20)                             ! Fade in vel command               
C&    INTEGER*4     rcavcmdi(20)                            ! Fade in vel command input         
C&    REAL*4        rcavcmdl(20)                            ! Fade in vel command limit         
C&    REAL*4        rcavcmdt(20)                            ! test velocity command             
C&    REAL*4        rcavcn1(20)                             ! Velocity in cockpit units N-1     
C&    REAL*4        rcave(20)                               ! Servo Velocity error              
C&    REAL*4        rcaveg(20)                              ! Servo control vel err gain        
C&    REAL*4        rcavegf(20)                             ! Servo control fadein vel err gain 
C&    REAL*4        rcavegt(20)                             ! test loop velocity error gain     
C&    REAL*4        rcavie(20)                              ! Servo control vel err integral    
C&    REAL*4        rcavieg(20)                             ! Servo control vel err integral gai
C&    REAL*4        rcavl(20)                               ! loader velocity                   
C&    REAL*4        rcavlg(20)                              ! Geared loader velocity            
C&    REAL*4        rcavlgn1(20)                            ! Geared loader velocity n-1        
C&    REAL*4        rcavll(20)                              ! intermediate actuator velocity    
C&    INTEGER*4     rcavmi(20)                              ! Pointer mass velocity input       
C&    REAL*4        rcawraperr(20)                          ! ao ai wrap error                  
C&    REAL*4        rcawraperrlim(20)                       ! ao ai wrap error limit            
C&    REAL*4        rcaxa(20)                               ! Abort position                    
C&    INTEGER*4     rcaxai(20)                              ! Pointer Position transducer input 
C&    REAL*4        rcaxaio(20)                             ! Position transducer input i/o offs
C&    REAL*4        rcaxalag                                ! lag on pos error in servo         
C&    REAL*4        rcaxc(20)                               ! Position in cockpit units         
C&    REAL*4        rcaxclag(20)                            ! Servo control lag in pos input    
C&    REAL*4        rcaxcn1(20)                             ! Position in cockpit units N-1     
C&    REAL*4        rcaxe(20)                               ! Servo Position error              
C&    REAL*4        rcaxee(20)                              ! encoder pos error from pot        
C&    REAL*4        rcaxeea(20)                             ! lagged encoder pos error from pot 
C&    REAL*4        rcaxeealag                              ! lag on pot pos error in servo     
C&    REAL*4        rcaxeeg(20)                             ! pot to encoder err gain           
C&    REAL*4        rcaxeel(20)                             ! encoder/pot error correction      
C&    REAL*4        rcaxeelg(20)                            ! encoder/pot error correction gain 
C&    REAL*4        rcaxeelim(20)                           ! encoder pos error from pot limit  
C&    REAL*4        rcaxeelt(20)                            ! encoder/pot error correction 1/tau
C&    REAL*4        rcaxeelv(20)                            ! encoder/pot error correction rate 
C&    REAL*4        rcaxelf(20)                             ! Pos err lim during fade in        
C&    REAL*4        rcaxelim(20)                            ! servo drive position error limit f
C&    REAL*4        rcaxelon(20)                            ! Pos err lim during nor operation  
C&    REAL*4        rcaxie(20)                              ! Servo control pos err integral    
C&    REAL*4        rcaxieg(20)                             ! Servo control pos err integral gai
C&    REAL*4        rcaxl(20)                               ! Position transducer in loader unit
C&    REAL*4        rcaxlg(20)                              ! Geared loader position            
C&    REAL*4        rcaxlgn1(20)                            ! Geared loader position N-1        
C&    REAL*4        rcaxlgo(20)                             ! Geared loader position offset     
C&    REAL*4        rcaxlgok                                ! position offset 1/tau             
C&    REAL*4        rcaxll(20)                              ! pot lagged for encoder sync       
C&    REAL*4        rcaxllag(20)                            ! position pot lag                  
C&    INTEGER*4     rcaxmi(20)                              ! Pointer mass position input       
C&    REAL*4        rcaxmsn(20)                             ! test position limit negative      
C&    REAL*4        rcaxmsp(20)                             ! test position limit positive      
C&    REAL*4        rcaxmt(20)                              ! test position command             
C&    REAL*4        rcaxnlim(20)                            ! min attained position             
C&    REAL*4        rcaxnlimp(20)                           ! test negative pot limit           
C&    REAL*4        rcaxnlimpai(20)                         ! test negative ai limit            
C&    REAL*4        rcaxplim(20)                            ! max attained position             
C&    REAL*4        rcaxplimp(20)                           ! test positive pot limit           
C&    REAL*4        rcaxplimpai(20)                         ! test positive ai limit            
C&    REAL*4        rcaxsc(20)                              ! stretch compensation              
C&    REAL*4        rcaxtote(20)                            ! test required total encoder travel
C&    REAL*4        rcaxtotp(20)                            ! test required total pot travel    
C&    REAL*4        rcbcddb(20)                             ! DEADBAND DAMPING COEFF.           
C&    REAL*4        rcbcdn(20)                              ! DAMPING NEG SEGMENT               
C&    REAL*4        rcbcdp(20)                              ! DAMPING POS SEGMENT               
C&    REAL*4        rcbfd(20)                               ! DAMPING FORCE                     
C&    REAL*4        rcbfs(20)                               ! CABLE SPRING FORCE                
C&    REAL*4        rcbft(20)                               ! TOTAL CABLE FORCE                 
C&    REAL*4        rcbkn(20)                               ! NEG SEG SPRING RATE               
C&    REAL*4        rcbkp(20)                               ! POS SEG SPRING RATE               
C&    INTEGER*4     rcbv1i(20)                              ! VELOCITY INPUT 1                  
C&    INTEGER*4     rcbv2i(20)                              ! VELOCITY INPUT 2                  
C&    REAL*4        rcbve(20)                               ! VELOCITY ERROR                    
C&    INTEGER*4     rcbx1i(20)                              ! POSITION INPUT 1                  
C&    INTEGER*4     rcbx2i(20)                              ! POSITION INPUT 2                  
C&    REAL*4        rcbxdb(20)                              ! CABLE DEADBAND                    
C&    REAL*4        rcbxe(20)                               ! POSITION ERROR                    
C&    REAL*4        rcefcnt(20)                             ! raw encoder count float           
C&    REAL*4        rcefcntd(20)                            ! intermediate actuator velocity    
C&    INTEGER*4     rceicnt(20)                             ! raw encoder count integer         
C&    REAL*4        rcfadein(20)                            ! channel fade in                   
C&    REAL*4        rcfadeinrate                            ! channel fade in rate              
C&    REAL*4        rcfrccor(20)                            ! nonlinear sim linkage force correc
C&    REAL*4        rcfrccorf(20)                           ! nonlinear sim linkage frc to model
C&    REAL*4        rcfrccorlm(20)                          ! nonlinear sim linkage frc cor limi
C&    REAL*4        rcfrccorp(20)                           ! nonlinear sim linkage frc to pos c
C&    INTEGER*1     rchar0                                  ! zero char value                   
C&    REAL*4        rchspare(40)                            ! high speed controls spares        
C&    REAL*4        rcrawfrc(20)                            ! raw force input                   
C&    REAL*4        rcrawfrcc(20)                           ! unbalance correction force        
C&    REAL*4        rcrawpos(20)                            ! raw position input                
C&    REAL*4        rcrawvel(20)                            ! raw velocity input                
C&    REAL*4        rcsam(20)                               ! MASS ACCELERATION                 
C&    INTEGER*4     rcsbcmd(20)                             ! COMMAND BOOLEAN                   
C&    INTEGER*4     rcsbmji(20)                             ! MASS JAM BOOLEAN                  
C&    REAL*4        rcscd(20)                               ! DAMPING COEFFICIENT               
C&    REAL*4        rcsdelt(20)                             ! DELTAT                            
C&    INTEGER*4     rcsf1ai(20)                             ! AFT FORCE 1                       
C&    INTEGER*4     rcsf1fi(20)                             ! FWD FORCE 1                       
C&    REAL*4        rcsf1frc(20)                            ! 1 over force scale factor         
C&    REAL*4        rcsf1pos(20)                            ! 1 over position scale factor      
C&    REAL*4        rcsf1vel(20)                            ! 1 over velocity scale factor      
C&    INTEGER*4     rcsf2ai(20)                             ! AFT FORCE 2                       
C&    INTEGER*4     rcsf2fi(20)                             ! FWD FORCE 2                       
C&    INTEGER*4     rcsf3ai(20)                             ! AFT FORCE 3                       
C&    INTEGER*4     rcsf4ai(20)                             ! AFT FORCE 4                       
C&    INTEGER*4     rcsf5ai(20)                             ! AFT FORCE 5                       
C&    REAL*4        rcsfd(20)                               ! DAMPING FORCE                     
C&    REAL*4        rcsffrc(20)                             ! force scale factor                
C&    REAL*4        rcsffst(20)                             ! TOTAL STATIC FRICTION F           
C&    REAL*4        rcsffv(20)                              ! VARIABLE FRICTION FORCE           
C&    REAL*4        rcsfhm(20)                              ! HINGE MOMENT FORCE                
C&    INTEGER*4     rcsfhmi(20)                             ! INPUT HINGE MOMENT FORCE          
C&    REAL*4        rcsfpos(20)                             ! position scale factor             
C&    REAL*4        rcsfref(20)                             ! REFERENCE FRICTION FORC           
C&    REAL*4        rcsft(20)                               ! TOTAL FORCE ACTING ON M           
C&    REAL*4        rcsfvel(20)                             ! velocity scale factor             
C&    REAL*4        rcsgf(20)                               ! CURRENT SEGMENT GAIN              
C&    REAL*4        rcsgfk(20)                              ! KINETIC FRICTION GAIN             
C&    REAL*4        rcsghm(20)                              ! HINGE MOMENT GAIN                 
C&    REAL*4        rcsgm(20)                               ! 1/MASS OF AFT SYSTEM              
C&    REAL*4        rcsivm(20)                              ! INTERM MASS VELOCITY              
C&    REAL*4        rcsixm(20)                              ! INTERM MASS POSITION              
C&    REAL*4        rcstretch(20)                           ! sim linkage stretch in control uni
C&    REAL*4        rcstretchk(20)                          ! sim linkage stretch spring constan
C&    REAL*4        rcstretchlm(20)                         ! sim linkage stretch limit         
C&    REAL*4        rcsvcmd(20)                             ! CMD'D MASS VELOCITY               
C&    REAL*4        rcsvcr(20)                              ! CMD'D RED MASS VEL.               
C&    REAL*4        rcsvf(20)                               ! VELOCITY MAGNITUDE VFS            
C&    REAL*4        rcsvfk(20)                              ! VELOCITY MAGNITUDE VFS            
C&    REAL*4        rcsvfs(20)                              ! VELOCITY MAGNITUDE FFST           
C&    REAL*4        rcsvm(20)                               ! MASS VELOCITY                     
C&    REAL*4        rcsvr(20)                               ! VELOCITY REDUCTION VF             
C&    REAL*4        rcsvref(20)                             ! RECFERENCE VELOCITY               
C&    REAL*4        rcsxm(20)                               ! AFT MASS POSITION                 
C&    REAL*4        rcsxmln(20)                             ! MASS POSITION LIMIT NEG           
C&    REAL*4        rcsxmlp(20)                             ! MASS POSITION LIMIT POS           
C&    REAL*4        rct1slope(20)                           ! inverse position scaling slope    
C&    REAL*4        rctsf1pos(20)                           ! table inverse position scaling out
C&    REAL*4        rctsffrc(20)                            ! force scale factor output         
C&    REAL*4        rctsfpos(20)                            ! table position scaling output     
C&    REAL*4        rctslope(20)                            ! position scaling slope            
C&    REAL*4        rcwhlsdot                               ! wheel stretch rate                
C&    REAL*4        rcwhlsdotlm                             ! wheel stretch rate limit          
C&    REAL*4        rcwhlsfgn                               ! wheel stretch force gain          
C&    REAL*4        rcwhlslag                               ! wheel stretch lag                 
C&    REAL*4        rcwhlspos                               ! wheel stretch position            
C&    REAL*4        rczero                                  ! zero value                        
C&    REAL*4        rdforce                                 ! digital force measure output      
C&    REAL*4        rdlaserdist                             ! digital laser distance output     
C&    REAL*4        rdlaseroffset                           ! digital laser distance offset     
C&    REAL*4        rdprotractor                            ! digital proctractor output        
C&    INTEGER*4     rfgbcli(20)                             ! LINEAR/ROTARY LOADER SWITCH       
C&    INTEGER*4     rfgfai(20)                              ! FORCE TRANSDUCER INPUT            
C&    REAL*4        rfgfao(20)                              ! CMD'D LOADER FORCE AO             
C&    REAL*4        rfgfcl(20)                              ! CONT FORCE LINEAR                 
C&    REAL*4        rfgfclc(20)                             ! CMD'D CONT FORCE (LIN)            
C&    REAL*4        rfgfct(20)                              ! CONT FORCE TANGENTAL              
C&    INTEGER*4     rfgfctci(20)                            ! CMD'D CONT FORCE (TAN)            
C&    REAL*4        rfgff(20)                               ! FRICTION FORCE                    
C&    REAL*4        rfgffn(20)                              ! NEGATIVE FRICTION VALUE           
C&    REAL*4        rfgffp(20)                              ! POSITIVE FRICTION VALUE           
C&    REAL*4        rfgfll(20)                              ! LOADER FORCE LINEAR               
C&    REAL*4        rfgfllc(20)                             ! CMD'D LOADER FORCE (LIN)          
C&    REAL*4        rfgflt(20)                              ! LOADER FORCE LINEAR (TAN)         
C&    REAL*4        rfgfltc(20)                             ! CMD'D LOADER FORCE (TAN)          
C&    REAL*4        rfgfpt(20)                              ! CONT FORCE AT PILOT'S HAND TAN    
C&    REAL*4        rfggf(20)                               ! FRICTION GAIN                     
C&    REAL*4        rfggfo(20)                              ! OUTPUT FORCE GAIN                 
C&    REAL*4        rfgglr(20)                              ! LIN TO RADIAL GAIN 1/RC           
C&    REAL*4        rfggma(20)                              ! MECH ADV (RC/RL)                  
C&    REAL*4        rfggxp(20)                              ! POSITION XDUCER GAIN              
C&    REAL*4        rfggxv(20)                              ! VELOCITY TRANSDUCER GAIN          
C&    REAL*4        rfgiff(20)                              ! INTERM FRICTION FORCE             
C&    REAL*4        rfgigxf(20)                             ! FORCE TRANSDUCER GAIN             
C&    REAL*4        rfgkl(20)                               ! LINKAGE STIFFNESS (LIN)           
C&    REAL*4        rfgkt(20)                               ! LINKAGE STIFFNESS (RAD)           
C&    INTEGER*4     rfgvai(20)                              ! VELOCITY TRANSDUCER INPUT         
C&    REAL*4        rfgvdeg(20)                             ! CONT VELOCITY IN DEGS             
C&    REAL*4        rfgvlin(20)                             ! LINEAR LOADER VELOCITY            
C&    REAL*4        rfgvll(20)                              ! LOADER VEL IN LINEAR UNITS        
C&    REAL*4        rfgvlr(20)                              ! LOADER VELOCITY IN RADS/S         
C&    REAL*4        rfgvrad(20)                             ! CONT VELOCITY IN RADS             
C&    INTEGER*4     rfgxai(20)                              ! POSITION XDUCER AI                
C&    REAL*4        rfgxbl(20)                              ! POS BIAS FROM TDCTONEU            
C&    REAL*4        rfgxbr(20)                              ! POS BIAS TDC TO NEU IN RADS       
C&    REAL*4        rfgxcl(20)                              ! COCKPIT CONTROL POS               
C&    REAL*4        rfgxcos(20)                             ! COS OF ANGLE OF CONT FROM NEU RADS
C&    REAL*4        rfgxcr(20)                              ! CONT FROM TDC ANGLE IN RADS       
C&    REAL*4        rfgxdeg(20)                             ! ANGLE OF CONT FROM NEU DEGS       
C&    REAL*4        rfgxlin(20)                             ! CONT POS FROM NEUT LIN            
C&    REAL*4        rfgxll(20)                              ! LINKAGE LOADER POS                
C&    REAL*4        rfgxlr(20)                              ! LOADER POS FROM 0.0 IN DEGS       
C&    REAL*4        rfgxpd(20)                              ! CONT POS AT PILOT'S HAND DEGS     
C&    REAL*4        rfgxpl(20)                              ! CONT POS AT PILOT'S HAND LIN      
C&    REAL*4        rfgxpr(20)                              ! CONT POS AT PILOT'S HAND RADS     
C&    REAL*4        rfgxrad(20)                             ! CONT FROM TDC POS IN RADS         
C&    REAL*4        rfgxsd(20)                              ! LINKAGE STRETCH (DEGS)            
C&    REAL*4        rfgxsin(20)                             ! SIN OF ANG OF CONTANDTDC          
C&    REAL*4        rfgxsl(20)                              ! LINKAGE STRECH (LIN)              
C&    REAL*4        rfgxsr(20)                              ! LINKAGE STRETCH (RAD)             
C&    REAL*4        rfgxtdc(20)                             ! LOADER OFFSET TO TDC              
C&    REAL*4        rfmacmd(20)                             ! Mass commanded acceleration       
C&    REAL*4        rfmacmn1(20)                            ! Mass commanded acceleration N-1   
C&    LOGICAL*1     rfmactiv(20)                            ! fwd mass channel active flag      
C&    REAL*4        rfmafk(20)                              ! acceleration due to kinetic fricti
C&    REAL*4        rfmafkn1(20)                            ! N-1 kinetic friction acceleration 
C&    REAL*4        rfmafr(20)                              ! Acceleration due to Ref Friction F
C&    REAL*4        rfmafrn1(20)                            ! Acc due to Reference Friction For 
C&    REAL*4        rfmafs(20)                              ! acceleration due to static frictio
C&    REAL*4        rfmafsn1(20)                            ! N-1 acceleration due to static fri
C&    REAL*4        rfmam(20)                               ! fwd mass accel                    
C&    REAL*4        rfmamn1(20)                             ! Emulated fwd mass acceleration (n-
C&    INTEGER*4     rfmbbd1i(20)                            ! backdrive active #1 input         
C&    INTEGER*4     rfmbbd2i(20)                            ! backdrive active #2 input         
C&    LOGICAL*1     rfmbcmd(20)                             ! COMMAND BOOLEAN                   
C&    LOGICAL*1     rfmbdact(20)                            ! backdrive active                  
C&    INTEGER*4     rfmbmji(20)                             ! pointer mass jamb boolian         
C&    INTEGER*4     rfmboni(20)                             ! Input flag, loader ON             
C&    INTEGER*4     rfmbxln(20)                             ! Mass at negative hard limit flag  
C&    INTEGER*4     rfmbxlp(20)                             ! Mass at positive hard limit flag  
C&    LOGICAL*1     rfmbxstr(20)                            ! Model Stretch ON when true        
C&    REAL*4        rfmcd(20)                               ! DAMPING COEFFICIENT               
C&    REAL*4        rfmcdf(20)                              ! damping coef. in free/spring mode 
C&    REAL*4        rfmcdln(20)                             ! FWD MASS NEG LIMIT                
C&    REAL*4        rfmcdlp(20)                             ! FWD MASS POS LIMIT                
C&    REAL*4        rfmcdn(20)                              ! FWD MASS NEG LIMIT                
C&    REAL*4        rfmcdp(20)                              ! FWD MASS POS LIMIT                
C&    REAL*4        rfmcdsn(20)                             ! Mass damping in spring/stop neg. l
C&    REAL*4        rfmcdsp(20)                             ! mass damping in spring/stop pos. l
C&    REAL*4        rfmcdt(20)                              ! Total fwd mass damping coefficient
C&    REAL*4        rfmcdv(20)                              ! Fwd mass variable damping coeffici
C&    REAL*4        rfmcfkf(20)                             ! kinetic friction in free/spring mo
C&    REAL*4        rfmcfkn(20)                             ! kinetic friction constant negative
C&    REAL*4        rfmcfkp(20)                             ! kinetic friction constant positive
C&    REAL*4        rfmcfsf(20)                             ! static friction in free/spring mod
C&    REAL*4        rfmcfsn(20)                             ! static friction constant negative 
C&    REAL*4        rfmcfsp(20)                             ! static friction constant positive 
C&    INTEGER*4     rfmcnt(20)                              ! fwd mass count                    
C&    REAL*4        rfmdelt(20)                             ! fwd delta time                    
C&    REAL*4        rfmeasy(20)                             ! fwd mass channel easyon           
C&    INTEGER*4     rfmf1ai(20)                             ! AFT FORCE NO. 1                   
C&    INTEGER*4     rfmf1bdi(20)                            ! BACK DRIVE FORCE No. 1            
C&    INTEGER*4     rfmf1fi(20)                             ! FWD FORCE NO. 1                   
C&    INTEGER*4     rfmf2ai(20)                             ! AFT FORCE NO. 2                   
C&    INTEGER*4     rfmf2fi(20)                             ! FWD FORCE NO. 2                   
C&    INTEGER*4     rfmfbdi(20)                             ! BACK DRIVE FORCE                  
C&    INTEGER*4     rfmfbwi(20)                             ! BOB WEIGHT FORCE                  
C&    REAL*4        rfmfcmd(20)                             ! FORCE COMMAND TO H/W              
C&    REAL*4        rfmfd(20)                               ! DAMPING FORCE                     
C&    REAL*4        rfmff(20)                               ! FRICITON FORCE                    
C&    REAL*4        rfmffk(20)                              ! kinetic friction force.           
C&    REAL*4        rfmfflk(20)                             ! Simulator linkage friction        
C&    REAL*4        rfmffn(20)                              ! NEG FRICTION FORCE                
C&    REAL*4        rfmffp(20)                              ! POS FRICTION FORCE                
C&    REAL*4        rfmffs(20)                              ! static friction force.            
C&    REAL*4        rfmffst(20)                             ! Total static friction force       
C&    REAL*4        rfmfl(20)                               ! LIMIT FORCE                       
C&    REAL*4        rfmfp(20)                               ! Fwd mass pilot's applied force equ
C&    INTEGER*4     rfmfpafi(20)                            ! Pointer to pilot's applied force  
C&    REAL*4        rfmfpc(20)                              ! Pilot force @@ the control         
C&    INTEGER*4     rfmfrefi(20)                            ! fwd mass reference force          
C&    REAL*4        rfmfs(20)                               ! Spring/stop force.                
C&    REAL*4        rfmfsc(20)                              ! Centering spring/stop force.      
C&    REAL*4        rfmft(20)                               ! Fwd mass total force              
C&    REAL*4        rfmgf(20)                               ! FRICTION GAIN FORCE               
C&    REAL*4        rfmgfade(20)                            ! fade gain                         
C&    REAL*4        rfmgfk(20)                              ! kinetic friction gain             
C&    REAL*4        rfmgfkf(20)                             ! kinetic friction gain free/spring 
C&    REAL*4        rfmgfkn(20)                             ! Gain on kinetic friction neg.     
C&    REAL*4        rfmgfkp(20)                             ! Gain on kinetic friction pos.     
C&    REAL*4        rfmgfkr(20)                             ! kinetic friction gain, ref frictio
C&    REAL*4        rfmgfs(20)                              ! static friction gain              
C&    REAL*4        rfmgm(20)                               ! fwd mass gain                     
C&    REAL*4        rfmgmeasy(20)                           ! fwd mass gain easyon              
C&    REAL*4        rfmgsc(20)                              ! Centering spring K                
C&    REAL*4        rfmgsn(20)                              ! spring/stop force negative gain   
C&    REAL*4        rfmgsp(20)                              ! spring/stop force positive gain   
C&    REAL*4        rfmgvdrive(20)                          ! Fwd Mass AP Drive Vel filter gain 
C&    REAL*4        rfmgxc(20)                              ! Unit conversion gain              
C&    REAL*4        rfmiff(20)                              ! FORWARD MASS KINETIC FF           
C&    REAL*4        rfmigm(20)                              ! intermediate fwd mass gain        
C&    REAL*4        rfmivc(20)                              ! Intermediate Control velocity @@ pi
C&    REAL*4        rfmivm(20)                              ! intermediate mass velocity.       
C&    REAL*4        rfmixc(20)                              ! Intermediate control position     
C&    REAL*4        rfmixcn1(20)                            ! Past value, intermediate control p
C&    REAL*4        rfmixm(20)                              ! intermediate mass position.       
C&    REAL*4        rfmkdt(20)                              ! Delta t  gain                     
C&    REAL*4        rfmkln(20)                              ! NEG LIMIT FORCE RATE              
C&    REAL*4        rfmklp(20)                              ! POS LIMIT FORCE RATE              
C&    INTEGER*4     rfmmode(20)                             ! fwd mass mode                     
C&    INTEGER*4     rfmmodep(20)                            ! fwd mass mode previous            
C&    REAL*4        rfmtamp(20)                             ! test amplitude                    
C&    REAL*4        rfmtampc(20)                            ! test amplitude command            
C&    REAL*4        rfmtampfade                             ! test amplitude fade out dt/tau    
C&    REAL*4        rfmtamplagk                             ! test amplitude fade in dt/tau     
C&    REAL*4        rfmtcps(20)                             ! test cycles/sec                   
C&    REAL*4        rfmtcyc(20)                             ! test cycle pos (rad)              
C&    LOGICAL*1     rfmtinit(20)                            ! test init complete                
C&    REAL*4        rfmtinitlag                             ! test pos init lag dt/tau          
C&    INTEGER*4     rfmtno(20)                              ! test number                       
C&    REAL*4        rfmtsin(20)                             ! test sin wave amp                 
C&    REAL*4        rfmtv(20)                               ! test commanded velocity           
C&    REAL*4        rfmtxe(20)                              ! test pos init error               
C&    REAL*4        rfmtxelim(20)                           ! test pos init error limit         
C&    REAL*4        rfmtxm(20)                              ! test pos init command             
C&    REAL*4        rfmvc(20)                               ! Control velocity @@ pilot's hand   
C&    REAL*4        rfmvcmd(20)                             ! Commanded mass velocity.          
C&    REAL*4        rfmvcr(20)                              ! Reduced Mass Commanded Velocity   
C&    REAL*4        rfmvdrive(20)                           ! Fwd Mass AP Drive Velocity        
C&    REAL*4        rfmvf(20)                               ! Velocity due to friction          
C&    REAL*4        rfmvfk(20)                              ! kinetic friction velocity equiv.  
C&    REAL*4        rfmvfr(20)                              ! Velocity due to reference friction
C&    REAL*4        rfmvfs(20)                              ! static  friction velocity equiv.  
C&    REAL*4        rfmvfsr(20)                             ! Velocity due to static Ref Frictio
C&    REAL*4        rfmvm(20)                               ! fwd velocity                      
C&    REAL*4        rfmvmax(20)                             ! Mass velocity limit.              
C&    INTEGER*4     rfmvmi(20)                              ! FWD MASS VELOCITY                 
C&    REAL*4        rfmvmn1(20)                             ! Computed fwd mass velocity (n-1)  
C&    REAL*4        rfmvr(20)                               ! Velocity Reduction Due to Friction
C&    INTEGER*4     rfmvrefi(20)                            ! fwd mass reference velocity       
C&    REAL*4        rfmvslp(20)                             ! Clutch slip velocity              
C&    REAL*4        rfmxc(20)                               ! Control position @@ pilot's hand   
C&    REAL*4        rfmxcn1(20)                             ! Past value of Ctl pos @@ pilot's ha
C&    INTEGER*4     rfmxi(20)                               ! Input position                    
C&    REAL*4        rfmxln(20)                              ! FWD MASS POS LIMIT NEG            
C&    REAL*4        rfmxlp(20)                              ! FWD MASS POS LIMIT POS            
C&    REAL*4        rfmxm(20)                               ! fwd position                      
C&    INTEGER*4     rfmxmi(20)                              ! FWD MASS POSITION                 
C&    REAL*4        rfmxmln(20)                             ! negative position limit hard.     
C&    REAL*4        rfmxmlp(20)                             ! positive position limit hard.     
C&    REAL*4        rfmxmsn(20)                             ! spring/stop pickup pos. negative. 
C&    REAL*4        rfmxmsp(20)                             ! spring/stop pickup pos. positive. 
C&    REAL*4        rfmxstr(20)                             ! Simulator linkage stetch          
C&    REAL*4        rfmxstrtau(20)                          ! lag time constant for mass stretch
C&    REAL*4        rfscd(20)                               ! feel spring damping coefficient   
C&    INTEGER*4     rfscdi(20)                              ! input damping coeff., aero        
C&    REAL*4        rfsfaero(20)                            ! total aero force                  
C&    REAL*4        rfsfd(20)                               ! DAMPING FORCE                     
C&    REAL*4        rfsfda(20)                              ! aero damping force                
C&    INTEGER*4     rfsfgradi(20)                           ! input froce gradient, aero        
C&    REAL*4        rfsfl(20)                               ! FORCE LIMIT                       
C&    REAL*4        rfsflmax(20)                            ! MAX FORCE LIMIT                   
C&    REAL*4        rfsfln(20)                              ! feel spring neg limit             
C&    REAL*4        rfsflp(20)                              ! feel spring pos limit             
C&    INTEGER*4     rfsfoffi(20)                            ! input froce offset, aero          
C&    REAL*4        rfsfs(20)                               ! SPRING FORCE                      
C&    REAL*4        rfsfsa(20)                              ! aero spring force                 
C&    REAL*4        rfsft(20)                               ! TOTAL FORCE                       
C&    REAL*4        rfsgfa(20)                              ! gain                              
C&    REAL*4        rfsgfdamp(20)                           ! aero force damping gain           
C&    REAL*4        rfsgfgrad(20)                           ! aero force gradient gain          
C&    REAL*4        rfsgfoff(20)                            ! aero force offset gain            
C&    REAL*4        rfsifd(20)                              ! INTERMEDIATE DAMPING FORCE        
C&    REAL*4        rfsifs(20)                              ! INTERMEDIATE SPRING FORCE         
C&    REAL*4        rfsift(20)                              ! INTERMEDIATE FORCE                
C&    REAL*4        rfsk(20)                                ! SEGMENT GAIN                      
C&    INTEGER*4     rfsqi(20)                               ! Q INPUT                           
C&    INTEGER*4     rfsv1i(20)                              ! VELOCITY INPUT 1                  
C&    INTEGER*4     rfsv2i(20)                              ! VELOCITY INPUT 2                  
C&    REAL*4        rfsve(20)                               ! VELOCITY ERROR                    
C&    INTEGER*4     rfsvsi(20)                              ! Surface Position Input Vel        
C&    INTEGER*4     rfsx1i(20)                              ! POSITION INPUT 1                  
C&    INTEGER*4     rfsx2i(20)                              ! POSITION INPUT 2                  
C&    REAL*4        rfsxe(20)                               ! POSITION ERROR                    
C&    INTEGER*4     rfsxsi(20)                              ! Surface Position Input Cmd        
C&    REAL*4        rgrgr(20)                               ! GEAR RATIO                        
C&    REAL*4        rgrk(20)                                ! GEAR RATIO SLOPE                  
C&    INTEGER*4     rgrxi(20)                               ! GEAR RATIO POSITION INPUT         
C&    INTEGER*4     rgrzi(20)                               ! GEAR RATIO INPUT                  
C&    REAL*4        rgrzo(20)                               ! GEAR RATIO OUTPUT                 
C&    REAL*4        rgscd(20)                               ! general spring damping coefficient
C&    REAL*4        rgsfd(40)                               ! DAMPING FORCE                     
C&    REAL*4        rgsfs(40)                               ! SPRING FORCE                      
C&    REAL*4        rgsft(40)                               ! DAMPING COEFFICIENT               
C&    REAL*4        rgsifd(40)                              ! spring damping                    
C&    INTEGER*4     rgsv1i(40)                              ! VELOCITY INPUT 1                  
C&    INTEGER*4     rgsv2i(40)                              ! VELOCITY INPUT 2                  
C&    REAL*4        rgsve(40)                               ! VELOCITY ERROR                    
C&    INTEGER*4     rgsx1i(40)                              ! POSITION INPUT 1                  
C&    INTEGER*4     rgsx2i(40)                              ! POSITION INPUT 2                  
C&    REAL*4        rgsxe(40)                               ! POSITION ERROR                    
C&    INTEGER*4     rhmaoai(40)                             ! local angle of attack input pointe
C&    REAL*4        rhmc(40)                                ! mean cord                         
C&    REAL*4        rhmcd(40)                               ! DAMPING COEFFICIENT               
C&    REAL*4        rhmcha(40)                              ! hinge moment coef due to adj surf 
C&    REAL*4        rhmchmadd(40)                           ! additional hinge moments          
C&    REAL*4        rhmchs(40)                              ! hinge moment coef due to surf pos 
C&    REAL*4        rhmchsa(40)                             ! surf Chm vs aoa total             
C&    REAL*4        rhmchsa1(40)                            ! surf Chm vs aoa                   
C&    REAL*4        rhmchsa2(40)                            ! surf Chm vs aoa                   
C&    REAL*4        rhmchscd(40)                            ! surf hm aero damping coeff        
C&    REAL*4        rhmchsfd(40)                            ! surf hm aero damping force        
C&    REAL*4        rhmchso(40)                             ! surf additional effects total     
C&    REAL*4        rhmchso1(40)                            ! additional hm effect #1           
C&    REAL*4        rhmchso2(40)                            ! additional hm effect #2           
C&    REAL*4        rhmchso3(40)                            ! additional hm effect #3           
C&    REAL*4        rhmchsxa(40)                            ! surf Chm vs surf aux pos          
C&    REAL*4        rhmchsxs(40)                            ! surf Chm vs surf pos              
C&    REAL*4        rhmchtotal(40)                          ! total hinge moment coef           
C&    REAL*4        rhmdelt(40)                             ! Delta time                        
C&    REAL*4        rhmfhm(40)                              ! control frc due to hinge moment   
C&    INTEGER*4     rhmfi(40)                               ! FORCE INPUT                       
C&    REAL*4        rhmfmh(40)                              ! CONTROL FORCE D.T. H/M            
C&    REAL*4        rhmfo(40)                               ! FORCE OUTPUT                      
C&    REAL*4        rhmgac(40)                              ! CONST MECH ADVANTAGE              
C&    REAL*4        rhmgav(40)                              ! GAIN FOR THE SEGMENT              
C&    REAL*4        rhmgcf(40)                              ! CONTROL FORCE GAIN                
C&    REAL*4        rhmgh1(40)                              ! HINGE MOMENT 1 GAIN               
C&    REAL*4        rhmgh2(40)                              ! HINGE MOMENT 2 GAIN               
C&    REAL*4        rhmghd(40)                              ! HINGE MOMENT GAIN                 
C&    REAL*4        rhmghms(40)                             ! int var surf hm to pilot frc      
C&    REAL*4        rhmgho(40)                              ! HINGE MOMENT OFFSET GA            
C&    REAL*4        rhmglink(40)                            ! link term for adjoining surfaces  
C&    INTEGER*4     rhmgm2i(40)                             ! AERO FORCE ON SURF 2              
C&    REAL*4        rhmgma(40)                              ! TOT MECH ADVANTAGE                
C&    REAL*4        rhmgps(40)                              ! control gearing pilot units/surf u
C&    REAL*4        rhmgsp(40)                              ! control gearing surf units/pilot u
C&    REAL*4        rhmgva(40)                              ! aux surf vel gearing              
C&    REAL*4        rhmgxa(40)                              ! aux surf pos gearing gain         
C&    REAL*4        rhmh1(40)                               ! HINGE MOMENT ON SURF 1            
C&    REAL*4        rhmh2(40)                               ! HINGE MOMENT ON SURF 2            
C&    REAL*4        rhmhd(40)                               ! HINGE MOMENT                      
C&    REAL*4        rhmhms(40)                              ! surf hinge moment                 
C&    REAL*4        rhmho(40)                               ! INTERM H/M OFFSET INPU            
C&    REAL*4        rhmhos(40)                              ! HINGE MOMENT OFFSET               
C&    REAL*4        rhmht(40)                               ! TOTAL HINGE MOMENT                
C&    REAL*4        rhmichsa1(40)                           ! intermediate surf Chm vs aoa      
C&    REAL*4        rhmichsa2(40)                           ! intermediate surf Chm vs aoa      
C&    REAL*4        rhmichso1(40)                           ! additional hm effect int  #1      
C&    REAL*4        rhmichso2(40)                           ! additional hm effect int  #2      
C&    REAL*4        rhmichso3(40)                           ! additional hm effect int  #3      
C&    REAL*4        rhmichsxa(40)                           ! intermediate surf Chm vs surf aux 
C&    REAL*4        rhmichsxs(40)                           ! intermediate Chm vs surf pos      
C&    REAL*4        rhmifhm(40)                             ! hm gearing surf to pilot force    
C&    REAL*4        rhmivs(40)                              ! surf velocity                     
C&    REAL*4        rhmixo(40)                              ! INTERM OUTPUT POSITION            
C&    REAL*4        rhmixs(40)                              ! surf position                     
C&    REAL*4        rhmk(40)                                ! surf effectiveness factor         
C&    INTEGER*4     rhmk1i(40)                              ! AERO FORCE GRAD ON S-1            
C&    INTEGER*4     rhmk2i(40)                              ! AERO FORCE GRAD ON S-2            
C&    REAL*4        rhmkchsa1(40)                           ! surf Chm vs aoa Factor            
C&    REAL*4        rhmkchsa2(40)                           ! surf Chm vs aoa Factor            
C&    REAL*4        rhmkchso1(40)                           ! additional hm effect gain #1      
C&    REAL*4        rhmkchso2(40)                           ! additional hm effect gain #2      
C&    REAL*4        rhmkchso3(40)                           ! additional hm effect gain #3      
C&    REAL*4        rhmkchsxa(40)                           ! surf Chm vs surf aux pos Factor   
C&    REAL*4        rhmkchsxs(40)                           ! surf Chm vs surf pos Factor       
C&    REAL*4        rhmkhms(40)                             ! hm gearing surf to pilot force fac
C&    INTEGER*4     rhmqi(40)                               ! dynamic press input pointer       
C&    REAL*4        rhms(40)                                ! control surface area              
C&    REAL*4        rhmva(40)                               ! aux surf velocity                 
C&    INTEGER*4     rhmvai(40)                              ! aux surf velocity input pointer   
C&    REAL*4        rhmvc(40)                               ! control velocity                  
C&    INTEGER*4     rhmvci(40)                              ! control velocity input pointer    
C&    INTEGER*4     rhmvi(40)                               ! VELOCITY INPUT                    
C&    REAL*4        rhmvo(40)                               ! VELOCITY OUTPUT                   
C&    REAL*4        rhmvs(40)                               ! surf velocity                     
C&    REAL*4        rhmvtr(40)                              ! Reciprocal of true airspeed       
C&    INTEGER*4     rhmvtri(40)                             ! true airspeed reciprocal          
C&    REAL*4        rhmvtrlim(40)                           ! Reciprocal of true airspeed limit 
C&    REAL*4        rhmx1(40)                               ! variable mech advantage           
C&    INTEGER*4     rhmx1i(40)                              ! SURFACE POSITION                  
C&    INTEGER*4     rhmx2i(40)                              ! SURF 2 POSITION                   
C&    REAL*4        rhmxa(40)                               ! aux surf position                 
C&    INTEGER*4     rhmxai(40)                              ! aux surf position input pointer   
C&    REAL*4        rhmxc(40)                               ! control position                  
C&    INTEGER*4     rhmxci(40)                              ! control position input pointer    
C&    REAL*4        rhmxo(40)                               ! POSITION OUTPUT                   
C&    REAL*4        rhmxs(40)                               ! surf position                     
C&    REAL*4        rhmxsd(40)                              ! Delta surface position            
C&    REAL*4        rhmxsn1(40)                             ! Last pass surface position n1     
C&    REAL*4        rmrdelt(20)                             ! rmr delta time                    
C&    REAL*4        rmrf1(20)                               ! mixer force 1                     
C&    INTEGER*4     rmrf1i(20)                              ! mixer force input 1               
C&    REAL*4        rmrf2(20)                               ! mixer force 2                     
C&    INTEGER*4     rmrf2i(20)                              ! mixer force input 2               
C&    REAL*4        rmrf3(20)                               ! mixer force 3                     
C&    INTEGER*4     rmrf3i(20)                              ! mixer force input 3               
C&    REAL*4        rmrf4(20)                               ! mixer force 4                     
C&    INTEGER*4     rmrf4i(20)                              ! mixer force input 4               
C&    REAL*4        rmrfo(20)                               ! mixer force output                
C&    REAL*4        rmrgf1(20)                              ! Force 1 input gain                
C&    REAL*4        rmrgf2(20)                              ! Force 2 input gain                
C&    REAL*4        rmrgf3(20)                              ! Force 3 input gain                
C&    REAL*4        rmrgf4(20)                              ! Force 4 input gain                
C&    REAL*4        rmrv1(20)                               ! mixer velocity 1                  
C&    REAL*4        rmrv2(20)                               ! mixer velocity 2                  
C&    REAL*4        rmrv3(20)                               ! mixer velocity 3                  
C&    REAL*4        rmrv4(20)                               ! mixer velocity 4                  
C&    REAL*4        rmrvo(20)                               ! mixer velocity output             
C&    REAL*4        rmrx1(20)                               ! mixer position 1                  
C&    INTEGER*4     rmrx1i(20)                              ! mixer position input 1            
C&    REAL*4        rmrx2(20)                               ! mixer position 2                  
C&    INTEGER*4     rmrx2i(20)                              ! mixer position input 2            
C&    REAL*4        rmrx3(20)                               ! mixer position 3                  
C&    INTEGER*4     rmrx3i(20)                              ! mixer position input 3            
C&    REAL*4        rmrx4(20)                               ! mixer position 4                  
C&    INTEGER*4     rmrx4i(20)                              ! mixer position input 4            
C&    REAL*4        rmrxo(20)                               ! mixer position output             
C&    REAL*4        rmrxon1(20)                             ! mixer position output (n-1)       
C&    LOGICAL*1     rover_switches(12)                      ! rover switches                    
C&    REAL*4        rpbrkcdn(2)                             ! Parking Brake Additional Cd  neg q
C&    REAL*4        rpbrkcdp(2)                             ! Parking Brake Additional Cd  pos q
C&    REAL*4        rpbrktaui                               ! Parking Brake Cd transition Time C
C&    REAL*4        rpsVc_f(12)                             ! Velocity filter array             
C&    INTEGER*4     rpsVc_ft(2)                             ! rps velocity filter index 0-7     
C&    REAL*4        rpsXc_f(12)                             ! Position filter array             
C&    INTEGER*4     rpsXc_ft(2)                             ! rps position filter index 0-7     
C&    INTEGER*4     rpsabort(2)                             ! servo drive abort flags.          
C&    REAL*4        rpsao(2)                                ! servo drive output i/o.           
C&    REAL*4        rpsaolim(2)                             ! servo drive output limit.         
C&    INTEGER*4     rpsaovrd(2)                             ! servo drive abort overide flag.   
C&    REAL*4        rpsgxl(2)                               ! Position transducer input gain    
C&    REAL*4        rpsgxlgn(2)                             ! Geared loader position gain negati
C&    REAL*4        rpsgxlgp(2)                             ! Geared loader position gain positi
C&    REAL*4        rpsirate(2)                             ! Module itteration rate            
C&    REAL*4        rpsitime(2)                             ! Module itteration time interval.  
C&    LOGICAL*1     rpsrun(2)                               ! servo drive run flag.             
C&    REAL*4        rpsscmd(2)                              ! Servo drive commanded.            
C&    REAL*4        rpsski(2)                               ! Servo integral gain (position erro
C&    REAL*4        rpsskpn(2)                              ! Servo proportional gain negative  
C&    REAL*4        rpsskpp(2)                              ! Servo proportional gain positive  
C&    REAL*4        rpssnull(2)                             ! servo drive output offset null.   
C&    LOGICAL*1     rpsstart(2)                             ! servo drive startup flag.         
C&    REAL*4        rpsvc(2)                                ! Velocity in cockpit units         
C&    REAL*4        rpsvclim(2)                             ! servo drive velocity limit for abo
C&    REAL*4        rpsve(2)                                ! Servo Velocity error              
C&    REAL*4        rpsvlg(2)                               ! Geared loader velocity            
C&    INTEGER*4     rpsxai(2)                               ! Pointer Position transducer input 
C&    REAL*4        rpsxaio(2)                              ! Position transducer input i/o offs
C&    REAL*4        rpsxc(2)                                ! Position in cockpit units         
C&    REAL*4        rpsxce(2)                               ! Servo Position command error.     
C&    REAL*4        rpsxcmd(2)                              ! commanded servo position          
C&    REAL*4        rpsxcn1(2)                              ! Position in cockpit units filtered
C&    REAL*4        rpsxe(2)                                ! Servo Position error              
C&    REAL*4        rpsxelim(2)                             ! servo drive position error limit f
C&    REAL*4        rpsxl(2)                                ! Position transducer in loader unit
C&    REAL*4        rpsxlg(2)                               ! Geared loader position            
C&    REAL*4        rpsxlgn1(2)                             ! Geared loader position N-1        
C&    REAL*4        rpsxlgo(2)                              ! Geared loader position offset     
C&    INTEGER*4     rpsxmi(2)                               ! Pointer mass position input       
C&    REAL*4        rpsxslew(2)                             ! Servo Position error slew rate lim
C&    REAL*4        rsaaf(40)                               ! Acceleration due to Friction Force
C&    REAL*4        rsaafn1(40)                             ! Acceleration due to Fric Force (n-
C&    REAL*4        rsaam(40)                               ! Pistion Mass Acceleration         
C&    REAL*4        rsaamn1(40)                             ! Pistion Mass Accelertaion (n-1)   
C&    REAL*4        rsaan1(40)                              ! n-1 accel                         
C&    REAL*4        rsaaplk(40)                             ! Actuator linkage acceleratio      
C&    INTEGER*4     rsabckvi(40)                            ! CHECK VLV INSTALED FLG            
C&    LOGICAL*1     rsabcmd(40)                             ! Command Boolean                   
C&    INTEGER*4     rsabpfi(40)                             ! PISTON FLOAT FLAG                 
C&    INTEGER*4     rsabpji(40)                             ! PISTON JAM FLAG                   
C&    INTEGER*4     rsabvj1i(40)                            ! servo valve jam input 1           
C&    INTEGER*4     rsabvj2i(40)                            ! servo valve jam input 2           
C&    INTEGER*4     rsabvj3i(40)                            ! servo valve jam input 3           
C&    INTEGER*4     rsabvji(40)                             ! VALVE JAM BOOLEAN                 
C&    REAL*4        rsacd(40)                               ! Damping coefficient (linkage)     
C&    REAL*4        rsacdord(40)                            ! Damp. Coeff. table input based on 
C&    REAL*4        rsacdsa(40)                             ! DAMPING COEFFICIENT               
C&    REAL*4        rsacdv1(40)                             ! Servo Valve #1 Limit Damping Coeff
C&    REAL*4        rsacdv2(40)                             ! Servo Valve #2 Limit Damping Coeff
C&    REAL*4        rsacdv3(40)                             ! Servo Valve #3 Limit Damping Coeff
C&    REAL*4        rsacv(40)                               ! VALVE COEFFICIENT                 
C&    REAL*4        rsadelt(40)                             ! DELTA TIME                        
C&    REAL*4        rsafd(40)                               ! Damping force                     
C&    REAL*4        rsafdv1(40)                             ! Servo Valve #1 Limit Damping Force
C&    REAL*4        rsafdv2(40)                             ! Servo Valve #2 Limit Damping Force
C&    REAL*4        rsafdv3(40)                             ! Servo Valve #3 Limit Damping Force
C&    REAL*4        rsaffv(40)                              ! Pistion Variable Friction Force   
C&    REAL*4        rsafhm(40)                              ! Hinge moment force                
C&    INTEGER*4     rsafhmi(40)                             ! Hinge moment force input pointer  
C&    REAL*4        rsafhy(40)                              ! Hydraulic Force on Actuator Pistio
C&    REAL*4        rsaflk(40)                              ! Linkage force                     
C&    INTEGER*4     rsaflki(40)                             ! Linkage force input pointer       
C&    REAL*4        rsafsv(40)                              ! servo valve force output          
C&    REAL*4        rsafsv1(40)                             ! Servo Valve #1 Limit Spring Force 
C&    REAL*4        rsafsv2(40)                             ! Servo Valve #2 Limit Spring Force 
C&    REAL*4        rsafsv3(40)                             ! Servo Valve #3 Limit Spring Force 
C&    REAL*4        rsaft(40)                               ! Total actuator linkage force      
C&    REAL*4        rsafv(40)                               ! Act Servo Valve limit Force on Pis
C&    REAL*4        rsafv1(40)                              ! Servo Valve #1 Total limit Force  
C&    REAL*4        rsafv2(40)                              ! Servo Valve #2 Total limit Force  
C&    REAL*4        rsafv3(40)                              ! Servo Valve #3 Total limit Force  
C&    REAL*4        rsagap(40)                              ! 1/AREA PISTON                     
C&    REAL*4        rsagapn(40)                             ! Piston Area on negitive side of pi
C&    REAL*4        rsagapp(40)                             ! Piston Area on positive side of pi
C&    REAL*4        rsagapr(40)                             ! 1 / Actuator Piston Area          
C&    REAL*4        rsagfhm(40)                             ! Hinge moment force gain           
C&    REAL*4        rsagfsv(40)                             ! Total Act Servo Valve limit force 
C&    REAL*4        rsaghm(40)                              ! HINGE MOMENT GAIN                 
C&    REAL*4        rsaghy(40)                              ! Hydraulic pressure input gain     
C&    REAL*4        rsaghy1(40)                             ! Servo Valve #1 Hydraulic Gain     
C&    REAL*4        rsaghy2(40)                             ! Servo Valve #2 Hydraulic Gain     
C&    REAL*4        rsaghy3(40)                             ! Servo Valve #3 Hydraulic Gain     
C&    REAL*4        rsaghyd(40)                             ! HYD FLOW GAIN                     
C&    REAL*4        rsaghyv(40)                             ! Servo Valve Hydraulic Gain        
C&    REAL*4        rsaglk(40)                              ! Linkage force gain                
C&    REAL*4        rsagm(40)                               ! Gain, (1/linkage mass+surface mass
C&    REAL*4        rsagnor(40)                             ! normalizing gain                  
C&    REAL*4        rsagnum(40)                             ! 1 / total number of servo valves  
C&    REAL*4        rsagpd(40)                              ! DIFFERENTIAL PRES GAIN            
C&    REAL*4        rsagphy1(40)                            ! Act Hydraulic Pressure Input #1 Ga
C&    REAL*4        rsagphy2(40)                            ! Act Hydraulic Pressure Input #2 Ga
C&    REAL*4        rsagphy3(40)                            ! Act Hydraulic Pressure Input #3 Ga
C&    REAL*4        rsags(40)                               ! CURRENT SEGMENT GAIN              
C&    REAL*4        rsagstf(40)                             ! Servo Valve Stiffness             
C&    REAL*4        rsagv(40)                               ! VALVE GAIN FOR SEGMENT            
C&    REAL*4        rsagv1(40)                              ! Actuator Input Velocity Cmd #1 Gai
C&    REAL*4        rsagv2(40)                              ! Actuator Input Velocity Cmd #2 Gai
C&    REAL*4        rsagx1(40)                              ! Actuator Input Position Cmd #1 Gai
C&    REAL*4        rsagx2(40)                              ! Actuator Input Position Cmd #2 Gai
C&    INTEGER*4     rsaidxc(40)                             ! VALVE COEFF INDEX                 
C&    REAL*4        rsaiv2(40)                              ! intermediate V2 vel               
C&    REAL*4        rsaivp(40)                              ! Interimediate Piston Velocity     
C&    REAL*4        rsaivpn1(40)                            ! Interimediate Piston Velocity n1  
C&    REAL*4        rsaivpv(40)                             ! Piston Velocity due to Servo Valve
C&    REAL*4        rsaivpvl(40)                            ! Piston Vel due to Servo Valve limi
C&    REAL*4        rsaix2(40)                              ! intermediate X2 pos               
C&    REAL*4        rsaixp(40)                              ! Intermediate Piston Position      
C&    REAL*4        rsaixv(40)                              ! INTERMEDIATE VALVE POS            
C&    REAL*4        rsaorder(40)                            ! Order, 1.0 for 1st, 2.0 for 2nd   
C&    REAL*4        rsapd(40)                               ! DIFFERENTIAL PRESSURE             
C&    REAL*4        rsapdsq(40)                             ! Square root of Abs Orif. Diff. Pre
C&    REAL*4        rsaphm(40)                              ! HINGE MOMENT PRESSURE             
C&    INTEGER*4     rsaphmi(40)                             ! INTERM H/M PRESSURE               
C&    REAL*4        rsaphy(40)                              ! Actuator Hydraulic Pressure       
C&    REAL*4        rsaphy1(40)                             ! Servo Valve #1 Hydraulic Pressure 
C&    INTEGER*4     rsaphy1i(40)                            ! servo act hyd press input 1       
C&    REAL*4        rsaphy2(40)                             ! Servo Valve #2 Hydraulic Pressure 
C&    INTEGER*4     rsaphy2i(40)                            ! servo act hyd press input 2       
C&    REAL*4        rsaphy3(40)                             ! Servo Valve #3 Hydraulic Pressure 
C&    INTEGER*4     rsaphy3i(40)                            ! servo act hyd press input 3       
C&    INTEGER*4     rsaphyi(40)                             ! HYDRAULIC PRESSURE                
C&    REAL*4        rsaphyt(40)                             ! Total Actuator Hydraulic Pressure 
C&    REAL*4        rsapl(40)                               ! LOAD PRESSURE                     
C&    REAL*4        rsaplk(40)                              ! Linkage Pressure on Actuator Pisti
C&    REAL*4        rsapthld(40)                            ! Hydraulic Press Threshold         
C&    REAL*4        rsapv(40)                               ! ACTUATOR DAMPING                  
C&    REAL*4        rsaqpf(40)                              ! PISTON FLOAT FLOW                 
C&    REAL*4        rsaqv(40)                               ! PISTON FLOW                       
C&    REAL*4        rsasvj1(40)                             ! Servo Valve #1 Jam Flag           
C&    REAL*4        rsasvj2(40)                             ! Servo Valve #2 Jam Flag           
C&    REAL*4        rsasvj3(40)                             ! Servo Valve #3 Jam Flag           
C&    REAL*4        rsav1(40)                               ! Actuator Input Velocity #1        
C&    INTEGER*4     rsav1i(40)                              ! servo valve velocity cmd 1 input  
C&    REAL*4        rsav2(40)                               ! Actuator Input Velocity #2 (SAS)  
C&    INTEGER*4     rsav2i(40)                              ! servo valve velocity cmd 2 input  
C&    REAL*4        rsavcmd(40)                             ! Pistion Mass Commanded Velocity   
C&    REAL*4        rsavcr(40)                              ! Reduced Pistion Mass Commanded Vel
C&    REAL*4        rsavf(40)                               ! Veloicty due to Friction Force    
C&    REAL*4        rsavleak(40)                            ! Max. Pistion Leakage Velocity     
C&    REAL*4        rsavp(40)                               ! PISTON VELOCITY                   
C&    INTEGER*4     rsavpfi(40)                             ! Piston float velocity input pointe
C&    REAL*4        rsavpl(40)                              ! Actuator Pistion Velocity Limit   
C&    REAL*4        rsavplk(40)                             ! Vel due to linkage + hinge moment 
C&    REAL*4        rsavplkl(40)                            ! Limit, Velocity due to Linkage    
C&    REAL*4        rsavpn1(40)                             ! Piston Velocity (n-1)             
C&    REAL*4        rsavpv(40)                              ! velocity due to servo valve flow  
C&    REAL*4        rsavpvl(40)                             ! Limit, Pistion Velocity           
C&    REAL*4        rsavr(40)                               ! Velocity Reduction Due to Friction
C&    REAL*4        rsavs(40)                               ! SURFACE VELOCITY                  
C&    REAL*4        rsavv(40)                               ! Servo Valve Velocity              
C&    REAL*4        rsavv1(40)                              ! Servo Valve #1 Velocity           
C&    REAL*4        rsavv2(40)                              ! Servo Valve #2 Velocity           
C&    REAL*4        rsavv3(40)                              ! Servo Valve #3 Velocity           
C&    REAL*4        rsax1(40)                               ! Actuator Input Position #1        
C&    INTEGER*4     rsax1i(40)                              ! servo valve position cmd 1 input  
C&    REAL*4        rsax2(40)                               ! Actuator Input Position #2 (SAS)  
C&    INTEGER*4     rsax2i(40)                              ! servo valve position cmd 2 input  
C&    REAL*4        rsax2ln(40)                             ! X2 position limit, neg            
C&    REAL*4        rsax2lp(40)                             ! X2 position limit, pos            
C&    REAL*4        rsaxe(40)                               ! POSITION ERROR TERM               
C&    REAL*4        rsaxp(40)                               ! PISTON POSITION                   
C&    REAL*4        rsaxpln(40)                             ! PISTON POSI LIMIT NEG             
C&    REAL*4        rsaxplp(40)                             ! PISTON POSI LIMIT POS             
C&    REAL*4        rsaxs(40)                               ! SURFACE POSITION                  
C&    INTEGER*4     rsaxsci(40)                             ! SERVO COMMAND INPUT               
C&    INTEGER*4     rsaxscoi(40)                            ! SERVO FEEDBACK                    
C&    REAL*4        rsaxsn1(40)                             ! Surface position (n-1)            
C&    REAL*4        rsaxsp(40)                              ! previous surf position            
C&    REAL*4        rsaxv(40)                               ! VALVE POSITION                    
C&    REAL*4        rsaxv1(40)                              ! Servo Valve #1 Position           
C&    REAL*4        rsaxv1jam(40)                           ! Servo Valve #1 Jam Position       
C&    REAL*4        rsaxv2(40)                              ! Servo Valve #2 Position           
C&    REAL*4        rsaxv2jam(40)                           ! Servo Valve #2 Jam Position       
C&    REAL*4        rsaxv3(40)                              ! Servo Valve #3 Position           
C&    REAL*4        rsaxv3jam(40)                           ! Servo Valve #3 Jam Position       
C&    REAL*4        rsaxvln(40)                             ! VALVE POSITION LIMIT N            
C&    REAL*4        rsaxvlp(40)                             ! VALVE POSITION LIMIT P            
C&    REAL*4        rtaatm(20)                              ! Trim Motor Accelleration          
C&    REAL*4        rtabfor(20)                             ! Centering Spring Override Bo      
C&    INTEGER*4     rtabtri(20)                             ! trim release switch input         
C&    REAL*4        rtacd(20)                               ! coeff of damping                  
C&    REAL*4        rtadelt(20)                             ! DELTA TIME                        
C&    REAL*4        rtafd(20)                               ! trim actuator damping force       
C&    REAL*4        rtafor(20)                              ! Override force threshold          
C&    REAL*4        rtafs(20)                               ! trim actuator spring force        
C&    REAL*4        rtaft(20)                               ! trim force output                 
C&    REAL*4        rtagerr(20)                             ! Position error gain               
C&    REAL*4        rtagm(20)                               ! Trim Switch Input Gain            
C&    REAL*4        rtak(20)                                ! CURRENT SEGMENT GAIN              
C&    REAL*4        rtata(20)                               ! TIME CONSTANT                     
C&    INTEGER*4     rtatrli(20)                             ! TRIM ACTUATOR TRIM RELEASE        
C&    INTEGER*4     rtattmi(20)                             ! Trim Switch Input                 
C&    REAL*4        rtave(20)                               ! trim actuator velocity error      
C&    INTEGER*4     rtavi(20)                               ! trim velocity input               
C&    REAL*4        rtavt(20)                               ! trim velocity                     
C&    REAL*4        rtavtc(20)                              ! Trim Velocity Command             
C&    INTEGER*4     rtavti(20)                              ! trim velocity command input       
C&    REAL*4        rtavtln(20)                             ! Trim Motor Neg Limit              
C&    REAL*4        rtavtlp(20)                             ! Trim Motor Pos Limit              
C&    REAL*4        rtavtm(20)                              ! Trim Motor Velocity               
C&    REAL*4        rtavxin(20)                             ! Velocity from trim pos CMD In     
C&    REAL*4        rtaxc(20)                               ! SCALED TRIM COMMAND               
C&    INTEGER*4     rtaxci(20)                              ! INPUT TRIM COMMAND                
C&    REAL*4        rtaxe(20)                               ! trim actuator position error      
C&    INTEGER*4     rtaxfi(20)                              ! TRIM ACT FWD POS                  
C&    INTEGER*4     rtaxi(20)                               ! trim position input               
C&    REAL*4        rtaxt(20)                               ! TRIM POSITION                     
C&    REAL*4        rtaxtc(20)                              ! Trim Position Command             
C&    INTEGER*4     rtaxti(20)                              ! Trim Position CMD Input           
C&    REAL*4        rtaxtil(20)                             ! Limited Position Input CMD        
C&    REAL*4        rtaxtsn(20)                             ! trim actuator negative stop       
C&    REAL*4        rtaxtsp(20)                             ! trim actuator positive stop       
C&    REAL*8        rtexec_timing(61)                       ! exec timing base                  
C&    REAL*4        rwaf_rudboost_force_app                 ! Rudder boost force applied        
C&    REAL*4        start_delay                             ! CLS channel turn-on delay         
C&    REAL*4        start_delay_time                        ! CLS channel turn-on delay time (se
C&    REAL*4        tcoveride                               ! host override real                
*/ 
#define DPSTART 0x4000000                                                                       
#define DPSTART1 0x5000000                                                                      
static float       *APSTKCMD= (void *)(0x00001550 + DPSTART);                                   
static float       *APWHLCMD= (void *)(0x0000157c + DPSTART);                                   
static float       *rwaf_rudboost_force_app= (void *)(0x00001964 + DPSTART);                    
static char        *APPITENG= (void *)(0x00002099 + DPSTART);                                   
static char        *APROLENG= (void *)(0x0000209a + DPSTART);                                   
static char        *APYDE= (void *)(0x0000209b + DPSTART);                                      
static char        *RUDBST_ENG= (void *)(0x00002247 + DPSTART);                                 
static float       *FDEMSTK= (void *)(0x000027fc + DPSTART);                                    
static float       *FDEMWHL= (void *)(0x00002800 + DPSTART);                                    
static float       *FDEMPDL= (void *)(0x00002804 + DPSTART);                                    
static float       *FACTPPOS= (void *)(0x00002808 + DPSTART);                                   
static float       *FACTSPP= (void *)(0x0000280c + DPSTART);                                    
static float       *FACTSSP= (void *)(0x00002810 + DPSTART);                                    
static float       *FACTELEV= (void *)(0x00002814 + DPSTART);                                   
static float       *FACTSTAB= (void *)(0x00002818 + DPSTART);                                   
static float       *FACTFLAP= (void *)(0x0000281c + DPSTART);                                   
static float       *FACTSFOR= (void *)(0x00002828 + DPSTART);                                   
static float       *FACTWFOR= (void *)(0x0000282c + DPSTART);                                   
static float       *FACTPFOR= (void *)(0x00002830 + DPSTART);                                   
static float       *FACTSPOS= (void *)(0x00002834 + DPSTART);                                   
static float       *FACTWPOS= (void *)(0x00002838 + DPSTART);                                   
static float       *FACTAIL= (void *)(0x00002958 + DPSTART);                                    
static float       *FACTSWP= (void *)(0x00002dd0 + DPSTART);                                    
static float       *FACTTBFL= (void *)(0x00002e6c + DPSTART);                                   
static float       *FACTTBFR= (void *)(0x00002e70 + DPSTART);                                   
static float       *FACTRUDD= (void *)(0x00002e7c + DPSTART);                                   
static float       *FMAXWFOR= (void *)(0x00002e9c + DPSTART);                                   
static float       *FMAXSFOR= (void *)(0x00002ec0 + DPSTART);                                   
static float       *FMAXPFOR= (void *)(0x00002f10 + DPSTART);                                   
static float       *FDEMNW= (void *)(0x00003010 + DPSTART);                                     
static float       *FDEMTOEB= (void *)(0x00003250 + DPSTART);                                   
static float       *FDEMTOER= (void *)(0x00003254 + DPSTART);                                   
static float       *FACTTOEB= (void *)(0x00003258 + DPSTART);                                   
static float       *FACTTOER= (void *)(0x0000325c + DPSTART);                                   
static float       *FTGLATT= (void *)(0x00003648 + DPSTART);                                    
static char        *FTGACTIV= (void *)(0x000036b0 + DPSTART);                                   
static char        *FSETAIL= (void *)(0x000036c0 + DPSTART);                                    
static char        *FSETSTAB= (void *)(0x000036c1 + DPSTART);                                   
static char        *FSETNWT= (void *)(0x000036c9 + DPSTART);                                    
static char        *FSRESET= (void *)(0x000036ca + DPSTART);                                    
static char        *FWRESET= (void *)(0x000036cb + DPSTART);                                    
static char        *FPRESET= (void *)(0x000036cc + DPSTART);                                    
static char        *FSETSFOR= (void *)(0x000036cd + DPSTART);                                   
static char        *FSETWFOR= (void *)(0x000036ce + DPSTART);                                   
static char        *FSETPFOR= (void *)(0x000036cf + DPSTART);                                   
static char        *FSETSPOS= (void *)(0x000036d6 + DPSTART);                                   
static char        *FSETWPOS= (void *)(0x000036d7 + DPSTART);                                   
static char        *FSETPPOS= (void *)(0x000036d8 + DPSTART);                                   
static char        *FSETSWP= (void *)(0x000036ff + DPSTART);                                    
static char        *FSETSSP= (void *)(0x00003700 + DPSTART);                                    
static char        *FSETSPP= (void *)(0x00003701 + DPSTART);                                    
static char        *FSETELEV= (void *)(0x00003702 + DPSTART);                                   
static char        *FSETRUDD= (void *)(0x00003703 + DPSTART);                                   
static char        *FSETTOER= (void *)(0x0000370a + DPSTART);                                   
static char        *FSETTOEB= (void *)(0x00003740 + DPSTART);                                   
static char        *FSETNW= (void *)(0x00003741 + DPSTART);                                     
static char        *FSETATAB= (void *)(0x00003783 + DPSTART);                                   
static char        *FSETRTAB= (void *)(0x00003784 + DPSTART);                                   
static char        *FSETNFOR= (void *)(0x0000378d + DPSTART);                                   
static char        *FBLCONT= (void *)(0x00003791 + DPSTART);                                    
static char        *FSETTBFL= (void *)(0x00003796 + DPSTART);                                   
static char        *FSETTBFR= (void *)(0x00003797 + DPSTART);                                   
static float       *FNMUREF= (void *)(0x000044d8 + DPSTART);                                    
static float       *FNU= (void *)(0x00004834 + DPSTART);                                        
static float       *FNUT= (void *)(0x00004838 + DPSTART);                                       
static float       *FNV= (void *)(0x0000483c + DPSTART);                                        
static float       *FNPPB= (void *)(0x0000484c + DPSTART);                                      
static char        *FBPUSHBK= (void *)(0x00005987 + DPSTART);                                   
static char        *FLBWO= (void *)(0x00005993 + DPSTART);                                      
static float       *FALPD= (void *)(0x00005ad8 + DPSTART);                                      
static float       *FAXA= (void *)(0x00005ae0 + DPSTART);                                       
static float       *FAYA= (void *)(0x00005ae4 + DPSTART);                                       
static float       *FBETAD= (void *)(0x00005af8 + DPSTART);                                     
static float       *FDPADEG= (void *)(0x00005b30 + DPSTART);                                    
static float       *FDQADEG= (void *)(0x00005b40 + DPSTART);                                    
static float       *FDRADEG= (void *)(0x00005b48 + DPSTART);                                    
static float       *FNAZA= (void *)(0x00005bc8 + DPSTART);                                      
static float       *FQ= (void *)(0x00005be8 + DPSTART);                                         
static float       *FQADEG= (void *)(0x00005bf0 + DPSTART);                                     
static float       *FRADEG= (void *)(0x00005c00 + DPSTART);                                     
static float       *FRSVP= (void *)(0x00005c0c + DPSTART);                                      
static float       *FVCAL= (void *)(0x00005c4c + DPSTART);                                      
static float       *FVP= (void *)(0x00005c60 + DPSTART);                                        
static float       *FVPKN= (void *)(0x00005c68 + DPSTART);                                      
static float       *FQRL= (void *)(0x00005ff0 + DPSTART);                                       
static float       *FQLRL= (void *)(0x00005ff4 + DPSTART);                                      
static float       *FQHRL= (void *)(0x00005ff8 + DPSTART);                                      
static float       *FVPLRL= (void *)(0x00005ffc + DPSTART);                                     
static float       *FVPHRL= (void *)(0x00006000 + DPSTART);                                     
static float       *FVPKNLRL= (void *)(0x00006004 + DPSTART);                                   
static float       *FVPKNHRL= (void *)(0x00006008 + DPSTART);                                   
static float       *FVPRL= (void *)(0x0000600c + DPSTART);                                      
static float       *FVPKNRL= (void *)(0x00006010 + DPSTART);                                    
static char        *FTORST= (void *)(0x000060d4 + DPSTART);                                     
static char        *FIPACTIV= (void *)(0x00006110 + DPSTART);                                   
static char        *MBRSTJAM= (void *)(0x0000a8c3 + DPSTART);                                   
static float       *RLZEL= (void *)(0x0000f3b4 + DPSTART);                                      
static float       *RRZEL= (void *)(0x0000f3b8 + DPSTART);                                      
static float       *RZCF= (void *)(0x0000f3bc + DPSTART);                                       
static float       *RZCFA= (void *)(0x0000f3c0 + DPSTART);                                      
static float       *RZCPS= (void *)(0x0000f3c4 + DPSTART);                                      
static float       *RZCPU= (void *)(0x0000f3c8 + DPSTART);                                      
static float       *RZELA= (void *)(0x0000f3d0 + DPSTART);                                      
static float       *RPSTCOM= (void *)(0x0000f420 + DPSTART);                                    
static float       *RLZAIL= (void *)(0x0000f4c0 + DPSTART);                                     
static float       *RRZAIL= (void *)(0x0000f4cc + DPSTART);                                     
static float       *RZAICTH= (void *)(0x0000f4d8 + DPSTART);                                    
static float       *RZWF= (void *)(0x0000f4f0 + DPSTART);                                       
static float       *RZWFA= (void *)(0x0000f4f4 + DPSTART);                                      
static float       *RZWPS= (void *)(0x0000f4f8 + DPSTART);                                      
static float       *RZWPU= (void *)(0x0000f4fc + DPSTART);                                      
static float       *RAPRCMD= (void *)(0x0000f588 + DPSTART);                                    
static float       *RZPF= (void *)(0x0000f590 + DPSTART);                                       
static float       *RZPFA= (void *)(0x0000f594 + DPSTART);                                      
static float       *RZPPS= (void *)(0x0000f598 + DPSTART);                                      
static float       *RZPPU= (void *)(0x0000f59c + DPSTART);                                      
static float       *RZRUD= (void *)(0x0000f5a8 + DPSTART);                                      
static float       *RZRUT= (void *)(0x0000f5d0 + DPSTART);                                      
static float       *RZNWA= (void *)(0x0000f688 + DPSTART);                                      
static char        *UCLDON= (void *)(0x00010de3 + DPSTART);                                     
static char        *XHPKBRK= (void *)(0x0001c59f + DPSTART);                                    
static float       *TLNFCNRG= (void *)(0x0003d2f4 + DPSTART);                                   
static float       *TLNFYMX= (void *)(0x0003d324 + DPSTART);                                    
static float       *TLNFZPT= (void *)(0x0003d32c + DPSTART);                                    
static float       *Y= (void *)(0x0003d640 + DPSTART);                                          
static long        *LB= (void *)(0x00041a10 + DPSTART);                                         
static char        *LZONLINE= (void *)(0x0009888c + DPSTART);                                   
static char        *electrimdown= (void *)(0x0009e2c7 + DPSTART);                               
static char        *electrimup= (void *)(0x0009e2c8 + DPSTART);                                 
static long        *HCLS= (void *)(0x000a0b26 + DPSTART);                                       
static long        *HCLS_ON_OFF= (void *)(0x000a0b26 + DPSTART);                                
static long        *HCLS_MODE= (void *)(0x000a0b3e + DPSTART);                                  
static long        *HCLS_RFC_CT= (void *)(0x000a0b56 + DPSTART);                                
static long        *HCLS_FAULT= (void *)(0x000a0b5a + DPSTART);                                 
static long        *HCLS_MODE_CMD= (void *)(0x000a0b72 + DPSTART);                              
static long        *HCLS_HOST_CT= (void *)(0x000a0b8a + DPSTART);                               
static long        *HCLS_RESET_CMD= (void *)(0x000a0b8e + DPSTART);                             
static long        *HCLS_ON_OFF_CMD= (void *)(0x000a0ba6 + DPSTART);                            
static float       *RFCLGALT= (void *)(0x000a0e30 + DPSTART);                                   
static char        *LFXFASTM= (void *)(0x000a52a0 + DPSTART);                                   
static char        *LIXINDIS= (void *)(0x000a52a7 + DPSTART);                                   
static char        *LAXMAN= (void *)(0x000a52a8 + DPSTART);                                     
static float       *RLXATTAB= (void *)(0x000a5bf4 + DPSTART);                                   
static float       *RLXETTAB= (void *)(0x000a5c14 + DPSTART);                                   
static float       *RLXNWANG= (void *)(0x000a5c24 + DPSTART);                                   
static float       *RLXRTTAB= (void *)(0x000a5c28 + DPSTART);                                   
static float       *RCXLONSK_TRM= (void *)(0x000a62b8 + DPSTART);                               
static float       *RCXLATSK_TRM= (void *)(0x000a62bc + DPSTART);                               
static float       *RCXRUPED_TRM= (void *)(0x000a62c0 + DPSTART);                               
static float       *RC_TRM_ERR= (void *)(0x000a94a4 + DPSTART);                                 
static long        *TCDWASH= (void *)(0x000a9c20 + DPSTART);                                    
static long        *TCDWASHZ= (void *)(0x000a9c24 + DPSTART);                                   
static float       *RC_DWASH= (void *)(0x000a9c28 + DPSTART);                                   
static float       *RC_DWASH_ZERO_AOA= (void *)(0x000a9c2c + DPSTART);                          
static float       *RC_DELTA_AOA= (void *)(0x000a9c30 + DPSTART);                               
static float       *RC_HORZ_STAB_AOA= (void *)(0x000a9c34 + DPSTART);                           
static float       *RC_VERT_STAB_AOA= (void *)(0x000a9c38 + DPSTART);                           
static float       *RC_DELTA_CAL= (void *)(0x000a9c64 + DPSTART);                               
static float       *NW_CASTOR_ANG_N1= (void *)(0x000a9e38 + DPSTART);                           
static char        *FSETSKPL= (void *)(0x000a9e50 + DPSTART);                                   
static long        *REALFEEL_RESERVED= (void *)(0x01100000 + DPSTART);                          
static float       *phymem= (void *)(0x01100000 + DPSTART);                                     
static double      *phymemd= (void *)(0x01100000 + DPSTART);                                    
static float       *phymemf= (void *)(0x01100000 + DPSTART);                                    
static long        *phymemi= (void *)(0x01100000 + DPSTART);                                    
static float       *GCTLBUF= (void *)(0x01100008 + DPSTART);                                    
static float       *GCTLBUF_RAM= (void *)(0x01100008 + DPSTART);                                
static float       *ramafr= (void *)(0x01100008 + DPSTART);                                     
static float       *ramafrn1= (void *)(0x01100058 + DPSTART);                                   
static float       *ramafs= (void *)(0x011000a8 + DPSTART);                                     
static float       *ramafsn1= (void *)(0x011000f8 + DPSTART);                                   
static float       *ramam= (void *)(0x01100148 + DPSTART);                                      
static float       *ramamn1= (void *)(0x01100198 + DPSTART);                                    
static float       *ramcd= (void *)(0x011001e8 + DPSTART);                                      
static float       *ramcdt= (void *)(0x01100238 + DPSTART);                                     
static float       *ramcdv= (void *)(0x01100288 + DPSTART);                                     
static float       *ramdelt= (void *)(0x011002d8 + DPSTART);                                    
static float       *ramfd= (void *)(0x01100328 + DPSTART);                                      
static float       *ramff= (void *)(0x01100378 + DPSTART);                                      
static float       *ramffst= (void *)(0x011003c8 + DPSTART);                                    
static float       *ramfft= (void *)(0x01100418 + DPSTART);                                     
static float       *ramffv= (void *)(0x01100468 + DPSTART);                                     
static float       *ramfref= (void *)(0x011004b8 + DPSTART);                                    
static float       *ramft= (void *)(0x01100508 + DPSTART);                                      
static float       *ramgf= (void *)(0x01100558 + DPSTART);                                      
static float       *ramgfk= (void *)(0x011005a8 + DPSTART);                                     
static float       *ramgfkr= (void *)(0x011005f8 + DPSTART);                                    
static float       *ramgm= (void *)(0x01100648 + DPSTART);                                      
static float       *ramgvdrive= (void *)(0x01100698 + DPSTART);                                 
static float       *ramivm= (void *)(0x011006e8 + DPSTART);                                     
static float       *ramixm= (void *)(0x01100738 + DPSTART);                                     
static float       *ramvcmd= (void *)(0x01100788 + DPSTART);                                    
static float       *ramvcr= (void *)(0x011007d8 + DPSTART);                                     
static float       *ramvdrive= (void *)(0x01100828 + DPSTART);                                  
static float       *ramvf= (void *)(0x01100878 + DPSTART);                                      
static float       *ramvfk= (void *)(0x011008c8 + DPSTART);                                     
static float       *ramvfs= (void *)(0x01100918 + DPSTART);                                     
static float       *ramvfsr= (void *)(0x01100968 + DPSTART);                                    
static float       *ramvm= (void *)(0x011009b8 + DPSTART);                                      
static float       *ramvmn1= (void *)(0x01100a08 + DPSTART);                                    
static float       *ramvr= (void *)(0x01100a58 + DPSTART);                                      
static float       *ramvref= (void *)(0x01100aa8 + DPSTART);                                    
static float       *ramvrefe= (void *)(0x01100af8 + DPSTART);                                   
static float       *ramvslp= (void *)(0x01100b48 + DPSTART);                                    
static float       *ramxm= (void *)(0x01100b98 + DPSTART);                                      
static float       *ramxmln= (void *)(0x01100be8 + DPSTART);                                    
static float       *ramxmlp= (void *)(0x01100c38 + DPSTART);                                    
static float       **ramf1ai= (void *)(0x01100c88 + DPSTART);                                   
static float       **ramf1fi= (void *)(0x01100cd8 + DPSTART);                                   
static float       **ramf2ai= (void *)(0x01100d28 + DPSTART);                                   
static float       **ramf2fi= (void *)(0x01100d78 + DPSTART);                                   
static float       **ramf3ai= (void *)(0x01100dc8 + DPSTART);                                   
static float       **ramf4ai= (void *)(0x01100e18 + DPSTART);                                   
static float       **ramf5ai= (void *)(0x01100e68 + DPSTART);                                   
static float       **ramfrefi= (void *)(0x01100eb8 + DPSTART);                                  
static float       **ramvrefi= (void *)(0x01100f08 + DPSTART);                                  
static float       **ramxfi= (void *)(0x01100f58 + DPSTART);                                    
static char        *rambcmd= (void *)(0x01100fa8 + DPSTART);                                    
static long        *rambxln= (void *)(0x01100fbc + DPSTART);                                    
static long        *rambxlp= (void *)(0x0110100c + DPSTART);                                    
static char        **rambmji= (void *)(0x0110105c + DPSTART);                                   
static char        **ramtrli= (void *)(0x011010ac + DPSTART);                                   
static float       *ramigm= (void *)(0x011010c0 + DPSTART);                                     
static float       *ramvmax= (void *)(0x01101110 + DPSTART);                                    
static float       *GCTLBUF_RAP= (void *)(0x01101778 + DPSTART);                                
static float       *rapdelt= (void *)(0x01101778 + DPSTART);                                    
static float       *rapfcws= (void *)(0x011017c8 + DPSTART);                                    
static float       *rapfmax= (void *)(0x01101818 + DPSTART);                                    
static float       *rapfor= (void *)(0x01101868 + DPSTART);                                     
static float       *rapgf1= (void *)(0x011018b8 + DPSTART);                                     
static float       *rapgf2= (void *)(0x01101908 + DPSTART);                                     
static float       *rapgramp= (void *)(0x01101958 + DPSTART);                                   
static float       *rapgtau= (void *)(0x011019a8 + DPSTART);                                    
static float       *rapgv= (void *)(0x011019f8 + DPSTART);                                      
static float       *rapgx1= (void *)(0x01101a48 + DPSTART);                                     
static float       *rapgx2= (void *)(0x01101a98 + DPSTART);                                     
static float       *rapramp= (void *)(0x01101ae8 + DPSTART);                                    
static float       *rapvap= (void *)(0x01101b38 + DPSTART);                                     
static float       *rapvapln= (void *)(0x01101b88 + DPSTART);                                   
static float       *rapvaplp= (void *)(0x01101bd8 + DPSTART);                                   
static float       *rapvcmd= (void *)(0x01101c28 + DPSTART);                                    
static float       *rapvcws= (void *)(0x01101c78 + DPSTART);                                    
static float       *rapx1= (void *)(0x01101cc8 + DPSTART);                                      
static float       *rapxapln= (void *)(0x01101d18 + DPSTART);                                   
static float       *rapxaplp= (void *)(0x01101d68 + DPSTART);                                   
static float       *rapxcmd= (void *)(0x01101db8 + DPSTART);                                    
static float       *rapxe= (void *)(0x01101e08 + DPSTART);                                      
static float       **rapf1i= (void *)(0x01101e58 + DPSTART);                                    
static float       **rapf2i= (void *)(0x01101ea8 + DPSTART);                                    
static float       **rapvi= (void *)(0x01101ef8 + DPSTART);                                     
static float       **rapx1i= (void *)(0x01101f48 + DPSTART);                                    
static float       **rapx2i= (void *)(0x01101f98 + DPSTART);                                    
static char        *rapbcws= (void *)(0x01101fe8 + DPSTART);                                    
static char        **rapengi= (void *)(0x01101ffc + DPSTART);                                   
static float       *GCTLBUF_RBD= (void *)(0x01102718 + DPSTART);                                
static float       *rbdgv= (void *)(0x01102718 + DPSTART);                                      
static float       *rbdgx= (void *)(0x01102768 + DPSTART);                                      
static float       *rbddelt= (void *)(0x011027b8 + DPSTART);                                    
static float       *rbdfbd= (void *)(0x01102808 + DPSTART);                                     
static float       *rbdfder= (void *)(0x01102858 + DPSTART);                                    
static float       *rbdfint= (void *)(0x011028a8 + DPSTART);                                    
static float       *rbdflbd= (void *)(0x011028f8 + DPSTART);                                    
static float       *rbdflmax= (void *)(0x01102948 + DPSTART);                                   
static float       *rbdfor= (void *)(0x01102998 + DPSTART);                                     
static float       *rbdfpro= (void *)(0x011029e8 + DPSTART);                                    
static float       *rbdftgm= (void *)(0x01102a38 + DPSTART);                                    
static float       *rbdg2= (void *)(0x01102a88 + DPSTART);                                      
static float       *rbdgder= (void *)(0x01102ad8 + DPSTART);                                    
static float       *rbdgf= (void *)(0x01102b28 + DPSTART);                                      
static float       *rbdgfastoff= (void *)(0x01102b78 + DPSTART);                                
static float       *rbdgint= (void *)(0x01102bc8 + DPSTART);                                    
static float       *rbdgloff= (void *)(0x01102c18 + DPSTART);                                   
static float       *rbdglon= (void *)(0x01102c68 + DPSTART);                                    
static float       *rbdgpro= (void *)(0x01102cb8 + DPSTART);                                    
static float       *rbdgxe= (void *)(0x01102d08 + DPSTART);                                     
static float       *rbdifbd= (void *)(0x01102d58 + DPSTART);                                    
static float       *rbdlagtau= (void *)(0x01102da8 + DPSTART);                                  
static float       *rbdvmn1= (void *)(0x01102dac + DPSTART);                                    
static float       *rbdx1= (void *)(0x01102dfc + DPSTART);                                      
static float       *rbdx2= (void *)(0x01102e4c + DPSTART);                                      
static float       *rbdxatgl= (void *)(0x01102e9c + DPSTART);                                   
static float       *rbdxe= (void *)(0x01102eec + DPSTART);                                      
static float       *rbdxen1= (void *)(0x01102f3c + DPSTART);                                    
static float       **rbdfatgi= (void *)(0x01102f8c + DPSTART);                                  
static float       **rbdflmaxi= (void *)(0x01102fdc + DPSTART);                                 
static float       **rbdfmi= (void *)(0x0110302c + DPSTART);                                    
static float       **rbdvmi= (void *)(0x0110307c + DPSTART);                                    
static float       **rbdxatgi= (void *)(0x011030cc + DPSTART);                                  
static float       **rbdxmi= (void *)(0x0110311c + DPSTART);                                    
static char        *rbdbatg= (void *)(0x0110316c + DPSTART);                                    
static char        *rbdfastoff= (void *)(0x01103180 + DPSTART);                                 
static char        **rbdbftgi= (void *)(0x01103194 + DPSTART);                                  
static char        **rbdbxtgi= (void *)(0x011031e4 + DPSTART);                                  
static float       *GCTLBUF_RCA= (void *)(0x011036b8 + DPSTART);                                
static float       *rcaaolimt= (void *)(0x011036b8 + DPSTART);                                  
static float       *rcaenctraverr= (void *)(0x01103708 + DPSTART);                              
static float       *rcafnlim= (void *)(0x01103758 + DPSTART);                                   
static float       *rcafplim= (void *)(0x011037a8 + DPSTART);                                   
static float       *rcamutab= (void *)(0x011037f8 + DPSTART);                                   
static float       *rcanullfai= (void *)(0x01103938 + DPSTART);                                 
static float       *rcanullfail= (void *)(0x01103988 + DPSTART);                                
static float       *rcanullpai= (void *)(0x011039d8 + DPSTART);                                 
static float       *rcapotctr= (void *)(0x01103a28 + DPSTART);                                  
static float       *rcapotctrai= (void *)(0x01103a78 + DPSTART);                                
static float       *rcapotenctrackerr= (void *)(0x01103ac8 + DPSTART);                          
static float       *rcapottraverr= (void *)(0x01103b18 + DPSTART);                              
static float       *rcaskit= (void *)(0x01103b68 + DPSTART);                                    
static float       *rcatampol= (void *)(0x01103bb8 + DPSTART);                                  
static float       *rcatampsol= (void *)(0x01103c08 + DPSTART);                                 
static float       *rcatsttime= (void *)(0x01103c58 + DPSTART);                                 
static float       *rcavcmdt= (void *)(0x01103ca8 + DPSTART);                                   
static float       *rcavegt= (void *)(0x01103cf8 + DPSTART);                                    
static float       *rcaxmsn= (void *)(0x01103d48 + DPSTART);                                    
static float       *rcaxmsp= (void *)(0x01103d98 + DPSTART);                                    
static float       *rcaxmt= (void *)(0x01103de8 + DPSTART);                                     
static float       *rcaxnlim= (void *)(0x01103e38 + DPSTART);                                   
static float       *rcaxnlimp= (void *)(0x01103e88 + DPSTART);                                  
static float       *rcaxnlimpai= (void *)(0x01103ed8 + DPSTART);                                
static float       *rcaxplim= (void *)(0x01103f28 + DPSTART);                                   
static float       *rcaxplimp= (void *)(0x01103f78 + DPSTART);                                  
static float       *rcaxplimpai= (void *)(0x01103fc8 + DPSTART);                                
static float       *rcaxtote= (void *)(0x01104018 + DPSTART);                                   
static float       *rcaxtotp= (void *)(0x01104068 + DPSTART);                                   
static float       *rcaruntime= (void *)(0x011040b8 + DPSTART);                                 
static float       *rcaaacc= (void *)(0x01104108 + DPSTART);                                    
static float       *rcaac= (void *)(0x01104158 + DPSTART);                                      
static float       *rcaAc_f= (void *)(0x011041a8 + DPSTART);                                    
static float       *rcaacn1= (void *)(0x01104388 + DPSTART);                                    
static float       *rcaae= (void *)(0x011043d8 + DPSTART);                                      
static float       *rcaalg= (void *)(0x01104428 + DPSTART);                                     
static float       *rcaalgn1= (void *)(0x01104478 + DPSTART);                                   
static float       *rcaamg= (void *)(0x011044c8 + DPSTART);                                     
static float       *rcaao= (void *)(0x01104518 + DPSTART);                                      
static float       *rcaaoc= (void *)(0x01104568 + DPSTART);                                     
static float       *rcaaoelim= (void *)(0x011045b8 + DPSTART);                                  
static float       *rcaaolim= (void *)(0x01104608 + DPSTART);                                   
static float       *rcaapos= (void *)(0x01104658 + DPSTART);                                    
static float       *rcaaposc= (void *)(0x011046a8 + DPSTART);                                   
static float       *rcaaposcg= (void *)(0x011046f8 + DPSTART);                                  
static float       *rcaate= (void *)(0x01104748 + DPSTART);                                     
static float       *rcaateg= (void *)(0x01104798 + DPSTART);                                    
static float       *rcaati= (void *)(0x011047e8 + DPSTART);                                     
static float       *rcaatig= (void *)(0x01104838 + DPSTART);                                    
static float       *rcaavel= (void *)(0x01104888 + DPSTART);                                    
static float       *rcaavelc= (void *)(0x011048d8 + DPSTART);                                   
static float       *rcaavelcg= (void *)(0x01104928 + DPSTART);                                  
static float       *rcacurrentc= (void *)(0x01104978 + DPSTART);                                
static float       *rcacurrentlag= (void *)(0x011049c8 + DPSTART);                              
static float       *rcaeasy= (void *)(0x011049cc + DPSTART);                                    
static float       *rcaeasy2= (void *)(0x01104a1c + DPSTART);                                   
static float       *rcaeasyl= (void *)(0x01104a6c + DPSTART);                                   
static float       *rcaencv= (void *)(0x01104abc + DPSTART);                                    
static float       *rcaencx= (void *)(0x01104b0c + DPSTART);                                    
static float       *rcaf2amp= (void *)(0x01104b5c + DPSTART);                                   
static float       *rcafa= (void *)(0x01104bac + DPSTART);                                      
static float       *rcaFa_f= (void *)(0x01104bfc + DPSTART);                                    
static float       *rcafaio= (void *)(0x01104ddc + DPSTART);                                    
static float       *rcafalag= (void *)(0x01104e2c + DPSTART);                                   
static float       *rcafc= (void *)(0x01104e30 + DPSTART);                                      
static float       *rcaFc_f= (void *)(0x01104e80 + DPSTART);                                    
static float       *rcafclag= (void *)(0x01105060 + DPSTART);                                   
static float       *rcafclf= (void *)(0x011050b0 + DPSTART);                                    
static float       *rcafclim= (void *)(0x01105100 + DPSTART);                                   
static float       *rcafclon= (void *)(0x01105150 + DPSTART);                                   
static float       *rcafcn1= (void *)(0x011051a0 + DPSTART);                                    
static float       *rcafl= (void *)(0x011051f0 + DPSTART);                                      
static float       *rcaflg= (void *)(0x01105240 + DPSTART);                                     
static float       *rcaflgn1= (void *)(0x01105290 + DPSTART);                                   
static float       *rcaflgo= (void *)(0x011052e0 + DPSTART);                                    
static float       *rcaflin= (void *)(0x01105330 + DPSTART);                                    
static float       *rcafmubc= (void *)(0x01105380 + DPSTART);                                   
static float       *rcagcount= (void *)(0x011053d0 + DPSTART);                                  
static float       *rcagflg= (void *)(0x01105420 + DPSTART);                                    
static float       *rcagfln= (void *)(0x01105470 + DPSTART);                                    
static float       *rcagflp= (void *)(0x011054c0 + DPSTART);                                    
static float       *rcagmtr= (void *)(0x01105510 + DPSTART);                                    
static float       *rcagvcmd= (void *)(0x01105560 + DPSTART);                                   
static float       *rcagxl= (void *)(0x011055b0 + DPSTART);                                     
static float       *rcagxlg= (void *)(0x01105600 + DPSTART);                                    
static float       *rcairate= (void *)(0x01105650 + DPSTART);                                   
static float       *rcaitime= (void *)(0x011056a0 + DPSTART);                                   
static float       *rcaixc= (void *)(0x011056f0 + DPSTART);                                     
static float       *rcalag= (void *)(0x01105740 + DPSTART);                                     
static float       *rcalead= (void *)(0x01105790 + DPSTART);                                    
static float       *rcap1= (void *)(0x011057e0 + DPSTART);                                      
static float       *rcap2= (void *)(0x01105830 + DPSTART);                                      
static float       *rcapaf= (void *)(0x01105880 + DPSTART);                                     
static float       *rcapkramp= (void *)(0x011058d0 + DPSTART);                                  
static float       *rcapowerc= (void *)(0x011058d4 + DPSTART);                                  
static float       *rcapramp= (void *)(0x01105924 + DPSTART);                                   
static float       *rcapwrbase= (void *)(0x01105974 + DPSTART);                                 
static float       *rcascmd= (void *)(0x01105c44 + DPSTART);                                    
static float       *rcascmdf= (void *)(0x01105c94 + DPSTART);                                   
static float       *rcascmdn= (void *)(0x01105ce4 + DPSTART);                                   
static float       *rcaskd= (void *)(0x01105d34 + DPSTART);                                     
static float       *rcaskf= (void *)(0x01105d84 + DPSTART);                                     
static float       *rcaski= (void *)(0x01105dd4 + DPSTART);                                     
static float       *rcaskif= (void *)(0x01105e24 + DPSTART);                                    
static float       *rcaskife= (void *)(0x01105e74 + DPSTART);                                   
static float       *rcaskp= (void *)(0x01105ec4 + DPSTART);                                     
static float       *rcasnull= (void *)(0x01105f14 + DPSTART);                                   
static float       *rcatime= (void *)(0x01105f64 + DPSTART);                                    
static float       *rcatstgn= (void *)(0x01105fb4 + DPSTART);                                   
static float       *rcatstin= (void *)(0x01105fbc + DPSTART);                                   
static float       *rcava= (void *)(0x01105fc4 + DPSTART);                                      
static float       *rcaVa_f= (void *)(0x01106014 + DPSTART);                                    
static float       *rcavalag= (void *)(0x011061f4 + DPSTART);                                   
static float       *rcavc= (void *)(0x011061f8 + DPSTART);                                      
static float       *rcaVc_f= (void *)(0x01106248 + DPSTART);                                    
static float       *rcavcg= (void *)(0x01106428 + DPSTART);                                     
static float       *rcavclag= (void *)(0x01106478 + DPSTART);                                   
static float       *rcavclf= (void *)(0x011064c8 + DPSTART);                                    
static float       *rcavclim= (void *)(0x01106518 + DPSTART);                                   
static float       *rcavclon= (void *)(0x01106568 + DPSTART);                                   
static float       *rcavcmd= (void *)(0x011065b8 + DPSTART);                                    
static float       *rcavcmdl= (void *)(0x01106608 + DPSTART);                                   
static float       *rcavcn1= (void *)(0x01106658 + DPSTART);                                    
static float       *rcave= (void *)(0x011066a8 + DPSTART);                                      
static float       *rcaveg= (void *)(0x011066f8 + DPSTART);                                     
static float       *rcavegf= (void *)(0x01106748 + DPSTART);                                    
static float       *rcavie= (void *)(0x01106798 + DPSTART);                                     
static float       *rcavieg= (void *)(0x011067e8 + DPSTART);                                    
static float       *rcavl= (void *)(0x01106838 + DPSTART);                                      
static float       *rcavlg= (void *)(0x01106888 + DPSTART);                                     
static float       *rcavlgn1= (void *)(0x011068d8 + DPSTART);                                   
static float       *rcavll= (void *)(0x01106928 + DPSTART);                                     
static float       *rcawraperr= (void *)(0x01106978 + DPSTART);                                 
static float       *rcawraperrlim= (void *)(0x011069c8 + DPSTART);                              
static float       *rcaxa= (void *)(0x01106a18 + DPSTART);                                      
static float       *rcaXa_f= (void *)(0x01106a68 + DPSTART);                                    
static float       *rcaxaio= (void *)(0x01106c48 + DPSTART);                                    
static float       *rcaxalag= (void *)(0x01106c98 + DPSTART);                                   
static float       *rcaxc= (void *)(0x01106c9c + DPSTART);                                      
static float       *rcaXc_f= (void *)(0x01106cec + DPSTART);                                    
static float       *rcaxclag= (void *)(0x01106ecc + DPSTART);                                   
static float       *rcaxcn1= (void *)(0x01106f1c + DPSTART);                                    
static float       *rcaxe= (void *)(0x01106f6c + DPSTART);                                      
static float       *rcaxee= (void *)(0x01106fbc + DPSTART);                                     
static float       *rcaxeea= (void *)(0x0110700c + DPSTART);                                    
static float       *rcaxeealag= (void *)(0x0110705c + DPSTART);                                 
static float       *rcaxeeg= (void *)(0x01107060 + DPSTART);                                    
static float       *rcaxeel= (void *)(0x011070b0 + DPSTART);                                    
static float       *rcaxeelg= (void *)(0x01107100 + DPSTART);                                   
static float       *rcaxeelim= (void *)(0x01107150 + DPSTART);                                  
static float       *rcaxeelt= (void *)(0x011071a0 + DPSTART);                                   
static float       *rcaxeelv= (void *)(0x011071f0 + DPSTART);                                   
static float       *rcaxelf= (void *)(0x01107240 + DPSTART);                                    
static float       *rcaxelim= (void *)(0x01107290 + DPSTART);                                   
static float       *rcaxelon= (void *)(0x011072e0 + DPSTART);                                   
static float       *rcaxie= (void *)(0x01107330 + DPSTART);                                     
static float       *rcaxieg= (void *)(0x01107380 + DPSTART);                                    
static float       *rcaxl= (void *)(0x011073d0 + DPSTART);                                      
static float       *rcaxlg= (void *)(0x01107420 + DPSTART);                                     
static float       *rcaxlgn1= (void *)(0x01107470 + DPSTART);                                   
static float       *rcaxlgo= (void *)(0x011074c0 + DPSTART);                                    
static float       *rcaxlgok= (void *)(0x01107510 + DPSTART);                                   
static float       *rcaxll= (void *)(0x01107514 + DPSTART);                                     
static float       *rcaxllag= (void *)(0x01107564 + DPSTART);                                   
static float       *rcaxsc= (void *)(0x011075b4 + DPSTART);                                     
static float       *rcaamp_volt= (void *)(0x01107604 + DPSTART);                                
static float       *rcacurrent= (void *)(0x01107654 + DPSTART);                                 
static float       *rcamaxpwr= (void *)(0x011076a4 + DPSTART);                                  
static float       *rcamaxvolt= (void *)(0x011076f4 + DPSTART);                                 
static float       *rcapower= (void *)(0x01107744 + DPSTART);                                   
static float       *rcapowerout= (void *)(0x01107794 + DPSTART);                                
static float       *rcapwrlim= (void *)(0x011077e4 + DPSTART);                                  
static float       *rcaratedcurr= (void *)(0x01107834 + DPSTART);                               
static float       *rcefcnt= (void *)(0x01107884 + DPSTART);                                    
static float       *rcefcntd= (void *)(0x011078d4 + DPSTART);                                   
static float       **rcaami= (void *)(0x01107924 + DPSTART);                                    
static float       **rcafai= (void *)(0x01107974 + DPSTART);                                    
static float       **rcavai= (void *)(0x011079c4 + DPSTART);                                    
static float       **rcavcmdi= (void *)(0x01107a14 + DPSTART);                                  
static float       **rcavmi= (void *)(0x01107a64 + DPSTART);                                    
static float       **rcaxai= (void *)(0x01107ab4 + DPSTART);                                    
static float       **rcaxmi= (void *)(0x01107b04 + DPSTART);                                    
static float       **rcacurrenti= (void *)(0x01107b54 + DPSTART);                               
static char        *rcakfilt= (void *)(0x01107ba4 + DPSTART);                                   
static long        *rcaterror= (void *)(0x01107bf4 + DPSTART);                                  
static long        *rcatno= (void *)(0x01107c44 + DPSTART);                                     
static long        *rcatnop= (void *)(0x01107c94 + DPSTART);                                    
static long        *rcaruncnt= (void *)(0x01107ce4 + DPSTART);                                  
static long        *rcaabort= (void *)(0x01107d34 + DPSTART);                                   
static long        *rcaAc_ft= (void *)(0x01107d84 + DPSTART);                                   
static long        *rcaaovrd= (void *)(0x01107dd4 + DPSTART);                                   
static long        *rcadidomask= (void *)(0x01107e24 + DPSTART);                                
static long        *rcadidomaskt= (void *)(0x01107e28 + DPSTART);                               
static long        *rcadidonum= (void *)(0x01107e2c + DPSTART);                                 
static long        *rcadidotest= (void *)(0x01107e30 + DPSTART);                                
static long        *rcaFa_ft= (void *)(0x01107e34 + DPSTART);                                   
static long        *rcaFc_ft= (void *)(0x01107e84 + DPSTART);                                   
static long        *rcaicount= (void *)(0x01107ed4 + DPSTART);                                  
static long        *rcaicount1= (void *)(0x01107f24 + DPSTART);                                 
static long        *rcaiframe= (void *)(0x01107f74 + DPSTART);                                  
static short       *rcabufai= (void *)(0x01107fc4 + DPSTART);                                   
static long        *rcaiobuf= (void *)(0x01107fc4 + DPSTART);                                   
static float       *rcafltai= (void *)(0x011080b4 + DPSTART);                                   
static short       *rcabufao= (void *)(0x01108294 + DPSTART);                                   
static float       *rcafltao= (void *)(0x01108384 + DPSTART);                                   
static long        *rcapkdido= (void *)(0x01108564 + DPSTART);                                  
static char        *rcabufdi= (void *)(0x011085a4 + DPSTART);                                   
static char        *rcabufdo= (void *)(0x01108624 + DPSTART);                                   
static long        *rcaoncnt= (void *)(0x01108f64 + DPSTART);                                   
static long        *rcarsettm= (void *)(0x01108fb4 + DPSTART);                                  
static long        *rcaVa_ft= (void *)(0x01109004 + DPSTART);                                   
static long        *rcaVc_ft= (void *)(0x01109054 + DPSTART);                                   
static long        *rcaXa_ft= (void *)(0x011090a4 + DPSTART);                                   
static long        *rcaXc_ft= (void *)(0x011090f4 + DPSTART);                                   
static long        *rceicnt= (void *)(0x01109144 + DPSTART);                                    
static char        *rcaampfail= (void *)(0x01109194 + DPSTART);                                 
static char        *rcainhibit= (void *)(0x011091a8 + DPSTART);                                 
static char        *rcamaint= (void *)(0x011091bc + DPSTART);                                   
static char        *rcaon= (void *)(0x011091d0 + DPSTART);                                      
static char        *rcaovrtmp= (void *)(0x011091e4 + DPSTART);                                  
static char        *rcapwrok= (void *)(0x011091f8 + DPSTART);                                   
static char        *rcareset= (void *)(0x0110920c + DPSTART);                                   
static char        *rcarun= (void *)(0x01109220 + DPSTART);                                     
static char        *rcatestdido= (void *)(0x01109234 + DPSTART);                                
static char        *rcatoggle= (void *)(0x01109274 + DPSTART);                                  
static char        *rcausepot= (void *)(0x01109288 + DPSTART);                                  
static long        *rcastart= (void *)(0x0110929c + DPSTART);                                   
static char        *rcachantype= (void *)(0x011092ec + DPSTART);                                
static float       *GCTLBUF_RCB= (void *)(0x01109c48 + DPSTART);                                
static float       *rcbcddb= (void *)(0x01109c48 + DPSTART);                                    
static float       *rcbcdn= (void *)(0x01109c98 + DPSTART);                                     
static float       *rcbcdp= (void *)(0x01109ce8 + DPSTART);                                     
static float       *rcbfd= (void *)(0x01109d38 + DPSTART);                                      
static float       *rcbfs= (void *)(0x01109d88 + DPSTART);                                      
static float       *rcbft= (void *)(0x01109dd8 + DPSTART);                                      
static float       *rcbkn= (void *)(0x01109e28 + DPSTART);                                      
static float       *rcbkp= (void *)(0x01109e78 + DPSTART);                                      
static float       *rcbve= (void *)(0x01109ec8 + DPSTART);                                      
static float       *rcbxdb= (void *)(0x01109f18 + DPSTART);                                     
static float       *rcbxe= (void *)(0x01109f68 + DPSTART);                                      
static float       **rcbv1i= (void *)(0x01109fb8 + DPSTART);                                    
static float       **rcbv2i= (void *)(0x0110a008 + DPSTART);                                    
static float       **rcbx1i= (void *)(0x0110a058 + DPSTART);                                    
static float       **rcbx2i= (void *)(0x0110a0a8 + DPSTART);                                    
static float       *GCTLBUF_RCS= (void *)(0x0110a800 + DPSTART);                                
static float       *rcsam= (void *)(0x0110a800 + DPSTART);                                      
static float       *rcscd= (void *)(0x0110a850 + DPSTART);                                      
static float       *rcsdelt= (void *)(0x0110a8a0 + DPSTART);                                    
static float       *rcsf1frc= (void *)(0x0110a8f0 + DPSTART);                                   
static float       *rcsf1pos= (void *)(0x0110a940 + DPSTART);                                   
static float       *rcsf1vel= (void *)(0x0110a990 + DPSTART);                                   
static float       *rcsfd= (void *)(0x0110a9e0 + DPSTART);                                      
static float       *rcsffrc= (void *)(0x0110aa30 + DPSTART);                                    
static float       *rcsffst= (void *)(0x0110aa80 + DPSTART);                                    
static float       *rcsffv= (void *)(0x0110aad0 + DPSTART);                                     
static float       *rcsfhm= (void *)(0x0110ab20 + DPSTART);                                     
static float       *rcsfpos= (void *)(0x0110ab70 + DPSTART);                                    
static float       *rcsfref= (void *)(0x0110abc0 + DPSTART);                                    
static float       *rcsft= (void *)(0x0110ac10 + DPSTART);                                      
static float       *rcsfvel= (void *)(0x0110ac60 + DPSTART);                                    
static float       *rcsgf= (void *)(0x0110acb0 + DPSTART);                                      
static float       *rcsgfk= (void *)(0x0110ad00 + DPSTART);                                     
static float       *rcsghm= (void *)(0x0110ad50 + DPSTART);                                     
static float       *rcsgm= (void *)(0x0110ada0 + DPSTART);                                      
static float       *rcsivm= (void *)(0x0110adf0 + DPSTART);                                     
static float       *rcsixm= (void *)(0x0110ae40 + DPSTART);                                     
static float       *rcstretch= (void *)(0x0110ae90 + DPSTART);                                  
static float       *rcstretchk= (void *)(0x0110aee0 + DPSTART);                                 
static float       *rcstretchlm= (void *)(0x0110af30 + DPSTART);                                
static float       *rcsvcmd= (void *)(0x0110af80 + DPSTART);                                    
static float       *rcsvcr= (void *)(0x0110afd0 + DPSTART);                                     
static float       *rcsvf= (void *)(0x0110b020 + DPSTART);                                      
static float       *rcsvfk= (void *)(0x0110b070 + DPSTART);                                     
static float       *rcsvfs= (void *)(0x0110b0c0 + DPSTART);                                     
static float       *rcsvm= (void *)(0x0110b110 + DPSTART);                                      
static float       *rcsvr= (void *)(0x0110b160 + DPSTART);                                      
static float       *rcsvref= (void *)(0x0110b1b0 + DPSTART);                                    
static float       *rcsxm= (void *)(0x0110b200 + DPSTART);                                      
static float       *rcsxmln= (void *)(0x0110b250 + DPSTART);                                    
static float       *rcsxmlp= (void *)(0x0110b2a0 + DPSTART);                                    
static float       **rcsf1ai= (void *)(0x0110b2f0 + DPSTART);                                   
static float       **rcsf1fi= (void *)(0x0110b340 + DPSTART);                                   
static float       **rcsf2ai= (void *)(0x0110b390 + DPSTART);                                   
static float       **rcsf2fi= (void *)(0x0110b3e0 + DPSTART);                                   
static float       **rcsf3ai= (void *)(0x0110b430 + DPSTART);                                   
static float       **rcsf4ai= (void *)(0x0110b480 + DPSTART);                                   
static float       **rcsf5ai= (void *)(0x0110b4d0 + DPSTART);                                   
static float       **rcsfhmi= (void *)(0x0110b520 + DPSTART);                                   
static long        *rcsbcmd= (void *)(0x0110b570 + DPSTART);                                    
static char        **rcsbmji= (void *)(0x0110b5c0 + DPSTART);                                   
static float       *GCTLBUF_RFG= (void *)(0x0110bb88 + DPSTART);                                
static float       *rfgfao= (void *)(0x0110bb88 + DPSTART);                                     
static float       *rfgfcl= (void *)(0x0110bbd8 + DPSTART);                                     
static float       *rfgfclc= (void *)(0x0110bc28 + DPSTART);                                    
static float       *rfgfct= (void *)(0x0110bc78 + DPSTART);                                     
static float       *rfgff= (void *)(0x0110bcc8 + DPSTART);                                      
static float       *rfgffn= (void *)(0x0110bd18 + DPSTART);                                     
static float       *rfgffp= (void *)(0x0110bd68 + DPSTART);                                     
static float       *rfgfll= (void *)(0x0110bdb8 + DPSTART);                                     
static float       *rfgfllc= (void *)(0x0110be08 + DPSTART);                                    
static float       *rfgflt= (void *)(0x0110be58 + DPSTART);                                     
static float       *rfgfltc= (void *)(0x0110bea8 + DPSTART);                                    
static float       *rfgfpt= (void *)(0x0110bef8 + DPSTART);                                     
static float       *rfggf= (void *)(0x0110bf48 + DPSTART);                                      
static float       *rfggfo= (void *)(0x0110bf98 + DPSTART);                                     
static float       *rfgglr= (void *)(0x0110bfe8 + DPSTART);                                     
static float       *rfggma= (void *)(0x0110c038 + DPSTART);                                     
static float       *rfggxp= (void *)(0x0110c088 + DPSTART);                                     
static float       *rfggxv= (void *)(0x0110c0d8 + DPSTART);                                     
static float       *rfgiff= (void *)(0x0110c128 + DPSTART);                                     
static float       *rfgigxf= (void *)(0x0110c178 + DPSTART);                                    
static float       *rfgkl= (void *)(0x0110c1c8 + DPSTART);                                      
static float       *rfgkt= (void *)(0x0110c218 + DPSTART);                                      
static float       *rfgvdeg= (void *)(0x0110c268 + DPSTART);                                    
static float       *rfgvlin= (void *)(0x0110c2b8 + DPSTART);                                    
static float       *rfgvll= (void *)(0x0110c308 + DPSTART);                                     
static float       *rfgvlr= (void *)(0x0110c358 + DPSTART);                                     
static float       *rfgvrad= (void *)(0x0110c3a8 + DPSTART);                                    
static float       *rfgxbl= (void *)(0x0110c3f8 + DPSTART);                                     
static float       *rfgxbr= (void *)(0x0110c448 + DPSTART);                                     
static float       *rfgxcl= (void *)(0x0110c498 + DPSTART);                                     
static float       *rfgxcos= (void *)(0x0110c4e8 + DPSTART);                                    
static float       *rfgxcr= (void *)(0x0110c538 + DPSTART);                                     
static float       *rfgxdeg= (void *)(0x0110c588 + DPSTART);                                    
static float       *rfgxlin= (void *)(0x0110c5d8 + DPSTART);                                    
static float       *rfgxll= (void *)(0x0110c628 + DPSTART);                                     
static float       *rfgxlr= (void *)(0x0110c678 + DPSTART);                                     
static float       *rfgxpd= (void *)(0x0110c6c8 + DPSTART);                                     
static float       *rfgxpl= (void *)(0x0110c718 + DPSTART);                                     
static float       *rfgxpr= (void *)(0x0110c768 + DPSTART);                                     
static float       *rfgxrad= (void *)(0x0110c7b8 + DPSTART);                                    
static float       *rfgxsd= (void *)(0x0110c808 + DPSTART);                                     
static float       *rfgxsin= (void *)(0x0110c858 + DPSTART);                                    
static float       *rfgxsl= (void *)(0x0110c8a8 + DPSTART);                                     
static float       *rfgxsr= (void *)(0x0110c8f8 + DPSTART);                                     
static float       *rfgxtdc= (void *)(0x0110c948 + DPSTART);                                    
static float       **rfgfai= (void *)(0x0110c998 + DPSTART);                                    
static float       **rfgfctci= (void *)(0x0110c9e8 + DPSTART);                                  
static float       **rfgvai= (void *)(0x0110ca38 + DPSTART);                                    
static float       **rfgxai= (void *)(0x0110ca88 + DPSTART);                                    
static long        **rfgbcli= (void *)(0x0110cad8 + DPSTART);                                   
static float       *GCTLBUF_RFM= (void *)(0x0110cf10 + DPSTART);                                
static float       *rfmcdf= (void *)(0x0110cf10 + DPSTART);                                     
static float       *rfmcfkf= (void *)(0x0110cf60 + DPSTART);                                    
static float       *rfmcfsf= (void *)(0x0110cfb0 + DPSTART);                                    
static float       *rfmgfkf= (void *)(0x0110d000 + DPSTART);                                    
static float       *rfmacmd= (void *)(0x0110d050 + DPSTART);                                    
static float       *rfmacmn1= (void *)(0x0110d0a0 + DPSTART);                                   
static float       *rfmafk= (void *)(0x0110d0f0 + DPSTART);                                     
static float       *rfmafkn1= (void *)(0x0110d140 + DPSTART);                                   
static float       *rfmafs= (void *)(0x0110d190 + DPSTART);                                     
static float       *rfmafsn1= (void *)(0x0110d1e0 + DPSTART);                                   
static float       *rfmam= (void *)(0x0110d230 + DPSTART);                                      
static float       *rfmamn1= (void *)(0x0110d280 + DPSTART);                                    
static float       *rfmcd= (void *)(0x0110d2d0 + DPSTART);                                      
static float       *rfmcdln= (void *)(0x0110d320 + DPSTART);                                    
static float       *rfmcdlp= (void *)(0x0110d370 + DPSTART);                                    
static float       *rfmcdn= (void *)(0x0110d3c0 + DPSTART);                                     
static float       *rfmcdp= (void *)(0x0110d410 + DPSTART);                                     
static float       *rfmcdsn= (void *)(0x0110d460 + DPSTART);                                    
static float       *rfmcdsp= (void *)(0x0110d4b0 + DPSTART);                                    
static float       *rfmcfkn= (void *)(0x0110d500 + DPSTART);                                    
static float       *rfmcfkp= (void *)(0x0110d550 + DPSTART);                                    
static float       *rfmcfsn= (void *)(0x0110d5a0 + DPSTART);                                    
static float       *rfmcfsp= (void *)(0x0110d5f0 + DPSTART);                                    
static float       *rfmdelt= (void *)(0x0110d640 + DPSTART);                                    
static float       *rfmeasy= (void *)(0x0110d690 + DPSTART);                                    
static float       *rfmfcmd= (void *)(0x0110d6e0 + DPSTART);                                    
static float       *rfmfd= (void *)(0x0110d730 + DPSTART);                                      
static float       *rfmff= (void *)(0x0110d780 + DPSTART);                                      
static float       *rfmffk= (void *)(0x0110d7d0 + DPSTART);                                     
static float       *rfmfflk= (void *)(0x0110d820 + DPSTART);                                    
static float       *rfmffn= (void *)(0x0110d870 + DPSTART);                                     
static float       *rfmffp= (void *)(0x0110d8c0 + DPSTART);                                     
static float       *rfmffs= (void *)(0x0110d910 + DPSTART);                                     
static float       *rfmfl= (void *)(0x0110d960 + DPSTART);                                      
static float       *rfmfp= (void *)(0x0110d9b0 + DPSTART);                                      
static float       *rfmfpc= (void *)(0x0110da00 + DPSTART);                                     
static float       *rfmfs= (void *)(0x0110da50 + DPSTART);                                      
static float       *rfmfsc= (void *)(0x0110daa0 + DPSTART);                                     
static float       *rfmft= (void *)(0x0110daf0 + DPSTART);                                      
static float       *rfmgf= (void *)(0x0110db40 + DPSTART);                                      
static float       *rfmgfade= (void *)(0x0110db90 + DPSTART);                                   
static float       *rfmgfkn= (void *)(0x0110dbe0 + DPSTART);                                    
static float       *rfmgfkp= (void *)(0x0110dc30 + DPSTART);                                    
static float       *rfmgfs= (void *)(0x0110dc80 + DPSTART);                                     
static float       *rfmgm= (void *)(0x0110dcd0 + DPSTART);                                      
static float       *rfmgmeasy= (void *)(0x0110dd20 + DPSTART);                                  
static float       *rfmgsc= (void *)(0x0110dd70 + DPSTART);                                     
static float       *rfmgsn= (void *)(0x0110ddc0 + DPSTART);                                     
static float       *rfmgsp= (void *)(0x0110de10 + DPSTART);                                     
static float       *rfmgvdrive= (void *)(0x0110de60 + DPSTART);                                 
static float       *rfmgxc= (void *)(0x0110deb0 + DPSTART);                                     
static float       *rfmiff= (void *)(0x0110df00 + DPSTART);                                     
static float       *rfmigm= (void *)(0x0110df50 + DPSTART);                                     
static float       *rfmivc= (void *)(0x0110dfa0 + DPSTART);                                     
static float       *rfmivm= (void *)(0x0110dff0 + DPSTART);                                     
static float       *rfmixc= (void *)(0x0110e040 + DPSTART);                                     
static float       *rfmixcn1= (void *)(0x0110e090 + DPSTART);                                   
static float       *rfmixm= (void *)(0x0110e0e0 + DPSTART);                                     
static float       *rfmkdt= (void *)(0x0110e130 + DPSTART);                                     
static float       *rfmkln= (void *)(0x0110e180 + DPSTART);                                     
static float       *rfmklp= (void *)(0x0110e1d0 + DPSTART);                                     
static float       *rfmtamp= (void *)(0x0110e220 + DPSTART);                                    
static float       *rfmtampc= (void *)(0x0110e270 + DPSTART);                                   
static float       *rfmtampfade= (void *)(0x0110e2c0 + DPSTART);                                
static float       *rfmtamplagk= (void *)(0x0110e2c4 + DPSTART);                                
static float       *rfmtcps= (void *)(0x0110e2c8 + DPSTART);                                    
static float       *rfmtcyc= (void *)(0x0110e318 + DPSTART);                                    
static float       *rfmtinitlag= (void *)(0x0110e368 + DPSTART);                                
static float       *rfmtsin= (void *)(0x0110e36c + DPSTART);                                    
static float       *rfmtv= (void *)(0x0110e3bc + DPSTART);                                      
static float       *rfmtxe= (void *)(0x0110e40c + DPSTART);                                     
static float       *rfmtxelim= (void *)(0x0110e45c + DPSTART);                                  
static float       *rfmtxm= (void *)(0x0110e4ac + DPSTART);                                     
static float       *rfmvc= (void *)(0x0110e4fc + DPSTART);                                      
static float       *rfmvcmd= (void *)(0x0110e54c + DPSTART);                                    
static float       *rfmvdrive= (void *)(0x0110e59c + DPSTART);                                  
static float       *rfmvfk= (void *)(0x0110e5ec + DPSTART);                                     
static float       *rfmvfr= (void *)(0x0110e63c + DPSTART);                                     
static float       *rfmvfs= (void *)(0x0110e68c + DPSTART);                                     
static float       *rfmvm= (void *)(0x0110e6dc + DPSTART);                                      
static float       *rfmvmax= (void *)(0x0110e72c + DPSTART);                                    
static float       *rfmvmn1= (void *)(0x0110e77c + DPSTART);                                    
static float       *rfmxc= (void *)(0x0110e7cc + DPSTART);                                      
static float       *rfmxcn1= (void *)(0x0110e81c + DPSTART);                                    
static float       *rfmxln= (void *)(0x0110e86c + DPSTART);                                     
static float       *rfmxlp= (void *)(0x0110e8bc + DPSTART);                                     
static float       *rfmxm= (void *)(0x0110e90c + DPSTART);                                      
static float       *rfmxmln= (void *)(0x0110e95c + DPSTART);                                    
static float       *rfmxmlp= (void *)(0x0110e9ac + DPSTART);                                    
static float       *rfmxmsn= (void *)(0x0110e9fc + DPSTART);                                    
static float       *rfmxmsp= (void *)(0x0110ea4c + DPSTART);                                    
static float       *rfmxstr= (void *)(0x0110ea9c + DPSTART);                                    
static float       *rfmxstrtau= (void *)(0x0110eaec + DPSTART);                                 
static float       *rfmafr= (void *)(0x0110eb3c + DPSTART);                                     
static float       *rfmafrn1= (void *)(0x0110eb8c + DPSTART);                                   
static float       *rfmffst= (void *)(0x0110ebdc + DPSTART);                                    
static float       *rfmgfk= (void *)(0x0110ec2c + DPSTART);                                     
static float       *rfmgfkr= (void *)(0x0110ec7c + DPSTART);                                    
static float       *rfmvcr= (void *)(0x0110eccc + DPSTART);                                     
static float       *rfmvf= (void *)(0x0110ed1c + DPSTART);                                      
static float       *rfmvfsr= (void *)(0x0110ed6c + DPSTART);                                    
static float       *rfmvr= (void *)(0x0110edbc + DPSTART);                                      
static float       *rfmvslp= (void *)(0x0110ee0c + DPSTART);                                    
static float       **rfmf1ai= (void *)(0x0110ee5c + DPSTART);                                   
static float       **rfmf1bdi= (void *)(0x0110eeac + DPSTART);                                  
static float       **rfmf1fi= (void *)(0x0110eefc + DPSTART);                                   
static float       **rfmf2ai= (void *)(0x0110ef4c + DPSTART);                                   
static float       **rfmf2fi= (void *)(0x0110ef9c + DPSTART);                                   
static float       **rfmfbdi= (void *)(0x0110efec + DPSTART);                                   
static float       **rfmfbwi= (void *)(0x0110f03c + DPSTART);                                   
static float       **rfmfpafi= (void *)(0x0110f08c + DPSTART);                                  
static float       **rfmvmi= (void *)(0x0110f0dc + DPSTART);                                    
static float       **rfmxi= (void *)(0x0110f12c + DPSTART);                                     
static float       **rfmxmi= (void *)(0x0110f17c + DPSTART);                                    
static float       **rfmfrefi= (void *)(0x0110f1cc + DPSTART);                                  
static float       **rfmvrefi= (void *)(0x0110f21c + DPSTART);                                  
static long        *rfmbxln= (void *)(0x0110f26c + DPSTART);                                    
static long        *rfmbxlp= (void *)(0x0110f2bc + DPSTART);                                    
static long        *rfmcnt= (void *)(0x0110f30c + DPSTART);                                     
static long        *rfmmode= (void *)(0x0110f35c + DPSTART);                                    
static long        *rfmmodep= (void *)(0x0110f3ac + DPSTART);                                   
static long        *rfmtno= (void *)(0x0110f3fc + DPSTART);                                     
static char        *rfmactiv= (void *)(0x0110f44c + DPSTART);                                   
static char        *rfmbdact= (void *)(0x0110f460 + DPSTART);                                   
static char        *rfmtinit= (void *)(0x0110f474 + DPSTART);                                   
static char        *rfmbcmd= (void *)(0x0110f488 + DPSTART);                                    
static char        **rfmbmji= (void *)(0x0110f49c + DPSTART);                                   
static char        **rfmboni= (void *)(0x0110f4ec + DPSTART);                                   
static char        **rfmbbd1i= (void *)(0x0110f53c + DPSTART);                                  
static char        **rfmbbd2i= (void *)(0x0110f58c + DPSTART);                                  
static float       *GCTLBUF_RFS= (void *)(0x0110fdf0 + DPSTART);                                
static float       *rfsgfa= (void *)(0x0110fdf0 + DPSTART);                                     
static float       *rfscd= (void *)(0x0110fe40 + DPSTART);                                      
static float       *rfsfaero= (void *)(0x0110fe90 + DPSTART);                                   
static float       *rfsfd= (void *)(0x0110fee0 + DPSTART);                                      
static float       *rfsfda= (void *)(0x0110ff30 + DPSTART);                                     
static float       *rfsfl= (void *)(0x0110ff80 + DPSTART);                                      
static float       *rfsflmax= (void *)(0x0110ffd0 + DPSTART);                                   
static float       *rfsfln= (void *)(0x01110020 + DPSTART);                                     
static float       *rfsflp= (void *)(0x01110070 + DPSTART);                                     
static float       *rfsfs= (void *)(0x011100c0 + DPSTART);                                      
static float       *rfsfsa= (void *)(0x01110110 + DPSTART);                                     
static float       *rfsft= (void *)(0x01110160 + DPSTART);                                      
static float       *rfsgfdamp= (void *)(0x011101b0 + DPSTART);                                  
static float       *rfsgfgrad= (void *)(0x01110200 + DPSTART);                                  
static float       *rfsgfoff= (void *)(0x01110250 + DPSTART);                                   
static float       *rfsifd= (void *)(0x011102a0 + DPSTART);                                     
static float       *rfsifs= (void *)(0x011102f0 + DPSTART);                                     
static float       *rfsift= (void *)(0x01110340 + DPSTART);                                     
static float       *rfsk= (void *)(0x01110390 + DPSTART);                                       
static float       *rfsve= (void *)(0x011103e0 + DPSTART);                                      
static float       *rfsxe= (void *)(0x01110430 + DPSTART);                                      
static float       **rfscdi= (void *)(0x01110480 + DPSTART);                                    
static float       **rfsfgradi= (void *)(0x011104d0 + DPSTART);                                 
static float       **rfsfoffi= (void *)(0x01110520 + DPSTART);                                  
static float       **rfsqi= (void *)(0x01110570 + DPSTART);                                     
static float       **rfsv1i= (void *)(0x011105c0 + DPSTART);                                    
static float       **rfsv2i= (void *)(0x01110610 + DPSTART);                                    
static float       **rfsvsi= (void *)(0x01110660 + DPSTART);                                    
static float       **rfsx1i= (void *)(0x011106b0 + DPSTART);                                    
static float       **rfsx2i= (void *)(0x01110700 + DPSTART);                                    
static float       **rfsxsi= (void *)(0x01110750 + DPSTART);                                    
static float       *GCTLBUF_RGR= (void *)(0x01110d90 + DPSTART);                                
static float       *rgrgr= (void *)(0x01110d90 + DPSTART);                                      
static float       *rgrk= (void *)(0x01110de0 + DPSTART);                                       
static float       *rgrzo= (void *)(0x01110e30 + DPSTART);                                      
static float       **rgrxi= (void *)(0x01110e80 + DPSTART);                                     
static float       **rgrzi= (void *)(0x01110ed0 + DPSTART);                                     
static float       *GCTLBUF_RGS= (void *)(0x01110f84 + DPSTART);                                
static float       *rgscd= (void *)(0x01110f84 + DPSTART);                                      
static float       *rgsfd= (void *)(0x01110fd4 + DPSTART);                                      
static float       *rgsfs= (void *)(0x01111074 + DPSTART);                                      
static float       *rgsft= (void *)(0x01111114 + DPSTART);                                      
static float       *rgsifd= (void *)(0x011111b4 + DPSTART);                                     
static float       *rgsve= (void *)(0x01111254 + DPSTART);                                      
static float       *rgsxe= (void *)(0x011112f4 + DPSTART);                                      
static float       **rgsv1i= (void *)(0x01111394 + DPSTART);                                    
static float       **rgsv2i= (void *)(0x01111434 + DPSTART);                                    
static float       **rgsx1i= (void *)(0x011114d4 + DPSTART);                                    
static float       **rgsx2i= (void *)(0x01111574 + DPSTART);                                    
static float       *GCTLBUF_RHM= (void *)(0x01111b3c + DPSTART);                                
static float       *rhmc= (void *)(0x01111b3c + DPSTART);                                       
static float       *rhmcd= (void *)(0x01111bdc + DPSTART);                                      
static float       *rhmchsa= (void *)(0x01111c7c + DPSTART);                                    
static float       *rhmchsa1= (void *)(0x01111d1c + DPSTART);                                   
static float       *rhmchsa2= (void *)(0x01111dbc + DPSTART);                                   
static float       *rhmchscd= (void *)(0x01111e5c + DPSTART);                                   
static float       *rhmchsfd= (void *)(0x01111efc + DPSTART);                                   
static float       *rhmchsxa= (void *)(0x01111f9c + DPSTART);                                   
static float       *rhmchsxs= (void *)(0x0111203c + DPSTART);                                   
static float       *rhmfhm= (void *)(0x011120dc + DPSTART);                                     
static float       *rhmfmh= (void *)(0x0111217c + DPSTART);                                     
static float       *rhmfo= (void *)(0x0111221c + DPSTART);                                      
static float       *rhmgac= (void *)(0x011122bc + DPSTART);                                     
static float       *rhmgav= (void *)(0x0111235c + DPSTART);                                     
static float       *rhmgcf= (void *)(0x011123fc + DPSTART);                                     
static float       *rhmgh1= (void *)(0x0111249c + DPSTART);                                     
static float       *rhmgh2= (void *)(0x0111253c + DPSTART);                                     
static float       *rhmghd= (void *)(0x011125dc + DPSTART);                                     
static float       *rhmgho= (void *)(0x0111267c + DPSTART);                                     
static float       *rhmgma= (void *)(0x0111271c + DPSTART);                                     
static float       *rhmgps= (void *)(0x011127bc + DPSTART);                                     
static float       *rhmgsp= (void *)(0x0111285c + DPSTART);                                     
static float       *rhmgva= (void *)(0x011128fc + DPSTART);                                     
static float       *rhmgxa= (void *)(0x0111299c + DPSTART);                                     
static float       *rhmh1= (void *)(0x01112a3c + DPSTART);                                      
static float       *rhmh2= (void *)(0x01112adc + DPSTART);                                      
static float       *rhmhd= (void *)(0x01112b7c + DPSTART);                                      
static float       *rhmhms= (void *)(0x01112c1c + DPSTART);                                     
static float       *rhmho= (void *)(0x01112cbc + DPSTART);                                      
static float       *rhmhos= (void *)(0x01112d5c + DPSTART);                                     
static float       *rhmht= (void *)(0x01112dfc + DPSTART);                                      
static float       *rhmivs= (void *)(0x01112e9c + DPSTART);                                     
static float       *rhmixo= (void *)(0x01112f3c + DPSTART);                                     
static float       *rhmixs= (void *)(0x01112fdc + DPSTART);                                     
static float       *rhmk= (void *)(0x0111307c + DPSTART);                                       
static float       *rhms= (void *)(0x0111311c + DPSTART);                                       
static float       *rhmva= (void *)(0x011131bc + DPSTART);                                      
static float       *rhmvc= (void *)(0x0111325c + DPSTART);                                      
static float       *rhmvo= (void *)(0x011132fc + DPSTART);                                      
static float       *rhmvs= (void *)(0x0111339c + DPSTART);                                      
static float       *rhmx1= (void *)(0x0111343c + DPSTART);                                      
static float       *rhmxa= (void *)(0x011134dc + DPSTART);                                      
static float       *rhmxc= (void *)(0x0111357c + DPSTART);                                      
static float       *rhmxo= (void *)(0x0111361c + DPSTART);                                      
static float       *rhmxs= (void *)(0x011136bc + DPSTART);                                      
static float       **rhmfi= (void *)(0x0111375c + DPSTART);                                     
static float       **rhmgm2i= (void *)(0x011137fc + DPSTART);                                   
static float       **rhmk1i= (void *)(0x0111389c + DPSTART);                                    
static float       **rhmk2i= (void *)(0x0111393c + DPSTART);                                    
static float       **rhmqi= (void *)(0x011139dc + DPSTART);                                     
static float       **rhmvai= (void *)(0x01113a7c + DPSTART);                                    
static float       **rhmvci= (void *)(0x01113b1c + DPSTART);                                    
static float       **rhmvi= (void *)(0x01113bbc + DPSTART);                                     
static float       **rhmvtri= (void *)(0x01113c5c + DPSTART);                                   
static float       **rhmx1i= (void *)(0x01113cfc + DPSTART);                                    
static float       **rhmx2i= (void *)(0x01113d9c + DPSTART);                                    
static float       **rhmxai= (void *)(0x01113e3c + DPSTART);                                    
static float       **rhmxci= (void *)(0x01113edc + DPSTART);                                    
static float       *rhmichsxs= (void *)(0x01113f7c + DPSTART);                                  
static float       *rhmkchsxs= (void *)(0x0111401c + DPSTART);                                  
static float       *rhmichsxa= (void *)(0x011140bc + DPSTART);                                  
static float       *rhmkchsxa= (void *)(0x0111415c + DPSTART);                                  
static float       *rhmichsa1= (void *)(0x011141fc + DPSTART);                                  
static float       *rhmkchsa1= (void *)(0x0111429c + DPSTART);                                  
static float       *rhmichsa2= (void *)(0x0111433c + DPSTART);                                  
static float       *rhmkchsa2= (void *)(0x011143dc + DPSTART);                                  
static float       *rhmchmadd= (void *)(0x0111447c + DPSTART);                                  
static float       *rhmvtr= (void *)(0x0111451c + DPSTART);                                     
static float       *rhmvtrlim= (void *)(0x011145bc + DPSTART);                                  
static float       *GCTLBUF_RMR= (void *)(0x01114634 + DPSTART);                                
static float       *rmrdelt= (void *)(0x01114634 + DPSTART);                                    
static float       *rmrf1= (void *)(0x01114684 + DPSTART);                                      
static float       *rmrf2= (void *)(0x011146d4 + DPSTART);                                      
static float       *rmrf3= (void *)(0x01114724 + DPSTART);                                      
static float       *rmrf4= (void *)(0x01114774 + DPSTART);                                      
static float       *rmrfo= (void *)(0x011147c4 + DPSTART);                                      
static float       *rmrgf1= (void *)(0x01114814 + DPSTART);                                     
static float       *rmrgf2= (void *)(0x01114864 + DPSTART);                                     
static float       *rmrgf3= (void *)(0x011148b4 + DPSTART);                                     
static float       *rmrgf4= (void *)(0x01114904 + DPSTART);                                     
static float       *rmrv1= (void *)(0x01114954 + DPSTART);                                      
static float       *rmrv2= (void *)(0x011149a4 + DPSTART);                                      
static float       *rmrv3= (void *)(0x011149f4 + DPSTART);                                      
static float       *rmrv4= (void *)(0x01114a44 + DPSTART);                                      
static float       *rmrvo= (void *)(0x01114a94 + DPSTART);                                      
static float       *rmrx1= (void *)(0x01114ae4 + DPSTART);                                      
static float       *rmrx2= (void *)(0x01114b34 + DPSTART);                                      
static float       *rmrx3= (void *)(0x01114b84 + DPSTART);                                      
static float       *rmrx4= (void *)(0x01114bd4 + DPSTART);                                      
static float       *rmrxo= (void *)(0x01114c24 + DPSTART);                                      
static float       *rmrxon1= (void *)(0x01114c74 + DPSTART);                                    
static float       **rmrf1i= (void *)(0x01114cc4 + DPSTART);                                    
static float       **rmrf2i= (void *)(0x01114d14 + DPSTART);                                    
static float       **rmrf3i= (void *)(0x01114d64 + DPSTART);                                    
static float       **rmrf4i= (void *)(0x01114db4 + DPSTART);                                    
static float       **rmrx1i= (void *)(0x01114e04 + DPSTART);                                    
static float       **rmrx2i= (void *)(0x01114e54 + DPSTART);                                    
static float       **rmrx3i= (void *)(0x01114ea4 + DPSTART);                                    
static float       **rmrx4i= (void *)(0x01114ef4 + DPSTART);                                    
static float       *GCTLBUF_RPS= (void *)(0x011155d4 + DPSTART);                                
static float       *rpsao= (void *)(0x011155d4 + DPSTART);                                      
static float       *rpsaolim= (void *)(0x011155dc + DPSTART);                                   
static float       *rpsgxl= (void *)(0x011155e4 + DPSTART);                                     
static float       *rpsgxlgn= (void *)(0x011155ec + DPSTART);                                   
static float       *rpsgxlgp= (void *)(0x011155f4 + DPSTART);                                   
static float       *rpsirate= (void *)(0x011155fc + DPSTART);                                   
static float       *rpsitime= (void *)(0x01115604 + DPSTART);                                   
static float       *rpsscmd= (void *)(0x0111560c + DPSTART);                                    
static float       *rpsski= (void *)(0x01115614 + DPSTART);                                     
static float       *rpsskpn= (void *)(0x0111561c + DPSTART);                                    
static float       *rpsskpp= (void *)(0x01115624 + DPSTART);                                    
static float       *rpssnull= (void *)(0x0111562c + DPSTART);                                   
static float       *rpsvc= (void *)(0x01115634 + DPSTART);                                      
static float       *rpsVc_f= (void *)(0x0111563c + DPSTART);                                    
static float       *rpsvclim= (void *)(0x0111566c + DPSTART);                                   
static float       *rpsve= (void *)(0x01115674 + DPSTART);                                      
static float       *rpsvlg= (void *)(0x0111567c + DPSTART);                                     
static float       *rpsxaio= (void *)(0x01115684 + DPSTART);                                    
static float       *rpsxc= (void *)(0x0111568c + DPSTART);                                      
static float       *rpsXc_f= (void *)(0x01115694 + DPSTART);                                    
static float       *rpsxce= (void *)(0x011156c4 + DPSTART);                                     
static float       *rpsxcmd= (void *)(0x011156cc + DPSTART);                                    
static float       *rpsxcn1= (void *)(0x011156d4 + DPSTART);                                    
static float       *rpsxe= (void *)(0x011156dc + DPSTART);                                      
static float       *rpsxelim= (void *)(0x011156e4 + DPSTART);                                   
static float       *rpsxl= (void *)(0x011156ec + DPSTART);                                      
static float       *rpsxlg= (void *)(0x011156f4 + DPSTART);                                     
static float       *rpsxlgn1= (void *)(0x011156fc + DPSTART);                                   
static float       *rpsxlgo= (void *)(0x01115704 + DPSTART);                                    
static float       *rpsxslew= (void *)(0x0111570c + DPSTART);                                   
static float       **rpsxai= (void *)(0x01115714 + DPSTART);                                    
static float       **rpsxmi= (void *)(0x0111571c + DPSTART);                                    
static long        *rpsabort= (void *)(0x01115724 + DPSTART);                                   
static long        *rpsaovrd= (void *)(0x0111572c + DPSTART);                                   
static long        *rpsVc_ft= (void *)(0x01115734 + DPSTART);                                   
static long        *rpsXc_ft= (void *)(0x0111573c + DPSTART);                                   
static char        *rpsrun= (void *)(0x01115744 + DPSTART);                                     
static char        *rpsstart= (void *)(0x01115746 + DPSTART);                                   
static float       *GCTLBUF_RSA= (void *)(0x011157c8 + DPSTART);                                
static float       *rsaiv2= (void *)(0x011157c8 + DPSTART);                                     
static float       *rsaix2= (void *)(0x01115868 + DPSTART);                                     
static float       *rsax2ln= (void *)(0x01115908 + DPSTART);                                    
static float       *rsax2lp= (void *)(0x011159a8 + DPSTART);                                    
static float       *rsaan1= (void *)(0x01115a48 + DPSTART);                                     
static float       *rsaaplk= (void *)(0x01115ae8 + DPSTART);                                    
static float       *rsacd= (void *)(0x01115b88 + DPSTART);                                      
static float       *rsacdsa= (void *)(0x01115c28 + DPSTART);                                    
static float       *rsacv= (void *)(0x01115cc8 + DPSTART);                                      
static float       *rsadelt= (void *)(0x01115d68 + DPSTART);                                    
static float       *rsafd= (void *)(0x01115e08 + DPSTART);                                      
static float       *rsafhm= (void *)(0x01115ea8 + DPSTART);                                     
static float       *rsaflk= (void *)(0x01115f48 + DPSTART);                                     
static float       *rsafsv= (void *)(0x01115fe8 + DPSTART);                                     
static float       *rsaft= (void *)(0x01116088 + DPSTART);                                      
static float       *rsagap= (void *)(0x01116128 + DPSTART);                                     
static float       *rsagfhm= (void *)(0x011161c8 + DPSTART);                                    
static float       *rsaghm= (void *)(0x01116268 + DPSTART);                                     
static float       *rsaghy= (void *)(0x01116308 + DPSTART);                                     
static float       *rsaghyd= (void *)(0x011163a8 + DPSTART);                                    
static float       *rsaglk= (void *)(0x01116448 + DPSTART);                                     
static float       *rsagm= (void *)(0x011164e8 + DPSTART);                                      
static float       *rsagpd= (void *)(0x01116588 + DPSTART);                                     
static float       *rsags= (void *)(0x01116628 + DPSTART);                                      
static float       *rsagv= (void *)(0x011166c8 + DPSTART);                                      
static float       *rsaivp= (void *)(0x01116768 + DPSTART);                                     
static float       *rsaivpn1= (void *)(0x01116808 + DPSTART);                                   
static float       *rsaivpv= (void *)(0x011168a8 + DPSTART);                                    
static float       *rsaivpvl= (void *)(0x01116948 + DPSTART);                                   
static float       *rsaixp= (void *)(0x011169e8 + DPSTART);                                     
static float       *rsaixv= (void *)(0x01116a88 + DPSTART);                                     
static float       *rsapd= (void *)(0x01116b28 + DPSTART);                                      
static float       *rsaphm= (void *)(0x01116bc8 + DPSTART);                                     
static float       *rsaphy= (void *)(0x01116c68 + DPSTART);                                     
static float       *rsapl= (void *)(0x01116d08 + DPSTART);                                      
static float       *rsapv= (void *)(0x01116da8 + DPSTART);                                      
static float       *rsaqpf= (void *)(0x01116e48 + DPSTART);                                     
static float       *rsaqv= (void *)(0x01116ee8 + DPSTART);                                      
static float       *rsavp= (void *)(0x01116f88 + DPSTART);                                      
static float       *rsavplk= (void *)(0x01117028 + DPSTART);                                    
static float       *rsavplkl= (void *)(0x011170c8 + DPSTART);                                   
static float       *rsavpn1= (void *)(0x01117168 + DPSTART);                                    
static float       *rsavpv= (void *)(0x01117208 + DPSTART);                                     
static float       *rsavpvl= (void *)(0x011172a8 + DPSTART);                                    
static float       *rsavs= (void *)(0x01117348 + DPSTART);                                      
static float       *rsaxe= (void *)(0x011173e8 + DPSTART);                                      
static float       *rsaxp= (void *)(0x01117488 + DPSTART);                                      
static float       *rsaxpln= (void *)(0x01117528 + DPSTART);                                    
static float       *rsaxplp= (void *)(0x011175c8 + DPSTART);                                    
static float       *rsaxs= (void *)(0x01117668 + DPSTART);                                      
static float       *rsaxsn1= (void *)(0x01117708 + DPSTART);                                    
static float       *rsaxsp= (void *)(0x011177a8 + DPSTART);                                     
static float       *rsaxv= (void *)(0x01117848 + DPSTART);                                      
static float       *rsaxvln= (void *)(0x011178e8 + DPSTART);                                    
static float       *rsaxvlp= (void *)(0x01117988 + DPSTART);                                    
static float       *rsaaf= (void *)(0x01117a28 + DPSTART);                                      
static float       *rsaafn1= (void *)(0x01117ac8 + DPSTART);                                    
static float       *rsaam= (void *)(0x01117b68 + DPSTART);                                      
static float       *rsaamn1= (void *)(0x01117c08 + DPSTART);                                    
static float       *rsacdord= (void *)(0x01117ca8 + DPSTART);                                   
static float       *rsacdv1= (void *)(0x01117d48 + DPSTART);                                    
static float       *rsacdv2= (void *)(0x01117de8 + DPSTART);                                    
static float       *rsacdv3= (void *)(0x01117e88 + DPSTART);                                    
static float       *rsafdv1= (void *)(0x01117f28 + DPSTART);                                    
static float       *rsafdv2= (void *)(0x01117fc8 + DPSTART);                                    
static float       *rsafdv3= (void *)(0x01118068 + DPSTART);                                    
static float       *rsaffv= (void *)(0x01118108 + DPSTART);                                     
static float       *rsafhy= (void *)(0x011181a8 + DPSTART);                                     
static float       *rsafsv1= (void *)(0x01118248 + DPSTART);                                    
static float       *rsafsv2= (void *)(0x011182e8 + DPSTART);                                    
static float       *rsafsv3= (void *)(0x01118388 + DPSTART);                                    
static float       *rsafv= (void *)(0x01118428 + DPSTART);                                      
static float       *rsafv1= (void *)(0x011184c8 + DPSTART);                                     
static float       *rsafv2= (void *)(0x01118568 + DPSTART);                                     
static float       *rsafv3= (void *)(0x01118608 + DPSTART);                                     
static float       *rsagapn= (void *)(0x011186a8 + DPSTART);                                    
static float       *rsagapp= (void *)(0x01118748 + DPSTART);                                    
static float       *rsagapr= (void *)(0x011187e8 + DPSTART);                                    
static float       *rsagfsv= (void *)(0x01118888 + DPSTART);                                    
static float       *rsaghy1= (void *)(0x01118928 + DPSTART);                                    
static float       *rsaghy2= (void *)(0x011189c8 + DPSTART);                                    
static float       *rsaghy3= (void *)(0x01118a68 + DPSTART);                                    
static float       *rsaghyv= (void *)(0x01118b08 + DPSTART);                                    
static float       *rsagnor= (void *)(0x01118ba8 + DPSTART);                                    
static float       *rsagnum= (void *)(0x01118c48 + DPSTART);                                    
static float       *rsagphy1= (void *)(0x01118ce8 + DPSTART);                                   
static float       *rsagphy2= (void *)(0x01118d88 + DPSTART);                                   
static float       *rsagphy3= (void *)(0x01118e28 + DPSTART);                                   
static float       *rsagstf= (void *)(0x01118ec8 + DPSTART);                                    
static float       *rsagv1= (void *)(0x01118f68 + DPSTART);                                     
static float       *rsagv2= (void *)(0x01119008 + DPSTART);                                     
static float       *rsagx1= (void *)(0x011190a8 + DPSTART);                                     
static float       *rsagx2= (void *)(0x01119148 + DPSTART);                                     
static float       *rsaorder= (void *)(0x011191e8 + DPSTART);                                   
static float       *rsapdsq= (void *)(0x01119288 + DPSTART);                                    
static float       *rsaphy1= (void *)(0x01119328 + DPSTART);                                    
static float       *rsaphy2= (void *)(0x011193c8 + DPSTART);                                    
static float       *rsaphy3= (void *)(0x01119468 + DPSTART);                                    
static float       *rsaphyt= (void *)(0x01119508 + DPSTART);                                    
static float       *rsaplk= (void *)(0x011195a8 + DPSTART);                                     
static float       *rsapthld= (void *)(0x01119648 + DPSTART);                                   
static float       *rsasvj1= (void *)(0x011196e8 + DPSTART);                                    
static float       *rsasvj2= (void *)(0x01119788 + DPSTART);                                    
static float       *rsasvj3= (void *)(0x01119828 + DPSTART);                                    
static float       *rsav1= (void *)(0x011198c8 + DPSTART);                                      
static float       *rsav2= (void *)(0x01119968 + DPSTART);                                      
static float       *rsavcmd= (void *)(0x01119a08 + DPSTART);                                    
static float       *rsavcr= (void *)(0x01119aa8 + DPSTART);                                     
static float       *rsavf= (void *)(0x01119b48 + DPSTART);                                      
static float       *rsavleak= (void *)(0x01119be8 + DPSTART);                                   
static float       *rsavpl= (void *)(0x01119c88 + DPSTART);                                     
static float       *rsavr= (void *)(0x01119d28 + DPSTART);                                      
static float       *rsavv= (void *)(0x01119dc8 + DPSTART);                                      
static float       *rsavv1= (void *)(0x01119e68 + DPSTART);                                     
static float       *rsavv2= (void *)(0x01119f08 + DPSTART);                                     
static float       *rsavv3= (void *)(0x01119fa8 + DPSTART);                                     
static float       *rsax1= (void *)(0x0111a048 + DPSTART);                                      
static float       *rsax2= (void *)(0x0111a0e8 + DPSTART);                                      
static float       *rsaxv1= (void *)(0x0111a188 + DPSTART);                                     
static float       *rsaxv1jam= (void *)(0x0111a228 + DPSTART);                                  
static float       *rsaxv2= (void *)(0x0111a2c8 + DPSTART);                                     
static float       *rsaxv2jam= (void *)(0x0111a368 + DPSTART);                                  
static float       *rsaxv3= (void *)(0x0111a408 + DPSTART);                                     
static float       *rsaxv3jam= (void *)(0x0111a4a8 + DPSTART);                                  
static float       **rsafhmi= (void *)(0x0111a548 + DPSTART);                                   
static float       **rsaflki= (void *)(0x0111a5e8 + DPSTART);                                   
static float       **rsaphmi= (void *)(0x0111a688 + DPSTART);                                   
static float       **rsaphy1i= (void *)(0x0111a728 + DPSTART);                                  
static float       **rsaphy2i= (void *)(0x0111a7c8 + DPSTART);                                  
static float       **rsaphy3i= (void *)(0x0111a868 + DPSTART);                                  
static float       **rsaphyi= (void *)(0x0111a908 + DPSTART);                                   
static float       **rsav1i= (void *)(0x0111a9a8 + DPSTART);                                    
static float       **rsav2i= (void *)(0x0111aa48 + DPSTART);                                    
static float       **rsavpfi= (void *)(0x0111aae8 + DPSTART);                                   
static float       **rsax1i= (void *)(0x0111ab88 + DPSTART);                                    
static float       **rsax2i= (void *)(0x0111ac28 + DPSTART);                                    
static float       **rsaxsci= (void *)(0x0111acc8 + DPSTART);                                   
static float       **rsaxscoi= (void *)(0x0111ad68 + DPSTART);                                  
static long        *rsaidxc= (void *)(0x0111ae08 + DPSTART);                                    
static char        *rsabcmd= (void *)(0x0111aea8 + DPSTART);                                    
static char        **rsabckvi= (void *)(0x0111aed0 + DPSTART);                                  
static char        **rsabpfi= (void *)(0x0111af70 + DPSTART);                                   
static char        **rsabpji= (void *)(0x0111b010 + DPSTART);                                   
static char        **rsabvj1i= (void *)(0x0111b0b0 + DPSTART);                                  
static char        **rsabvj2i= (void *)(0x0111b150 + DPSTART);                                  
static char        **rsabvj3i= (void *)(0x0111b1f0 + DPSTART);                                  
static char        **rsabvji= (void *)(0x0111b290 + DPSTART);                                   
static float       *GCTLBUF_RTA= (void *)(0x0111bd58 + DPSTART);                                
static float       *rtaatm= (void *)(0x0111bd58 + DPSTART);                                     
static float       *rtabfor= (void *)(0x0111bda8 + DPSTART);                                    
static float       *rtafor= (void *)(0x0111bdf8 + DPSTART);                                     
static float       *rtagm= (void *)(0x0111be48 + DPSTART);                                      
static float       *rtavtc= (void *)(0x0111be98 + DPSTART);                                     
static float       *rtavtln= (void *)(0x0111bee8 + DPSTART);                                    
static float       *rtavtlp= (void *)(0x0111bf38 + DPSTART);                                    
static float       *rtavtm= (void *)(0x0111bf88 + DPSTART);                                     
static float       *rtaxtc= (void *)(0x0111bfd8 + DPSTART);                                     
static float       *rtaxtil= (void *)(0x0111c028 + DPSTART);                                    
static float       *rtacd= (void *)(0x0111c078 + DPSTART);                                      
static float       *rtadelt= (void *)(0x0111c0c8 + DPSTART);                                    
static float       *rtafd= (void *)(0x0111c118 + DPSTART);                                      
static float       *rtafs= (void *)(0x0111c168 + DPSTART);                                      
static float       *rtaft= (void *)(0x0111c1b8 + DPSTART);                                      
static float       *rtagerr= (void *)(0x0111c208 + DPSTART);                                    
static float       *rtak= (void *)(0x0111c258 + DPSTART);                                       
static float       *rtata= (void *)(0x0111c2a8 + DPSTART);                                      
static float       *rtave= (void *)(0x0111c2f8 + DPSTART);                                      
static float       *rtavt= (void *)(0x0111c348 + DPSTART);                                      
static float       *rtavxin= (void *)(0x0111c398 + DPSTART);                                    
static float       *rtaxc= (void *)(0x0111c3e8 + DPSTART);                                      
static float       *rtaxe= (void *)(0x0111c438 + DPSTART);                                      
static float       *rtaxt= (void *)(0x0111c488 + DPSTART);                                      
static float       *rtaxtsn= (void *)(0x0111c4d8 + DPSTART);                                    
static float       *rtaxtsp= (void *)(0x0111c528 + DPSTART);                                    
static float       **rtattmi= (void *)(0x0111c578 + DPSTART);                                   
static float       **rtaxti= (void *)(0x0111c5c8 + DPSTART);                                    
static float       **rtavi= (void *)(0x0111c618 + DPSTART);                                     
static float       **rtavti= (void *)(0x0111c668 + DPSTART);                                    
static float       **rtaxci= (void *)(0x0111c6b8 + DPSTART);                                    
static float       **rtaxfi= (void *)(0x0111c708 + DPSTART);                                    
static float       **rtaxi= (void *)(0x0111c758 + DPSTART);                                     
static char        **rtabtri= (void *)(0x0111c7a8 + DPSTART);                                   
static char        **rtatrli= (void *)(0x0111c7f8 + DPSTART);                                   
static float       *GCTLBUF_TABLES= (void *)(0x0111ccf8 + DPSTART);                             
static long        *TCAGFLG= (void *)(0x0111ccf8 + DPSTART);                                    
static long        *TCAPCMD= (void *)(0x0111cd48 + DPSTART);                                    
static long        *TCAPCWS= (void *)(0x0111cd98 + DPSTART);                                    
static long        *TCAXLGO= (void *)(0x0111cde8 + DPSTART);                                    
static long        *TCCDV= (void *)(0x0111ce38 + DPSTART);                                      
static long        *TCEMU= (void *)(0x0111ce88 + DPSTART);                                      
static long        *TCFFFV= (void *)(0x0111ce8c + DPSTART);                                     
static long        *TCFFKV= (void *)(0x0111cedc + DPSTART);                                     
static long        *TCFFV= (void *)(0x0111cf2c + DPSTART);                                      
static long        *TCFGFD= (void *)(0x0111cf7c + DPSTART);                                     
static long        *TCFGFS= (void *)(0x0111cfcc + DPSTART);                                     
static long        *TCFMFFLK= (void *)(0x0111d01c + DPSTART);                                   
static long        *TCFMGFK= (void *)(0x0111d06c + DPSTART);                                    
static long        *TCFMGFKR= (void *)(0x0111d0bc + DPSTART);                                   
static long        *TCFMGXC= (void *)(0x0111d10c + DPSTART);                                    
static long        *TCFMUBC= (void *)(0x0111d15c + DPSTART);                                    
static long        *TCFMXSTR= (void *)(0x0111d1ac + DPSTART);                                   
static long        *TCFSCD= (void *)(0x0111d1fc + DPSTART);                                     
static long        *TCFSFD= (void *)(0x0111d24c + DPSTART);                                     
static long        *TCFSFL= (void *)(0x0111d29c + DPSTART);                                     
static long        *TCFSFS= (void *)(0x0111d2ec + DPSTART);                                     
static long        *TCGFFKR= (void *)(0x0111d33c + DPSTART);                                    
static long        *TCGFK= (void *)(0x0111d38c + DPSTART);                                      
static long        *TCGFKR= (void *)(0x0111d3dc + DPSTART);                                     
static long        *TCGSCD= (void *)(0x0111d42c + DPSTART);                                     
static long        *TCGSFD= (void *)(0x0111d47c + DPSTART);                                     
static long        *TCGSFS= (void *)(0x0111d51c + DPSTART);                                     
static long        *TCHMCHSA1= (void *)(0x0111d5bc + DPSTART);                                  
static long        *TCHMCHSA2= (void *)(0x0111d65c + DPSTART);                                  
static long        *TCHMCHSCD= (void *)(0x0111d6fc + DPSTART);                                  
static long        *TCHMCHSXA= (void *)(0x0111d79c + DPSTART);                                  
static long        *TCHMCHSXS= (void *)(0x0111d83c + DPSTART);                                  
static long        *TCHMFHM= (void *)(0x0111d8dc + DPSTART);                                    
static long        *TCHMIXO= (void *)(0x0111d97c + DPSTART);                                    
static long        *TCHMK= (void *)(0x0111da1c + DPSTART);                                      
static long        *TCHMXS= (void *)(0x0111dabc + DPSTART);                                     
static long        *TCMU= (void *)(0x0111db5c + DPSTART);                                       
static long        *TCNPTXCD= (void *)(0x0111db74 + DPSTART);                                   
static long        *TCOAILG= (void *)(0x0111db78 + DPSTART);                                    
static long        *TCRCGAIN= (void *)(0x0111db7c + DPSTART);                                   
static long        *TCRFMGM= (void *)(0x0111db80 + DPSTART);                                    
static long        *TCRMRF1= (void *)(0x0111dbd0 + DPSTART);                                    
static long        *TCRMRF2= (void *)(0x0111dc20 + DPSTART);                                    
static long        *TCRMRF3= (void *)(0x0111dc70 + DPSTART);                                    
static long        *TCRMRF4= (void *)(0x0111dcc0 + DPSTART);                                    
static long        *TCRMRX1= (void *)(0x0111dd10 + DPSTART);                                    
static long        *TCRMRX2= (void *)(0x0111dd60 + DPSTART);                                    
static long        *TCRMRX3= (void *)(0x0111ddb0 + DPSTART);                                    
static long        *TCRMRX4= (void *)(0x0111de00 + DPSTART);                                    
static long        *TCRPXL= (void *)(0x0111de50 + DPSTART);                                     
static long        *TCRPXLB= (void *)(0x0111de54 + DPSTART);                                    
static long        *TCSACD= (void *)(0x0111de58 + DPSTART);                                     
static long        *TCSACDV1= (void *)(0x0111def8 + DPSTART);                                   
static long        *TCSACDV2= (void *)(0x0111df98 + DPSTART);                                   
static long        *TCSACDV3= (void *)(0x0111e038 + DPSTART);                                   
static long        *TCSACV= (void *)(0x0111e0d8 + DPSTART);                                     
static long        *TCSAFFV= (void *)(0x0111e178 + DPSTART);                                    
static long        *TCSAFSV1= (void *)(0x0111e218 + DPSTART);                                   
static long        *TCSAFSV2= (void *)(0x0111e2b8 + DPSTART);                                   
static long        *TCSAFSV3= (void *)(0x0111e358 + DPSTART);                                   
static long        *TCSAVPVL= (void *)(0x0111e3f8 + DPSTART);                                   
static long        *TCSAXS= (void *)(0x0111e3fc + DPSTART);                                     
static long        *TCSF1POS= (void *)(0x0111e49c + DPSTART);                                   
static long        *TCSFFRC= (void *)(0x0111e4ec + DPSTART);                                    
static long        *TCSFPOS= (void *)(0x0111e53c + DPSTART);                                    
static long        *TCTACD= (void *)(0x0111e58c + DPSTART);                                     
static long        *TCTAFS= (void *)(0x0111e5dc + DPSTART);                                     
static long        *TCTAXC= (void *)(0x0111e62c + DPSTART);                                     
static long        *TCVAPCU= (void *)(0x0111e67c + DPSTART);                                    
static long        *TCVEPCU= (void *)(0x0111e680 + DPSTART);                                    
static long        *TCVRPCU= (void *)(0x0111e684 + DPSTART);                                    
static long        *TCHMKCHSXS= (void *)(0x0111e688 + DPSTART);                                 
static long        *TCHMKCHSXA= (void *)(0x0111e728 + DPSTART);                                 
static long        *TCHMKCHSA1= (void *)(0x0111e7c8 + DPSTART);                                 
static long        *TCHMKCHSA2= (void *)(0x0111e868 + DPSTART);                                 
static long        *TCFMCDV= (void *)(0x0111e908 + DPSTART);                                    
static long        *TCAMGM= (void *)(0x0111e958 + DPSTART);                                     
static double      *DZDRTSK= (void *)(0x0111ec38 + DPSTART);                                    
static double      *rtexec_timing= (void *)(0x0111ec38 + DPSTART);                              
static double      *DZDRTSKMAX= (void *)(0x0111ec88 + DPSTART);                                 
static double      *DZDRTSKMIN= (void *)(0x0111ecd8 + DPSTART);                                 
static double      *DZRTSKTM= (void *)(0x0111ed28 + DPSTART);                                   
static double      *DZRTSKTMMAX= (void *)(0x0111ed78 + DPSTART);                                
static double      *DZRTSKTMMIN= (void *)(0x0111edc8 + DPSTART);                                
static char        *LZRTSKTM= (void *)(0x0111ee18 + DPSTART);                                   
static float       *CTLBUF= (void *)(0x0111f400 + DPSTART);                                     
static float       *drccount= (void *)(0x0111f400 + DPSTART);                                   
static float       *RAMF2A= (void *)(0x0111f404 + DPSTART);                                     
static float       *RAMF2F= (void *)(0x0111f454 + DPSTART);                                     
static float       *RAMF3A= (void *)(0x0111f4a4 + DPSTART);                                     
static float       *RAMF4A= (void *)(0x0111f4f4 + DPSTART);                                     
static float       *RAMF5A= (void *)(0x0111f544 + DPSTART);                                     
static float       *RAPF1= (void *)(0x0111f594 + DPSTART);                                      
static float       *RAPF2= (void *)(0x0111f5e4 + DPSTART);                                      
static float       *RAPX1= (void *)(0x0111f634 + DPSTART);                                      
static float       *RAPX2= (void *)(0x0111f684 + DPSTART);                                      
static float       *RBDFATG= (void *)(0x0111f6d4 + DPSTART);                                    
static float       *RBDXATG= (void *)(0x0111f724 + DPSTART);                                    
static float       *rbrkcdn= (void *)(0x0111f774 + DPSTART);                                    
static float       *rbrkcdp= (void *)(0x0111f77c + DPSTART);                                    
static float       *RC1POS= (void *)(0x0111f784 + DPSTART);                                     
static float       *RCACTBDD= (void *)(0x0111f788 + DPSTART);                                   
static float       *RCACTBDU= (void *)(0x0111f828 + DPSTART);                                   
static float       *RCACTVL= (void *)(0x0111f8c8 + DPSTART);                                    
static float       *RCAGBOB= (void *)(0x0111f968 + DPSTART);                                    
static float       *RCAHYD= (void *)(0x0111f978 + DPSTART);                                     
static float       *RCAILTG= (void *)(0x0111f97c + DPSTART);                                    
static float       *RCAILTX= (void *)(0x0111f980 + DPSTART);                                    
static float       *RCALTGF= (void *)(0x0111f984 + DPSTART);                                    
static float       *RCAPCENG= (void *)(0x0111f988 + DPSTART);                                   
static float       *RCAPCMDC= (void *)(0x0111f98c + DPSTART);                                   
static float       *RCAPCWSC= (void *)(0x0111f9dc + DPSTART);                                   
static float       *RCAPCWST= (void *)(0x0111fa2c + DPSTART);                                   
static float       *RCAPDTG= (void *)(0x0111fa7c + DPSTART);                                    
static float       *RCAPFCWS= (void *)(0x0111facc + DPSTART);                                   
static float       *RCAPFOR= (void *)(0x0111fb1c + DPSTART);                                    
static float       *RCAPK= (void *)(0x0111fb6c + DPSTART);                                      
static float       *RCAPKCWS= (void *)(0x0111fbbc + DPSTART);                                   
static float       *RCAPKG= (void *)(0x0111fc0c + DPSTART);                                     
static float       *RCAPKXE= (void *)(0x0111fc5c + DPSTART);                                    
static float       *RCAPLCMD= (void *)(0x0111fcac + DPSTART);                                   
static float       *RCAPMG= (void *)(0x0111fcb0 + DPSTART);                                     
static float       *RCAPPCMD= (void *)(0x0111fd00 + DPSTART);                                   
static float       *RCAPPLG= (void *)(0x0111fd04 + DPSTART);                                    
static float       *RCAPPUG= (void *)(0x0111fd08 + DPSTART);                                    
static float       *RCAPPVMX= (void *)(0x0111fd0c + DPSTART);                                   
static float       *RCAPRCMD= (void *)(0x0111fd10 + DPSTART);                                   
static float       *RCAPRVMX= (void *)(0x0111fd14 + DPSTART);                                   
static float       *RCAPUCMD= (void *)(0x0111fd18 + DPSTART);                                   
static float       *RCAPVMX= (void *)(0x0111fd1c + DPSTART);                                    
static float       *RCAPYCMD= (void *)(0x0111fd6c + DPSTART);                                   
static float       *RCAPYVMX= (void *)(0x0111fd70 + DPSTART);                                   
static float       *RCAXA= (void *)(0x0111fd74 + DPSTART);                                      
static float       *RCAZA= (void *)(0x0111fd78 + DPSTART);                                      
static float       *RCBOOST= (void *)(0x0111fd7c + DPSTART);                                    
static float       *RCBUFFET= (void *)(0x0111fd80 + DPSTART);                                   
static float       *RCCOLTRM= (void *)(0x0111fd98 + DPSTART);                                   
static float       *RCCWSPF= (void *)(0x0111fd9c + DPSTART);                                    
static float       *RCCWSRF= (void *)(0x0111fda0 + DPSTART);                                    
static float       *RCDEMLATX= (void *)(0x0111fda4 + DPSTART);                                  
static float       *RCDEMLG= (void *)(0x0111fda8 + DPSTART);                                    
static float       *RCDEMLTB= (void *)(0x0111fdac + DPSTART);                                   
static float       *RCDEMNG= (void *)(0x0111fdb0 + DPSTART);                                    
static float       *RCDEMNW= (void *)(0x0111fdb4 + DPSTART);                                    
static float       *RCDEMPDL= (void *)(0x0111fdb8 + DPSTART);                                   
static float       *RCDEMRG= (void *)(0x0111fdbc + DPSTART);                                    
static float       *RCDEMRTB= (void *)(0x0111fdc0 + DPSTART);                                   
static float       *RCDEMSTK= (void *)(0x0111fdc4 + DPSTART);                                   
static float       *RCDEMWHL= (void *)(0x0111fdc8 + DPSTART);                                   
static float       *RCEALP= (void *)(0x0111fdcc + DPSTART);                                     
static float       *RCELFIAS= (void *)(0x0111fdd0 + DPSTART);                                   
static float       *RCEXFHM= (void *)(0x0111fdd4 + DPSTART);                                    
static float       *RCEXGSV= (void *)(0x0111fe74 + DPSTART);                                    
static float       *RCEXGSX= (void *)(0x0111fec4 + DPSTART);                                    
static float       *RCEXPHM= (void *)(0x0111ff14 + DPSTART);                                    
static float       *RCEXPHY= (void *)(0x0111ffb4 + DPSTART);                                    
static float       *RCEXQIN= (void *)(0x01120054 + DPSTART);                                    
static float       *RCEXTRIMN= (void *)(0x01120058 + DPSTART);                                  
static float       *RCFABOB= (void *)(0x011200a8 + DPSTART);                                    
static float       *RCFBOB= (void *)(0x011200b8 + DPSTART);                                     
static float       *RCFEELP= (void *)(0x011200e0 + DPSTART);                                    
static float       *RCFELPL= (void *)(0x011200e4 + DPSTART);                                    
static float       *RCFELPR= (void *)(0x011200e8 + DPSTART);                                    
static float       *RCFEMU= (void *)(0x011200ec + DPSTART);                                     
static float       *RCFETMU= (void *)(0x011200f0 + DPSTART);                                    
static float       *RCFFBOB= (void *)(0x011200f4 + DPSTART);                                    
static float       *RCFGBOB= (void *)(0x01120104 + DPSTART);                                    
static float       *RCFMXGE= (void *)(0x01120114 + DPSTART);                                    
static float       *RCFMXGET= (void *)(0x01120118 + DPSTART);                                   
static float       *RCFOFF= (void *)(0x0112011c + DPSTART);                                     
static float       *RCFSVRUD= (void *)(0x0112016c + DPSTART);                                   
static float       *RCGDTQDB= (void *)(0x01120170 + DPSTART);                                   
static float       *RCGFBOBC= (void *)(0x01120174 + DPSTART);                                   
static float       *RCGNDTQ= (void *)(0x011201c4 + DPSTART);                                    
static float       *RCGNDTQG= (void *)(0x011201c8 + DPSTART);                                   
static float       *RCHPTC= (void *)(0x011201cc + DPSTART);                                     
static float       *RCHPTL= (void *)(0x011201d0 + DPSTART);                                     
static float       *RCHPTR= (void *)(0x011201d4 + DPSTART);                                     
static float       *RCHPWC= (void *)(0x011201d8 + DPSTART);                                     
static float       *RCHPWL= (void *)(0x011201dc + DPSTART);                                     
static float       *RCHPWR= (void *)(0x011201e0 + DPSTART);                                     
static float       *RCIAGN= (void *)(0x011201e4 + DPSTART);                                     
static float       *RCIAGP= (void *)(0x011201e8 + DPSTART);                                     
static float       *RCIAL= (void *)(0x011201ec + DPSTART);                                      
static float       *RCIEGN= (void *)(0x011201f0 + DPSTART);                                     
static float       *RCIEGP= (void *)(0x011201f4 + DPSTART);                                     
static float       *RCIELMAX= (void *)(0x011201f8 + DPSTART);                                   
static float       *RCKBOOST= (void *)(0x011201fc + DPSTART);                                   
static float       *RCLAHYD= (void *)(0x01120200 + DPSTART);                                    
static float       *RCLIAILV= (void *)(0x01120204 + DPSTART);                                   
static float       *RCLIAILX= (void *)(0x01120208 + DPSTART);                                   
static float       *RCLOAILV= (void *)(0x0112020c + DPSTART);                                   
static float       *RCLOAILX= (void *)(0x01120210 + DPSTART);                                   
static float       *RCLRGN= (void *)(0x01120214 + DPSTART);                                     
static float       *RCLRGP= (void *)(0x01120218 + DPSTART);                                     
static float       *RCNFC= (void *)(0x0112021c + DPSTART);                                      
static float       *RCNFCDB= (void *)(0x01120220 + DPSTART);                                    
static float       *RCNFCDTG= (void *)(0x01120224 + DPSTART);                                   
static float       *RCNFCL= (void *)(0x01120228 + DPSTART);                                     
static float       *RCNGEAR= (void *)(0x0112022c + DPSTART);                                    
static float       *RCNLG= (void *)(0x01120230 + DPSTART);                                      
static float       *RCNLMDTG= (void *)(0x01120234 + DPSTART);                                   
static float       *RCNLN= (void *)(0x01120238 + DPSTART);                                      
static float       *RCNLP= (void *)(0x0112023c + DPSTART);                                      
static float       *RCNPHDTG= (void *)(0x01120240 + DPSTART);                                   
static float       *RCNPHY= (void *)(0x01120244 + DPSTART);                                     
static float       *RCNPTG= (void *)(0x01120248 + DPSTART);                                     
static float       *RCNPTG1= (void *)(0x0112024c + DPSTART);                                    
static float       *RCNPTGM= (void *)(0x01120250 + DPSTART);                                    
static float       *RCNPTVCD= (void *)(0x01120254 + DPSTART);                                   
static float       *RCNPTXCD= (void *)(0x01120258 + DPSTART);                                   
static float       *RCNTF= (void *)(0x0112025c + DPSTART);                                      
static float       *RCNTNG= (void *)(0x01120260 + DPSTART);                                     
static float       *RCNTNG1= (void *)(0x01120264 + DPSTART);                                    
static float       *RCNTNGM= (void *)(0x01120268 + DPSTART);                                    
static float       *RCNTPU= (void *)(0x0112026c + DPSTART);                                     
static float       *RCNVCD= (void *)(0x01120270 + DPSTART);                                     
static float       *RCNWAPL= (void *)(0x01120274 + DPSTART);                                    
static float       *RCNWGN= (void *)(0x01120278 + DPSTART);                                     
static float       *RCNWGP= (void *)(0x0112027c + DPSTART);                                     
static float       *RCNWHPR1= (void *)(0x01120280 + DPSTART);                                   
static float       *RCNWHPR3= (void *)(0x01120284 + DPSTART);                                   
static float       *RCNWTL= (void *)(0x01120288 + DPSTART);                                     
static float       *RCNXCD= (void *)(0x0112028c + DPSTART);                                     
static float       *RCOAGN= (void *)(0x01120290 + DPSTART);                                     
static float       *RCOAGP= (void *)(0x01120294 + DPSTART);                                     
static float       *RCOAILG= (void *)(0x01120298 + DPSTART);                                    
static float       *RCOALTED= (void *)(0x0112029c + DPSTART);                                   
static float       *RCOALTEU= (void *)(0x011202a0 + DPSTART);                                   
static float       *RCOAMAX= (void *)(0x011202a4 + DPSTART);                                    
static float       *RCOEGN= (void *)(0x011202a8 + DPSTART);                                     
static float       *RCOEGP= (void *)(0x011202ac + DPSTART);                                     
static float       *RCOELMAX= (void *)(0x011202b0 + DPSTART);                                   
static float       *RCOLEO= (void *)(0x011202b4 + DPSTART);                                     
static float       *RCONTIMER= (void *)(0x011202b8 + DPSTART);                                  
static float       *RCPEDTDB= (void *)(0x011202bc + DPSTART);                                   
static float       *RCPEDTG= (void *)(0x011202c0 + DPSTART);                                    
static float       *RCPEDTRM= (void *)(0x011202c4 + DPSTART);                                   
static float       *RCPEDTU= (void *)(0x011202c8 + DPSTART);                                    
static float       *RCPF= (void *)(0x011202cc + DPSTART);                                       
static float       *RCPFDTG= (void *)(0x0112031c + DPSTART);                                    
static float       *RCPFG= (void *)(0x0112036c + DPSTART);                                      
static float       *RCPFKAP= (void *)(0x011203bc + DPSTART);                                    
static float       *RCPTRM= (void *)(0x0112040c + DPSTART);                                     
static float       *RCPTRMG= (void *)(0x01120410 + DPSTART);                                    
static float       *RCRAHYD= (void *)(0x01120414 + DPSTART);                                    
static float       *RCRCEV= (void *)(0x01120418 + DPSTART);                                     
static float       *RCRCGAIN= (void *)(0x0112041c + DPSTART);                                   
static float       *RCRIAILV= (void *)(0x01120420 + DPSTART);                                   
static float       *RCRIAILX= (void *)(0x01120424 + DPSTART);                                   
static float       *RCROAILV= (void *)(0x01120428 + DPSTART);                                   
static float       *RCROAILX= (void *)(0x0112042c + DPSTART);                                   
static float       *RCROAVCD= (void *)(0x01120430 + DPSTART);                                   
static float       *RCROAXCD= (void *)(0x01120434 + DPSTART);                                   
static float       *RCRPXL= (void *)(0x01120438 + DPSTART);                                     
static float       *RCRPXLB= (void *)(0x0112043c + DPSTART);                                    
static float       *RCRPXLMAX= (void *)(0x01120440 + DPSTART);                                  
static float       *RCRPXLN= (void *)(0x01120444 + DPSTART);                                    
static float       *RCRPXLP= (void *)(0x01120448 + DPSTART);                                    
static float       *RCRUDG= (void *)(0x0112044c + DPSTART);                                     
static float       *RCRUDMDH= (void *)(0x01120450 + DPSTART);                                   
static float       *RCRUDTG= (void *)(0x01120454 + DPSTART);                                    
static float       *RCRUDTX= (void *)(0x01120458 + DPSTART);                                    
static float       *RCSAFHM= (void *)(0x0112045c + DPSTART);                                    
static float       *RCSAPHY1= (void *)(0x011204fc + DPSTART);                                   
static float       *RCSAPHY2= (void *)(0x0112059c + DPSTART);                                   
static float       *RCSAPHY3= (void *)(0x0112063c + DPSTART);                                   
static float       *RCSAV1= (void *)(0x011206dc + DPSTART);                                     
static float       *RCSAV2= (void *)(0x0112077c + DPSTART);                                     
static float       *RCSAX1= (void *)(0x0112081c + DPSTART);                                     
static float       *RCSAX2= (void *)(0x011208bc + DPSTART);                                     
static float       *RCTESTINK1= (void *)(0x0112095c + DPSTART);                                 
static float       *RCTESTINK2= (void *)(0x01120960 + DPSTART);                                 
static float       *RCTGLATT= (void *)(0x01120964 + DPSTART);                                   
static float       *RCTHYD= (void *)(0x01120968 + DPSTART);                                     
static float       *RCTLTIME= (void *)(0x0112096c + DPSTART);                                   
static float       *RCTRESET= (void *)(0x01120970 + DPSTART);                                   
static float       *RCUNLOAD= (void *)(0x011209a0 + DPSTART);                                   
static float       *RCURGN= (void *)(0x011209b8 + DPSTART);                                     
static float       *RCURGP= (void *)(0x011209bc + DPSTART);                                     
static float       *RCVCKTS= (void *)(0x011209c0 + DPSTART);                                    
static float       *RCVIAS= (void *)(0x011209c4 + DPSTART);                                     
static float       *RCVNW= (void *)(0x011209c8 + DPSTART);                                      
static float       *rcwhlsdot= (void *)(0x011209cc + DPSTART);                                  
static float       *rcwhlsdotlm= (void *)(0x011209d0 + DPSTART);                                
static float       *rcwhlsfgn= (void *)(0x011209d4 + DPSTART);                                  
static float       *rcwhlslag= (void *)(0x011209d8 + DPSTART);                                  
static float       *rcwhlspos= (void *)(0x011209dc + DPSTART);                                  
static float       *RCWHLTDB= (void *)(0x011209e0 + DPSTART);                                   
static float       *RCWHLTG= (void *)(0x011209e4 + DPSTART);                                    
static float       *RCWHLTRM= (void *)(0x011209e8 + DPSTART);                                   
static float       *RCWHLTRMU= (void *)(0x011209ec + DPSTART);                                  
static float       *RCWTRM= (void *)(0x011209f0 + DPSTART);                                     
static float       *RCWTRMG= (void *)(0x011209f4 + DPSTART);                                    
static float       *RCYDLRV= (void *)(0x011209f8 + DPSTART);                                    
static float       *RCYDLRX= (void *)(0x011209fc + DPSTART);                                    
static float       *RCYDURV= (void *)(0x01120a00 + DPSTART);                                    
static float       *RCYDURX= (void *)(0x01120a04 + DPSTART);                                    
static float       *RCYLRVCD= (void *)(0x01120a08 + DPSTART);                                   
static float       *RCYLRXCD= (void *)(0x01120a0c + DPSTART);                                   
static float       *RCYURVCD= (void *)(0x01120a10 + DPSTART);                                   
static float       *RCYURXCD= (void *)(0x01120a14 + DPSTART);                                   
static float       *RCYVCMD= (void *)(0x01120a18 + DPSTART);                                    
static float       *RCYXCMD= (void *)(0x01120a1c + DPSTART);                                    
static float       *RCZSTFRL= (void *)(0x01120a20 + DPSTART);                                   
static float       *rdforce= (void *)(0x01120a24 + DPSTART);                                    
static float       *rdlaserdist= (void *)(0x01120a28 + DPSTART);                                
static float       *rdlaseroffset= (void *)(0x01120a2c + DPSTART);                              
static float       *rdprotractor= (void *)(0x01120a30 + DPSTART);                               
static float       *RFMF1BD= (void *)(0x01120a34 + DPSTART);                                    
static float       *RFMF1F= (void *)(0x01120a84 + DPSTART);                                     
static float       *RFMF2A= (void *)(0x01120ad4 + DPSTART);                                     
static float       *RFMF2F= (void *)(0x01120b24 + DPSTART);                                     
static float       *RFMFBW= (void *)(0x01120b74 + DPSTART);                                     
static float       *RFMXCIN= (void *)(0x01120bc4 + DPSTART);                                    
static float       *RPAPPCOM= (void *)(0x01120bdc + DPSTART);                                   
static float       *rpbrkcdn= (void *)(0x01120be0 + DPSTART);                                   
static float       *rpbrkcdp= (void *)(0x01120be8 + DPSTART);                                   
static float       *rpbrktaui= (void *)(0x01120bf0 + DPSTART);                                  
static float       *RRAPRC= (void *)(0x01120bf4 + DPSTART);                                     
static float       *RRAPRR= (void *)(0x01120bf8 + DPSTART);                                     
static float       *RRCFBDSF= (void *)(0x01120bfc + DPSTART);                                   
static float       *RRCFBFSF= (void *)(0x01120c4c + DPSTART);                                   
static float       *RRCFBMSF= (void *)(0x01120c9c + DPSTART);                                   
static float       *RRCFBNSPP= (void *)(0x01120cec + DPSTART);                                  
static float       *RRCFBXOF= (void *)(0x01120d8c + DPSTART);                                   
static float       *RRCFBXSF= (void *)(0x01120ddc + DPSTART);                                   
static float       *RTATTM1= (void *)(0x01120e2c + DPSTART);                                    
static float       *RTAXT1= (void *)(0x01120e30 + DPSTART);                                     
static float       *tcoveride= (void *)(0x01120e34 + DPSTART);                                  
static float       *dortamp= (void *)(0x01120e38 + DPSTART);                                    
static float       *dortfmaxn= (void *)(0x01120e88 + DPSTART);                                  
static float       *dortfmaxp= (void *)(0x01120ed8 + DPSTART);                                  
static float       *dortpos= (void *)(0x01120f28 + DPSTART);                                    
static float       *rcfadein= (void *)(0x01120f78 + DPSTART);                                   
static float       *rcfadeinrate= (void *)(0x01120fc8 + DPSTART);                               
static float       *rcfrccor= (void *)(0x01120fcc + DPSTART);                                   
static float       *rcfrccorf= (void *)(0x0112101c + DPSTART);                                  
static float       *rcfrccorlm= (void *)(0x0112106c + DPSTART);                                 
static float       *rcfrccorp= (void *)(0x011210bc + DPSTART);                                  
static float       *rchspare= (void *)(0x0112110c + DPSTART);                                   
static float       *rcrawfrc= (void *)(0x011211ac + DPSTART);                                   
static float       *rcrawfrcc= (void *)(0x011211fc + DPSTART);                                  
static float       *rcrawpos= (void *)(0x0112124c + DPSTART);                                   
static float       *rcrawvel= (void *)(0x0112129c + DPSTART);                                   
static float       *rct1slope= (void *)(0x011212ec + DPSTART);                                  
static float       *rctsf1pos= (void *)(0x0112133c + DPSTART);                                  
static float       *rctsffrc= (void *)(0x0112138c + DPSTART);                                   
static float       *rctsfpos= (void *)(0x011213dc + DPSTART);                                   
static float       *rctslope= (void *)(0x0112142c + DPSTART);                                   
static float       *rczero= (void *)(0x0112147c + DPSTART);                                     
static char        *RAPENG= (void *)(0x01121480 + DPSTART);                                     
static char        *rchar0= (void *)(0x011214d0 + DPSTART);                                     
static char        *dortstat= (void *)(0x011214d4 + DPSTART);                                   
static long        *cqhosttimer= (void *)(0x011217f8 + DPSTART);                                
static long        *cqhostup= (void *)(0x011217fc + DPSTART);                                   
static long        *cqhsdtoggle= (void *)(0x01121800 + DPSTART);                                
static long        *cqreadupdt= (void *)(0x01121804 + DPSTART);                                 
static long        *ctliook= (void *)(0x01121808 + DPSTART);                                    
static long        *drcicount= (void *)(0x0112180c + DPSTART);                                  
static long        *KC10CMD= (void *)(0x01121810 + DPSTART);                                    
static long        *KC10SAFE= (void *)(0x01121814 + DPSTART);                                   
static long        *KC10STATUS= (void *)(0x01121818 + DPSTART);                                 
static long        *KC11CMD= (void *)(0x0112181c + DPSTART);                                    
static long        *KC11SAFE= (void *)(0x01121820 + DPSTART);                                   
static long        *KC11STATUS= (void *)(0x01121824 + DPSTART);                                 
static long        *KC12CMD= (void *)(0x01121828 + DPSTART);                                    
static long        *KC12SAFE= (void *)(0x0112182c + DPSTART);                                   
static long        *KC12STATUS= (void *)(0x01121830 + DPSTART);                                 
static long        *KC1CMD= (void *)(0x01121834 + DPSTART);                                     
static long        *KC1SAFE= (void *)(0x01121838 + DPSTART);                                    
static long        *KC1STATUS= (void *)(0x0112183c + DPSTART);                                  
static long        *KC2CMD= (void *)(0x01121840 + DPSTART);                                     
static long        *KC2SAFE= (void *)(0x01121844 + DPSTART);                                    
static long        *KC2STATUS= (void *)(0x01121848 + DPSTART);                                  
static long        *KC3CMD= (void *)(0x0112184c + DPSTART);                                     
static long        *KC3SAFE= (void *)(0x01121850 + DPSTART);                                    
static long        *KC3STATUS= (void *)(0x01121854 + DPSTART);                                  
static long        *KC4CMD= (void *)(0x01121858 + DPSTART);                                     
static long        *KC4SAFE= (void *)(0x0112185c + DPSTART);                                    
static long        *KC4STATUS= (void *)(0x01121860 + DPSTART);                                  
static long        *KC5CMD= (void *)(0x01121864 + DPSTART);                                     
static long        *KC5SAFE= (void *)(0x01121868 + DPSTART);                                    
static long        *KC5STATUS= (void *)(0x0112186c + DPSTART);                                  
static long        *KC6CMD= (void *)(0x01121870 + DPSTART);                                     
static long        *KC6SAFE= (void *)(0x01121874 + DPSTART);                                    
static long        *KC6STATUS= (void *)(0x01121878 + DPSTART);                                  
static long        *KC7CMD= (void *)(0x0112187c + DPSTART);                                     
static long        *KC7SAFE= (void *)(0x01121880 + DPSTART);                                    
static long        *KC7STATUS= (void *)(0x01121884 + DPSTART);                                  
static long        *KC8CMD= (void *)(0x01121888 + DPSTART);                                     
static long        *KC8SAFE= (void *)(0x0112188c + DPSTART);                                    
static long        *KC8STATUS= (void *)(0x01121890 + DPSTART);                                  
static long        *KC9CMD= (void *)(0x01121894 + DPSTART);                                     
static long        *KC9SAFE= (void *)(0x01121898 + DPSTART);                                    
static long        *KC9STATUS= (void *)(0x0112189c + DPSTART);                                  
static long        *KCRSTATUS= (void *)(0x011218a0 + DPSTART);                                  
static long        *KRT1MODTM= (void *)(0x011218a4 + DPSTART);                                  
static long        *KRT1RATE= (void *)(0x011218f4 + DPSTART);                                   
static long        *KRT1TASKTM= (void *)(0x011218f8 + DPSTART);                                 
static long        *MODEL_TYPE= (void *)(0x011218fc + DPSTART);                                 
static long        *RTTASKCNT= (void *)(0x01121900 + DPSTART);                                  
static long        *TSTBHM= (void *)(0x01121928 + DPSTART);                                     
static long        *kcaftm= (void *)(0x0112192c + DPSTART);                                     
static long        *kcbd= (void *)(0x0112197c + DPSTART);                                       
static long        *kccable= (void *)(0x011219cc + DPSTART);                                    
static long        *kcclp1= (void *)(0x01121a1c + DPSTART);                                     
static long        *kcclp2= (void *)(0x01121abc + DPSTART);                                     
static long        *kcclp3= (void *)(0x01121b5c + DPSTART);                                     
static long        *kcclp4= (void *)(0x01121bfc + DPSTART);                                     
static long        *kcfeelspr= (void *)(0x01121c9c + DPSTART);                                  
static long        *kcfwdm= (void *)(0x01121cec + DPSTART);                                     
static long        *kcindx= (void *)(0x01121d3c + DPSTART);                                     
static long        *kcsact1= (void *)(0x01121ddc + DPSTART);                                    
static long        *kcsact2= (void *)(0x01121e2c + DPSTART);                                    
static long        *kcsact3= (void *)(0x01121e7c + DPSTART);                                    
static long        *kcsact4= (void *)(0x01121ecc + DPSTART);                                    
static long        *kctable= (void *)(0x01121f1c + DPSTART);                                    
static long        *kctablep= (void *)(0x01121f6c + DPSTART);                                   
static long        *kctact= (void *)(0x01121fbc + DPSTART);                                     
static char        *LCAPCWS= (void *)(0x0112200c + DPSTART);                                    
static char        *LCAPCWSA= (void *)(0x01122020 + DPSTART);                                   
static char        *LCAPOR= (void *)(0x01122034 + DPSTART);                                     
static char        *LCAPPSV1= (void *)(0x01122048 + DPSTART);                                   
static char        *LCAPPSV2= (void *)(0x01122049 + DPSTART);                                   
static char        *LCAPPSV3= (void *)(0x0112204a + DPSTART);                                   
static char        *LCAPRSV1= (void *)(0x0112204b + DPSTART);                                   
static char        *LCAPRSV2= (void *)(0x0112204c + DPSTART);                                   
static char        *LCAPRSV3= (void *)(0x0112204d + DPSTART);                                   
static char        *LCAPYSV1= (void *)(0x0112204e + DPSTART);                                   
static char        *LCAPYSV2= (void *)(0x0112204f + DPSTART);                                   
static char        *LCAPYSV3= (void *)(0x01122050 + DPSTART);                                   
static char        *LCBUFFOUT= (void *)(0x01122051 + DPSTART);                                  
static char        *LCBUFFOVRD= (void *)(0x01122052 + DPSTART);                                 
static char        *LCCMD= (void *)(0x01122053 + DPSTART);                                      
static char        *LCHOSTOVRD= (void *)(0x01122054 + DPSTART);                                 
static char        *LCIELEV= (void *)(0x01122055 + DPSTART);                                    
static char        *LCLFAIL= (void *)(0x01122056 + DPSTART);                                    
static char        *LCLOFF= (void *)(0x01122057 + DPSTART);                                     
static char        *LCLON= (void *)(0x01122058 + DPSTART);                                      
static char        *LCLSWITCH= (void *)(0x01122059 + DPSTART);                                  
static char        *LCQUIT= (void *)(0x0112205a + DPSTART);                                     
static char        *LCSETAIL= (void *)(0x0112206e + DPSTART);                                   
static char        *LCSETCSFOR= (void *)(0x0112206f + DPSTART);                                 
static char        *LCSETCSSP= (void *)(0x01122070 + DPSTART);                                  
static char        *LCSETCSWP= (void *)(0x01122071 + DPSTART);                                  
static char        *LCSETCWFOR= (void *)(0x01122072 + DPSTART);                                 
static char        *LCSETELEV= (void *)(0x01122073 + DPSTART);                                  
static char        *LCSETLG= (void *)(0x01122074 + DPSTART);                                    
static char        *LCSETLGH= (void *)(0x01122075 + DPSTART);                                   
static char        *LCSETLGHF= (void *)(0x01122076 + DPSTART);                                  
static char        *LCSETLTB= (void *)(0x01122077 + DPSTART);                                   
static char        *LCSETLTBF= (void *)(0x01122078 + DPSTART);                                  
static char        *LCSETNFOR= (void *)(0x01122079 + DPSTART);                                  
static char        *LCSETNG= (void *)(0x0112207a + DPSTART);                                    
static char        *LCSETNGH= (void *)(0x0112207b + DPSTART);                                   
static char        *LCSETNGHF= (void *)(0x0112207c + DPSTART);                                  
static char        *LCSETNW= (void *)(0x0112207d + DPSTART);                                    
static char        *LCSETNWT= (void *)(0x0112207e + DPSTART);                                   
static char        *LCSETPFOR= (void *)(0x0112207f + DPSTART);                                  
static char        *LCSETRAIL= (void *)(0x01122080 + DPSTART);                                  
static char        *LCSETRELEV= (void *)(0x01122081 + DPSTART);                                 
static char        *LCSETRG= (void *)(0x01122082 + DPSTART);                                    
static char        *LCSETRGH= (void *)(0x01122083 + DPSTART);                                   
static char        *LCSETRGHF= (void *)(0x01122084 + DPSTART);                                  
static char        *LCSETRTB= (void *)(0x01122085 + DPSTART);                                   
static char        *LCSETRTBF= (void *)(0x01122086 + DPSTART);                                  
static char        *LCSETRUDD= (void *)(0x01122087 + DPSTART);                                  
static char        *LCSETSFOR= (void *)(0x01122088 + DPSTART);                                  
static char        *LCSETSPP= (void *)(0x01122089 + DPSTART);                                   
static char        *LCSETSSP= (void *)(0x0112208a + DPSTART);                                   
static char        *LCSETSWP= (void *)(0x0112208b + DPSTART);                                   
static char        *LCSETWFOR= (void *)(0x0112208c + DPSTART);                                  
static char        *LCZONLINE= (void *)(0x0112208d + DPSTART);                                  
static char        *RAMBMJ= (void *)(0x0112208e + DPSTART);                                     
static char        *RBDBFTG= (void *)(0x011220a2 + DPSTART);                                    
static char        *RBDBXTG= (void *)(0x011220b6 + DPSTART);                                    
static char        *RCAON1= (void *)(0x011220ca + DPSTART);                                     
static char        *RCAON10= (void *)(0x011220cb + DPSTART);                                    
static char        *RCAON11= (void *)(0x011220cc + DPSTART);                                    
static char        *RCAON12= (void *)(0x011220cd + DPSTART);                                    
static char        *RCAON2= (void *)(0x011220ce + DPSTART);                                     
static char        *RCAON3= (void *)(0x011220cf + DPSTART);                                     
static char        *RCAON4= (void *)(0x011220d0 + DPSTART);                                     
static char        *RCAON5= (void *)(0x011220d1 + DPSTART);                                     
static char        *RCAON6= (void *)(0x011220d2 + DPSTART);                                     
static char        *RCAON7= (void *)(0x011220d3 + DPSTART);                                     
static char        *RCAON8= (void *)(0x011220d4 + DPSTART);                                     
static char        *RCAON9= (void *)(0x011220d5 + DPSTART);                                     
static char        *RCAONA= (void *)(0x011220d6 + DPSTART);                                     
static char        *RCAPLENG= (void *)(0x011220e2 + DPSTART);                                   
static char        *RCAPPENG= (void *)(0x011220e3 + DPSTART);                                   
static char        *RCAPRENG= (void *)(0x011220e4 + DPSTART);                                   
static char        *RCAPUENG= (void *)(0x011220e5 + DPSTART);                                   
static char        *RCAPYENG= (void *)(0x011220e6 + DPSTART);                                   
static char        *RCBLCONT= (void *)(0x011220e7 + DPSTART);                                   
static char        *RCBLCONTM= (void *)(0x011220e8 + DPSTART);                                  
static char        *RCEXBCKV= (void *)(0x011220e9 + DPSTART);                                   
static char        *RCEXBCLI= (void *)(0x01122111 + DPSTART);                                   
static char        *RCEXBFTG= (void *)(0x01122125 + DPSTART);                                   
static char        *RCEXBMJ= (void *)(0x01122139 + DPSTART);                                    
static char        *RCEXBPF= (void *)(0x0112214d + DPSTART);                                    
static char        *RCEXBPJ= (void *)(0x01122175 + DPSTART);                                    
static char        *RCEXBVJ= (void *)(0x0112219d + DPSTART);                                    
static char        *RCEXBXTG= (void *)(0x011221c5 + DPSTART);                                   
static char        *RCEXTRL= (void *)(0x011221d9 + DPSTART);                                    
static char        *RCSABPJ= (void *)(0x011221ed + DPSTART);                                    
static char        *RCSABVJ1= (void *)(0x01122215 + DPSTART);                                   
static char        *RCSABVJ2= (void *)(0x0112223d + DPSTART);                                   
static char        *RCSABVJ3= (void *)(0x01122265 + DPSTART);                                   
static char        *RFMBMJ= (void *)(0x0112228d + DPSTART);                                     
static char        *rcaafcs_rel= (void *)(0x011222a1 + DPSTART);                                
static char        *rover_switches= (void *)(0x011222a1 + DPSTART);                             
static char        *rcaattd_dn= (void *)(0x011222a2 + DPSTART);                                 
static char        *rcaattd_up= (void *)(0x011222a3 + DPSTART);                                 
static char        *rcagrnthumb= (void *)(0x011222a4 + DPSTART);                                
static char        *rcamidthumb= (void *)(0x011222a5 + DPSTART);                                
static char        *rcapitch_dn= (void *)(0x011222a6 + DPSTART);                                
static char        *rcapitch_up= (void *)(0x011222a7 + DPSTART);                                
static char        *rcaredthumb= (void *)(0x011222a8 + DPSTART);                                
static char        *rcaroll_lt= (void *)(0x011222a9 + DPSTART);                                 
static char        *rcaroll_rt= (void *)(0x011222aa + DPSTART);                                 
static char        *rcatrigger= (void *)(0x011222ab + DPSTART);                                 
static char        *rcatriggerh= (void *)(0x011222ac + DPSTART);                                
static char        *ROVERCON= (void *)(0x011222ad + DPSTART);                                   
static char        *RSABCKV= (void *)(0x011222ae + DPSTART);                                    
static char        *RSABPJ= (void *)(0x011222d6 + DPSTART);                                     
static char        *RSABVJ= (void *)(0x011222fe + DPSTART);                                     
static char        **lchwemul= (void *)(0x01122326 + DPSTART);                                  
static float       *start_delay_time= (void *)(0x0112233a + DPSTART);                           
static float       *start_delay= (void *)(0x0112233e + DPSTART);                                
static float       *RCPAFBOB= (void *)(0x01122342 + DPSTART);                                   
static float       *RCRAFBOB= (void *)(0x01122346 + DPSTART);                                   
static float       *RCYAFBOB= (void *)(0x0112234a + DPSTART);                                   
static float       *AXPBWG= (void *)(0x0112234e + DPSTART);                                     
static float       *AYPBWG= (void *)(0x01122352 + DPSTART);                                     
static float       *AZPBWG= (void *)(0x01122356 + DPSTART);                                     
static float       *APG= (void *)(0x0112235a + DPSTART);                                        
static float       *ARG= (void *)(0x0112235e + DPSTART);                                        
static float       *AYG= (void *)(0x01122362 + DPSTART);                                        
static float       *RCGFBOB= (void *)(0x01122366 + DPSTART);                                    
static float       *RCFBOBLP= (void *)(0x01122372 + DPSTART);                                   
static float       *RCFBOBLN= (void *)(0x0112237e + DPSTART);                                   
static float       *RCANGLE= (void *)(0x0112238a + DPSTART);                                    
static float       *RCSCALE= (void *)(0x01122396 + DPSTART);                                    
static float       *RC_ELV_TRIM= (void *)(0x011223a2 + DPSTART);                                
static float       *RC_A2R_POS= (void *)(0x011223a6 + DPSTART);                                 
static float       *RC_A2R_VEL= (void *)(0x011223aa + DPSTART);                                 
static float       *RC_R2A_FOR= (void *)(0x011223ae + DPSTART);                                 
static long        *TCA2RX= (void *)(0x011223b2 + DPSTART);                                     
static long        *TCA2RV= (void *)(0x011223b6 + DPSTART);                                     
static long        *TCR2AF= (void *)(0x011223ba + DPSTART);                                     
static float       *AXRBWG= (void *)(0x011223be + DPSTART);                                     
static float       *AYRBWG= (void *)(0x011223c2 + DPSTART);                                     
static float       *AZRBWG= (void *)(0x011223c6 + DPSTART);                                     
static float       *RCNWLIM= (void *)(0x011223ca + DPSTART);                                    
static float       *RC_BRK_SET_POS= (void *)(0x011223ce + DPSTART);                             
static float       *RC_BRK_OFF_POS= (void *)(0x011223d6 + DPSTART);                             
static float       *PARK_BRK_N1= (void *)(0x011223de + DPSTART);                                
static float       *GCTLBUF_RHM_ADD= (void *)(0x011223e2 + DPSTART);                            
static float       *rhmichso1= (void *)(0x011223e2 + DPSTART);                                  
static float       *rhmkchso1= (void *)(0x01122482 + DPSTART);                                  
static float       *rhmchso1= (void *)(0x01122522 + DPSTART);                                   
static float       *rhmichso2= (void *)(0x011225c2 + DPSTART);                                  
static float       *rhmkchso2= (void *)(0x01122662 + DPSTART);                                  
static float       *rhmchso2= (void *)(0x01122702 + DPSTART);                                   
static float       *rhmichso3= (void *)(0x011227a2 + DPSTART);                                  
static float       *rhmkchso3= (void *)(0x01122842 + DPSTART);                                  
static float       *rhmchso3= (void *)(0x011228e2 + DPSTART);                                   
static float       *rhmglink= (void *)(0x01122982 + DPSTART);                                   
static long        *TCHMCHSO1= (void *)(0x01122a22 + DPSTART);                                  
static long        *TCHMKCHSO1= (void *)(0x01122ac2 + DPSTART);                                 
static long        *TCHMCHSO2= (void *)(0x01122b62 + DPSTART);                                  
static long        *TCHMKCHSO2= (void *)(0x01122c02 + DPSTART);                                 
static long        *TCHMCHSO3= (void *)(0x01122ca2 + DPSTART);                                  
static long        *TCHMKCHSO3= (void *)(0x01122d42 + DPSTART);                                 
static float       *rhmchso= (void *)(0x01122de2 + DPSTART);                                    
static float       *rhmghms= (void *)(0x01122e82 + DPSTART);                                    
static float       *rhmchs= (void *)(0x01122f22 + DPSTART);                                     
static float       *rhmcha= (void *)(0x01122fc2 + DPSTART);                                     
static float       *rhmchtotal= (void *)(0x01123062 + DPSTART);                                 
static float       **rhmaoai= (void *)(0x01123102 + DPSTART);                                   
static long        *TCHMKFHM= (void *)(0x011231a2 + DPSTART);                                   
static float       *rhmkhms= (void *)(0x01123242 + DPSTART);                                    
static float       *rhmifhm= (void *)(0x011232e2 + DPSTART);                                    
static float       *rhmdelt= (void *)(0x01123382 + DPSTART);                                    
static float       *rhmxsd= (void *)(0x01123422 + DPSTART);                                     
static float       *rhmxsn1= (void *)(0x011234c2 + DPSTART);                                    
static long        *TCHMADD0= (void *)(0x01123562 + DPSTART);                                   
static float       *RC_NWS_POS= (void *)(0x011236a4 + DPSTART);                                 
static float       *RC_NWS_POS_N1= (void *)(0x011236a8 + DPSTART);                              
static float       *RC_NWS_VEL= (void *)(0x011236ac + DPSTART);                                 
static long        *TCNWSX= (void *)(0x011236b0 + DPSTART);                                     
static long        *TCHMDS= (void *)(0x011236b4 + DPSTART);                                     
static long        *TCHMKDS= (void *)(0x011236b8 + DPSTART);                                    
static float       *RC_ELV_FDSI= (void *)(0x011236bc + DPSTART);                                
static float       *RC_ELV_FDSK= (void *)(0x011236c0 + DPSTART);                                
static float       *RC_ELV_FDS= (void *)(0x011236c4 + DPSTART);                                 
static long        *TCASTOR= (void *)(0x011236c8 + DPSTART);                                    
static char        *LC_START= (void *)(0x011236cc + DPSTART);                                   
static float       *rfmcdt= (void *)(0x011236d8 + DPSTART);                                     
static float       *rfmcdv= (void *)(0x01123728 + DPSTART);                                     
static float       *RC_DYNGOOSE= (void *)(0x01123784 + DPSTART);                                
static long        *TCGOOSE= (void *)(0x011237b4 + DPSTART);                                    
static char        *rfmbxstr= (void *)(0x01123820 + DPSTART);                                   
static float       *RCSPARE= (void *)(0x01123838 + DPSTART);                                    
static float       *RC_CASTOR_ANG= (void *)(0x0112386c + DPSTART);                              
static float       *RC_CASTOR_VEL= (void *)(0x01123870 + DPSTART);                              
static float       *RCREPOSD= (void *)(0x01123874 + DPSTART);                                   
static float       *RC_BKDR= (void *)(0x01123898 + DPSTART);                                    
static float       *AF_SPARE= (void *)(0x01124220 + DPSTART);                                   
static long        *osnapbuf= (void *)(0x011251c0 + DPSTART);                                   
static long        *CLDGXFER_BASE= (void *)(0x01143620 + DPSTART);                              
static char        *CLS_ON_OFF= (void *)(0x01143620 + DPSTART);                                 
static char        *CLS_STD_C= (void *)(0x01143620 + DPSTART);                                  
static float       *CLS_STD_F= (void *)(0x01143620 + DPSTART);                                  
static long        *CLS_STD_I= (void *)(0x01143620 + DPSTART);                                  
static long        *CTL_PXFER_BASE= (void *)(0x01143620 + DPSTART);                             
static long        *CTL_PXFER_IN= (void *)(0x01143620 + DPSTART);                               
static long        *KCZCWBUF= (void *)(0x01143620 + DPSTART);                                   
static long        *KCZHRBUF= (void *)(0x01143620 + DPSTART);                                   
static char        *LCTL_PXFER_IN= (void *)(0x01143620 + DPSTART);                              
static char        *LCZCWBUF= (void *)(0x01143620 + DPSTART);                                   
static char        *LCZHRBUF= (void *)(0x01143620 + DPSTART);                                   
static float       *RCTL_PXFER_IN= (void *)(0x01143620 + DPSTART);                              
static float       *RCZCWBUF= (void *)(0x01143620 + DPSTART);                                   
static float       *RCZHRBUF= (void *)(0x01143620 + DPSTART);                                   
static char        *CLS_MODE= (void *)(0x0114362c + DPSTART);                                   
static long        *CLS_RFC_CT= (void *)(0x01143638 + DPSTART);                                 
static long        *CLS_FAULT= (void *)(0x0114363c + DPSTART);                                  
static float       *CLS_RFMXC= (void *)(0x0114366c + DPSTART);                                  
static float       *CLS_XC= (void *)(0x0114366c + DPSTART);                                     
static float       *CLS_XTRM= (void *)(0x0114369c + DPSTART);                                   
static float       *CLS_FPC= (void *)(0x011436cc + DPSTART);                                    
static float       *CLS_RFMFPC= (void *)(0x011436cc + DPSTART);                                 
static char        *CLS_LATENCY_OUT= (void *)(0x011436fc + DPSTART);                            
static char        *CLS_DORT_STAT= (void *)(0x011436fd + DPSTART);                              
static float       *CLS_AFT_POS= (void *)(0x011437b0 + DPSTART);                                
static char        *CLS_GENSIM_C= (void *)(0x011437b0 + DPSTART);                               
static float       *CLS_GENSIM_F= (void *)(0x011437b0 + DPSTART);                               
static long        *CLS_GENSIM_I= (void *)(0x011437b0 + DPSTART);                               
static float       *CLS_RAMXM= (void *)(0x011437b0 + DPSTART);                                  
static float       *CLS_AFT_VEL= (void *)(0x011437e0 + DPSTART);                                
static float       *CLS_RAMVM= (void *)(0x011437e0 + DPSTART);                                  
static char        *CLS_CWS_ENG= (void *)(0x01143810 + DPSTART);                                
static float       *CLS_SURF_POS= (void *)(0x01143a6c + DPSTART);                               
static char        *CLS_UNIQUE_C= (void *)(0x01143a6c + DPSTART);                               
static float       *CLS_UNIQUE_F= (void *)(0x01143a6c + DPSTART);                               
static long        *CLS_UNIQUE_I= (void *)(0x01143a6c + DPSTART);                               
static float       *CLS_SURF_VEL= (void *)(0x01143a8c + DPSTART);                               
static float       *CLS_AFT_FOR= (void *)(0x01143aac + DPSTART);                                
static float       *CLS_AP_STQ= (void *)(0x01143abc + DPSTART);                                 
static float       *CLS_RFMVM= (void *)(0x01143ac8 + DPSTART);                                  
static float       *CLS_VC= (void *)(0x01143ac8 + DPSTART);                                     
static float       *CLS_XC_IN= (void *)(0x01143ae0 + DPSTART);                                  
static float       *CLS_RBDFBD= (void *)(0x01143af8 + DPSTART);                                 
static float       *CLS_RFMXM= (void *)(0x01143b10 + DPSTART);                                  
static float       *CLS_RCAXC= (void *)(0x01143b28 + DPSTART);                                  
static float       *CLS_RCAFC= (void *)(0x01143b40 + DPSTART);                                  
static float       *CLS_RCAXL= (void *)(0x01143b58 + DPSTART);                                  
static float       *CLS_RCAFL= (void *)(0x01143b70 + DPSTART);                                  
static float       *CLS_RCAENCX= (void *)(0x01143b88 + DPSTART);                                
static float       *CLS_RFMFP= (void *)(0x01143ba0 + DPSTART);                                  
static float       *CLS_RDFORCE= (void *)(0x01143bb8 + DPSTART);                                
static float       *CLS_RDPROTRACTOR= (void *)(0x01143bbc + DPSTART);                           
static float       *CLS_RDLASERDIST= (void *)(0x01143bc0 + DPSTART);                            
static char        *CLS_HOST_STD_C= (void *)(0x01143c60 + DPSTART);                             
static float       *CLS_HOST_STD_F= (void *)(0x01143c60 + DPSTART);                             
static long        *CLS_HOST_STD_I= (void *)(0x01143c60 + DPSTART);                             
static char        *CLS_ON_OFF_CMD= (void *)(0x01143c60 + DPSTART);                             
static long        *CTL_PXFER_OUT= (void *)(0x01143c60 + DPSTART);                              
static long        *KCZCRBUF= (void *)(0x01143c60 + DPSTART);                                   
static long        *KCZHWBUF= (void *)(0x01143c60 + DPSTART);                                   
static char        *LCTL_PXFER_OUT= (void *)(0x01143c60 + DPSTART);                             
static char        *LCZCRBUF= (void *)(0x01143c60 + DPSTART);                                   
static char        *LCZHWBUF= (void *)(0x01143c60 + DPSTART);                                   
static float       *RCTL_PXFER_OUT= (void *)(0x01143c60 + DPSTART);                             
static float       *RCZCRBUF= (void *)(0x01143c60 + DPSTART);                                   
static char        *CLS_MODE_CMD= (void *)(0x01143c6c + DPSTART);                               
static long        *CLS_HOST_CT= (void *)(0x01143c78 + DPSTART);                                
static char        *CLS_RESET_CMD= (void *)(0x01143c7c + DPSTART);                              
static float       *CLS_BKDR_CMD= (void *)(0x01143c88 + DPSTART);                               
static float       *CLS_BKDR_FOR_LIMIT= (void *)(0x01143cb8 + DPSTART);                         
static float       *CLS_XTRM_CMD= (void *)(0x01143ce8 + DPSTART);                               
static float       *CLS_AX= (void *)(0x01143d18 + DPSTART);                                     
static float       *CLS_AY= (void *)(0x01143d1c + DPSTART);                                     
static float       *CLS_AZ= (void *)(0x01143d20 + DPSTART);                                     
static float       *CLS_LATENCY_IN= (void *)(0x01143d24 + DPSTART);                             
static char        *CLS_DORT_ON= (void *)(0x01143d28 + DPSTART);                                
static char        *CLS_HOST_GENSIM_C= (void *)(0x01143df0 + DPSTART);                          
static float       *CLS_HOST_GENSIM_F= (void *)(0x01143df0 + DPSTART);                          
static long        *CLS_HOST_GENSIM_I= (void *)(0x01143df0 + DPSTART);                          
static char        *CLS_TRM_REL_SW= (void *)(0x01143df0 + DPSTART);                             
static float       *CLS_AIR_SPEED= (void *)(0x01143dfc + DPSTART);                              
static float       *CLS_MACH_NO= (void *)(0x01143e00 + DPSTART);                                
static float       *CLS_PED_STEER= (void *)(0x01143e04 + DPSTART);                              
static float       *CLS_FEEL_PRESS= (void *)(0x01143e08 + DPSTART);                             
static float       *CLS_FGRAD= (void *)(0x01143e38 + DPSTART);                                  
static float       *CLS_FOFF= (void *)(0x01143e68 + DPSTART);                                   
static float       *CLS_DAMP= (void *)(0x01143e98 + DPSTART);                                   
static float       *CLS_AP_CMD= (void *)(0x01143ec8 + DPSTART);                                 
static char        *CLS_AP_ENG= (void *)(0x01143ef8 + DPSTART);                                 
static char        *CLS_AFT_JAM= (void *)(0x01143f04 + DPSTART);                                
static char        *CLS_FWD_JAM= (void *)(0x01143f10 + DPSTART);                                
static char        *CLS_HOST_UNIQUE_C= (void *)(0x011440ac + DPSTART);                          
static float       *CLS_HOST_UNIQUE_F= (void *)(0x011440ac + DPSTART);                          
static long        *CLS_HOST_UNIQUE_I= (void *)(0x011440ac + DPSTART);                          
static char        *CLS_TRM_RST= (void *)(0x011440ac + DPSTART);                                
static char        *CLS_PARK_BRK= (void *)(0x011440af + DPSTART);                               
static char        *CLS_TOW_ENG= (void *)(0x011440b0 + DPSTART);                                
static char        *CLS_SKID_PLATE= (void *)(0x011440b1 + DPSTART);                             
static float       *CLS_SURF_Q= (void *)(0x011440b4 + DPSTART);                                 
static float       *CLS_SURF_AS_INV= (void *)(0x011440c0 + DPSTART);                            
static float       *CLS_SURF_ALPHA= (void *)(0x011440cc + DPSTART);                             
static float       *CLS_PITCH_ACC= (void *)(0x011440d8 + DPSTART);                              
static float       *CLS_ROLL_ACC= (void *)(0x011440dc + DPSTART);                               
static float       *CLS_NW_FOR= (void *)(0x011440e0 + DPSTART);                                 
static float       *CLS_TOW_POS= (void *)(0x011440e4 + DPSTART);                                
static float       *CLS_STICK_PUSHER= (void *)(0x011440e8 + DPSTART);                           
static float       *CLS_NW_LIM= (void *)(0x011440ec + DPSTART);                                 
static float       *CLS_NW_CASTOR_ANG= (void *)(0x011440f0 + DPSTART);                          
static float       *CLS_NW_CASTOR_GAIN= (void *)(0x011440f4 + DPSTART);                         
static float       *CLS_NW_SPEED= (void *)(0x011440f8 + DPSTART);                               
static float       *CLS_RBDGFASTOFF= (void *)(0x011440fc + DPSTART);                            
static char        *CLS_RBDFASTOFF= (void *)(0x01144108 + DPSTART);                             
static float       *CLS_FLAP= (void *)(0x0114410c + DPSTART);                                   
static float       *CLS_RUD_BOOST_FOR= (void *)(0x01144110 + DPSTART);                          
static float       *CLS_NW_CASTOR_VEL= (void *)(0x01144114 + DPSTART);                          
static float       *CLS_YAW_ACC= (void *)(0x01144118 + DPSTART);                                
static float       *CLS_YFRSVP= (void *)(0x0114411c + DPSTART);                                 
static char        *CLS_REPOS= (void *)(0x01144120 + DPSTART);                                  
static float       *CLS_H_AGL= (void *)(0x01144124 + DPSTART);                                  
static float       *CLS_NOSE_OFFSET= (void *)(0x01144128 + DPSTART);                            
static long        *CLS_INPUT_TMP= (void *)(0x011445c0 + DPSTART);                              
static char        *HOST_STD_C= (void *)(0x011445c0 + DPSTART);                                 
static float       *HOST_STD_F= (void *)(0x011445c0 + DPSTART);                                 
static long        *HOST_STD_I= (void *)(0x011445c0 + DPSTART);                                 
static char        *ON_OFF_CMD= (void *)(0x011445c0 + DPSTART);                                 
static char        *MODE_CMD= (void *)(0x011445cc + DPSTART);                                   
static long        *HOST_CT= (void *)(0x011445d8 + DPSTART);                                    
static char        *RESET_CMD= (void *)(0x011445dc + DPSTART);                                  
static float       *BKDR_CMD= (void *)(0x011445e8 + DPSTART);                                   
static float       *BKDR_FOR_LIMIT= (void *)(0x01144618 + DPSTART);                             
static float       *XTRM_CMD= (void *)(0x01144648 + DPSTART);                                   
static float       *AX_ACC= (void *)(0x01144678 + DPSTART);                                     
static float       *AY_ACC= (void *)(0x0114467c + DPSTART);                                     
static float       *AZ_ACC= (void *)(0x01144680 + DPSTART);                                     
static float       *LATENCY_IN= (void *)(0x01144684 + DPSTART);                                 
static char        *DORT_ON= (void *)(0x01144688 + DPSTART);                                    
static char        *HOST_GENSIM_C= (void *)(0x01144750 + DPSTART);                              
static float       *HOST_GENSIM_F= (void *)(0x01144750 + DPSTART);                              
static long        *HOST_GENSIM_I= (void *)(0x01144750 + DPSTART);                              
static char        *TRM_REL_SW= (void *)(0x01144750 + DPSTART);                                 
static float       *AIR_SPEED= (void *)(0x0114475c + DPSTART);                                  
static float       *MACH_NO= (void *)(0x01144760 + DPSTART);                                    
static float       *PED_STEER= (void *)(0x01144764 + DPSTART);                                  
static float       *FEEL_PRESS= (void *)(0x01144768 + DPSTART);                                 
static float       *FGRAD= (void *)(0x01144798 + DPSTART);                                      
static float       *FOFF= (void *)(0x011447c8 + DPSTART);                                       
static float       *DAMP= (void *)(0x011447f8 + DPSTART);                                       
static float       *AP_CMD= (void *)(0x01144828 + DPSTART);                                     
static char        *AP_ENG= (void *)(0x01144858 + DPSTART);                                     
static char        *AFT_JAM= (void *)(0x01144864 + DPSTART);                                    
static char        *FWD_JAM= (void *)(0x01144870 + DPSTART);                                    
static char        *HOST_UNIQUE_C= (void *)(0x01144a0c + DPSTART);                              
static float       *HOST_UNIQUE_F= (void *)(0x01144a0c + DPSTART);                              
static long        *HOST_UNIQUE_I= (void *)(0x01144a0c + DPSTART);                              
static char        *TRM_RST= (void *)(0x01144a0c + DPSTART);                                    
static char        *PARK_BRK= (void *)(0x01144a0f + DPSTART);                                   
static char        *TOW_ENG= (void *)(0x01144a10 + DPSTART);                                    
static char        *SKID_PLATE= (void *)(0x01144a11 + DPSTART);                                 
static float       *SURF_Q= (void *)(0x01144a14 + DPSTART);                                     
static float       *SURF_AS_INV= (void *)(0x01144a20 + DPSTART);                                
static float       *SURF_ALPHA= (void *)(0x01144a2c + DPSTART);                                 
static float       *PITCH_ACC= (void *)(0x01144a38 + DPSTART);                                  
static float       *ROLL_ACC= (void *)(0x01144a3c + DPSTART);                                   
static float       *NW_FOR= (void *)(0x01144a40 + DPSTART);                                     
static float       *TOW_POS= (void *)(0x01144a44 + DPSTART);                                    
static float       *STICK_PUSHER= (void *)(0x01144a48 + DPSTART);                               
static float       *NW_LIM= (void *)(0x01144a4c + DPSTART);                                     
static float       *NW_CASTOR_ANG= (void *)(0x01144a50 + DPSTART);                              
static float       *NW_CASTOR_GAIN= (void *)(0x01144a54 + DPSTART);                             
static float       *NW_SPEED= (void *)(0x01144a58 + DPSTART);                                   
static float       *FLAP= (void *)(0x01144a6c + DPSTART);                                       
static float       *RUD_BOOST_FOR= (void *)(0x01144a70 + DPSTART);                              
static float       *NW_CASTOR_VEL= (void *)(0x01144a74 + DPSTART);                              
static float       *YAW_ACC= (void *)(0x01144a78 + DPSTART);                                    
static float       *YFRSVP= (void *)(0x01144a7c + DPSTART);                                     
static char        *REPOS= (void *)(0x01144a80 + DPSTART);                                      
static float       *H_AGL= (void *)(0x01144a84 + DPSTART);                                      
static float       *NOSE_OFFSET= (void *)(0x01144a88 + DPSTART);                                
static float       *LVTABLEC= (void *)(0x01146500 + DPSTART);                                   
static float       *LVTABLEP= (void *)(0x01146500 + DPSTART);                                   
@
