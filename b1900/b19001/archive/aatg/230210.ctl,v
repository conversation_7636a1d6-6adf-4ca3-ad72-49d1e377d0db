head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.34;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Flap Change Dynamics
*--------------------------------------------



*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

LZFNHLD: True
* IFXTRIM:   3
LZROLHLD:  True
* LZTQHLD:   True
* Post_trim: LZTQHLD F

*-----------------------------------------------------
#           Min       <PERSON>
X_Scale:   0.00     30.00    2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: flap_def                         -10.0       20.0     10.0
Plot: flap_lvr                         -10.0       20.0     10.0
Plot: kcas            3.0              120.0      180.0     20.0
Plot: hp            100.0             4500.0     5500.0    500.0

Plot: ins_vt_spd                     -4000.0     2000.0   2000.0
Plot: pitch_att       1.5    20.0      -10.0       10.0     10.0
Plot: roll_att                          -5.0        5.0      5.0
Plotf: heading                          10.0       40.0     10.0

Plot: pitch_rate                        -2.0        2.0      2.0
Plot: roll_rate                         -2.0        2.0      2.0
Plotf: yaw_rate                          -2.0        2.0      2.0
Plot: alpha_true                        -5.0       10.0      5.0

*Plot: beta_true                         -5.0        5.0      5.0
*Plot: ax_corrctd                        -0.2        0.2      0.2
*Plot: ay_corrctd                        -0.1        0.1      0.1
*Plot: az_corrctd                        -2.0        2.0      2.0

*Plot: pitch_acc                         -2.0        2.0      2.0
*Plot: roll_acc                          -5.0        5.0      5.0
*Plot: yaw_acc                           -2.0        2.0      2.0
*Plot: pla_l                              2.0        6.0      2.0

*Plot: pla_r                              2.0        6.0      2.0
*Plot: torque_l                         500.0     2000.0    500.0
*Plot: torque_r                        1000.0     2000.0    500.0
Plot: fn_l                             400.0      800.0    200.0

Plot: fn_r                             400.0     1000.0    200.0
Plot: ele_trim                           2.0        6.0      2.0
Plot: ele_def                           -5.0        5.0      5.0
*Plot: pcp                               -5.0        5.0      5.0

*Plot: pcf                              -20.0       20.0     20.0
*Plot: ail_trim                           0.0       10.0      5.0
*Plot: pwf                              -20.0       20.0     20.0
*Plot: pwp                              -10.0       20.0     10.0

*Plot: ail_def_l                         -5.0       10.0      5.0
*Plot: ail_def_r                          0.0       10.0      5.0
*Plot: rud_trim                          -5.0        5.0      5.0
*Plot: rud_def                            0.0       10.0      5.0

*Plot: prpp_l                            -2.0        2.0      2.0
*Plot: prpf                             -20.0       20.0     20.0
* Plot: wnddir_cal                       160.0      200.0     20.0
* Plot: wndvel_cal                       -10.0       20.0     10.0

Add_Criteria: flap_lvr
@
