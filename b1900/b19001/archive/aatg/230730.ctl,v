head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.35;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Long Man Stability
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

target_bank_angles:  1.5   20.5    30.0   45.0
target_bank_tol:     1.0    1.5    1.0     1.0

LZROLHLD:     True
LZPITHLD:     True
LZPITIASHLD:  True
LZTQHLD:      True
LZRASSIST:    True
LZPASSIST:    True
* Post_Trim:    LZTQHLD=F

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    240.00    20.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas                             100.0      110.0      5.0
Plot: hp                              4000.0     5600.0    800.0
Plot: ins_vt_spd                     -2000.0     1000.0   1000.0
* Plot: ele_trim                           4.0       10.0      2.0

Plot: ele_def       1.0                -10.0        0.0      5.0
* Plot: pcp                               -5.0        5.0      5.0
Plot: pcf                              -50.0      100.0     50.0
Plot: pitch_att                         -5.0        5.0      5.0

Plot: roll_att                           0.0       80.0     40.0
Plot: heading                            0.0      500.0    250.0
Plot: pitch_rate                        -8.0        8.0      8.0
* Plot: roll_rate                        -10.0       10.0     10.0

* Plot: yaw_rate                           0.0       10.0      5.0
* Plot: alpha_true                         0.0       10.0      5.0
* Plot: beta_true                         -5.0        5.0      5.0
* Plot: ax_corrctd                        -0.2        0.2      0.2

* Plot: ay_corrctd                        -0.1        0.1      0.1
Plot: az_corrctd                        -2.0        0.0      1.0
* Plot: pitch_acc                         -8.0        8.0      8.0
* Plot: roll_acc                         -20.0       20.0     20.0

* Plot: yaw_acc                           -4.0        4.0      4.0
* Plot: pla_l                              2.0        6.0      2.0
* Plot: pla_r                              2.0        6.0      2.0
* Plot: torque_l                        1000.0     2000.0    500.0

* Plot: torque_r                        1000.0     2000.0    500.0
Plot: fn_l                             800.0     1200.0    200.0
Plot: fn_r                             800.0     1200.0    200.0
* Plot: ail_trim                          -5.0       10.0      5.0

* Plot: pwf                              -20.0       20.0     20.0
* Plot: pwp                              -20.0       20.0     20.0
* Plot: ail_def_l                         -5.0       10.0      5.0
* Plot: ail_def_r                          0.0       15.0      5.0

* Plot: rud_trim                          -5.0        5.0      5.0
* Plot: rud_def                          -10.0        5.0      5.0
* Plot: prpp_l                             0.0       10.0      5.0
* Plot: prpf                             -20.0       40.0     20.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
