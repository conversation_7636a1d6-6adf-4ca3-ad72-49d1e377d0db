head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.37;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Ground Effect
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

IFXTRIM:     2
FMNOWINDS:   False
default_lat_rates: False
RZCQDS:      0.0
RZCPDS:      0.0
RZCRDS:      0.0
LZHDGHLD:    True
LZYASSIST:   True
LZTQHLD:     True
LZCRADALT:   True
LZPITALTHLD: True
LZROLHLD:    True
LZPITHLD:    True
LZPASSIST:   True
LZRASSIST:   True
RZCUDOT:     0.0
Field_Elevation:  850.60
LZFLARE:     False
RZFLAREH:    190
RZCROC:     -30.00
LDATRMON(8): 0
LFXSTABT:    True
LFXAILT:     False
LFXRUDT:     False
RFXHDGCM:    175.850
Crit_Offset: heading   0      -5.0      1
*
Pre_Trim: RZPITALTK=.15
Pre_Trim: RZPGAIN(7)=0.04
Pre_Trim: RZIGAIN(7)=2
Pre_Trim: RZDGAIN(7)=2
Pre_Trim: LZTRIM_ROC_UDOT=T
*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    100.00    10.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: radalt          5.0       10.0      -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
*Plot: hp                                  -20.0   20.0    20.0
*Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: alpha_true      1.0                 -20.0   20.0    20.0
*Plot: beta_true                           -20.0   20.0    20.0
Plot: pitch_att       1.0                 -20.0   20.0    20.0
*Plot: roll_att                            -20.0   20.0    20.0
*Plot: heading                             -20.0   20.0    20.0
*Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def         1.0                 -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
*Plot: pitch_rate                          -20.0   20.0    20.0
*Plot: roll_rate                           -20.0   20.0    20.0
*Plot: yaw_rate                            -20.0   20.0    20.0
*Plot: pla_l                               -20.0   20.0    20.0
*Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
*Plot: fn_l                                -20.0   20.0    20.0
*Plot: fn_r                                -20.0   20.0    20.0
*Plot: rud_trim                            -20.0   20.0    20.0
*Plot: rud_def                             -20.0   20.0    20.0
*Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
*Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
*Plot: pwp                                 -20.0   20.0    20.0
*Plot: ail_def_l                           -20.0   20.0    20.0
*Plot: ail_def_r                           -20.0   20.0    20.0
*Plot: pitch_acc                           -20.0   20.0    20.0
*Plot: roll_acc                            -20.0   20.0    20.0
*Plot: yaw_acc                             -20.0   20.0    20.0
*Plot: ax_corrctd                          -20.0   20.0    20.0
*Plot: ay_corrctd                          -20.0   20.0    20.0
*Plot: az_corrctd                          -20.0   20.0    20.0
@
