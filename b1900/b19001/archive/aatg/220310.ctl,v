head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.34;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* <PERSON>yn <PERSON> to Yaw Input
*--------------------------------------------


#            Var     x_off  y_off     y_mult
Crit_Offset: prpp_l  -0.01  -1.28     -1

*-----------------------------------------
KAXPMODE:  3
KAXRMODE:  3
KAXYMODE:  3
*-----------------------------------------

ZERO_RATES:  True

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     36.00     4.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
*Plot: rud_trim                            -20.0   20.0    20.0
*Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0


pre_trim: *
pre_trim: C CLS_RBDFASTOFF(3) 1        ! fast release for dynamics
pre_trim: C CLS_RBDGFASTOFF(3) 100       ! speed up release for dynamics
pre_trim: *

clean_up: *
clean_up: C CLS_RBDFASTOFF(3) 0
clean_up: *
@
