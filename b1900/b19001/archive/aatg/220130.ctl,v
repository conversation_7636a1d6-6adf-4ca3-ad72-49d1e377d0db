head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.34;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* <PERSON>yn <PERSON> to Pitch Input
*--------------------------------------------


#            Var    x_off  y_off     y_mult
Crit_Offset: pcp    -0.01   0.0      1

FDEMSTAB: -1.213
LFXSTABT: False


*-----------------------------------------
KAXPMODE:  3
KAXRMODE:  3
KAXYMODE:  3
*-----------------------------------------

ZERO_RATES:  True

*-----------------------------------------------------
#           <PERSON>       <PERSON>
X_Scale:   0.00     20.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    <PERSON>    Delta
#                    ----       ----       ---    ---    -----
* Plot: ele_trim                          -2.0        2.0      2.0
*Plot: ele_def                           -5.0        5.0      5.0
Plot: pcp                               -1.25       1.0      0.25
*Plot: pcf                             -100.0      200.0    100.0


pre_trim: *
pre_trim: C CLS_RBDFASTOFF(1) 1        ! fast release for dynamics
pre_trim: C CLS_RBDGFASTOFF(1) 200       ! speed up release for dynamics
pre_trim: *

clean_up: *
clean_up: C CLS_RBDFASTOFF(1) 0
clean_up: *
@
