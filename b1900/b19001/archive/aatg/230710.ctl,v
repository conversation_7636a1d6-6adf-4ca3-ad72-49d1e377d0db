head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.35;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Long Man Stability
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

LZROLHLD:     True
LZPITHLD:     True
LZPITIASHLD:  True
LZFNHLD:      True
LZRASSIST:    True
LZPASSIST:    True

target_bank_angles:  1.5   22.0    31.0   46.0
target_bank_tol:     1.0    1.0    1.0     1.0

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    220.00    20.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas                                -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
* Plot: ele_trim                            -20.0   20.0    20.0

Plot: ele_def       1.0                   -20.0   20.0    20.0
* Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
Plot: pitch_att                           -20.0   20.0    20.0

Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
* Plot: roll_rate                           -20.0   20.0    20.0

* Plot: yaw_rate                            -20.0   20.0    20.0
* Plot: alpha_true                          -20.0   20.0    20.0
* Plot: beta_true                           -20.0   20.0    20.0
* Plot: ax_corrctd                          -20.0   20.0    20.0

* Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
* Plot: pitch_acc                           -20.0   20.0    20.0
* Plot: roll_acc                            -20.0   20.0    20.0

* Plot: yaw_acc                             -20.0   20.0    20.0
* Plot: pla_l                               -20.0   20.0    20.0
* Plot: pla_r                               -20.0   20.0    20.0
* Plot: torque_l                            -20.0   20.0    20.0

* Plot: torque_r                            -20.0   20.0    20.0
Plot: fn_l                                -20.0   20.0    20.0
Plot: fn_r                                -20.0   20.0    20.0
* Plot: ail_trim                            -20.0   20.0    20.0

* Plot: pwf                                 -20.0   20.0    20.0
* Plot: pwp                                 -20.0   20.0    20.0
* Plot: ail_def_l                           -20.0   20.0    20.0
* Plot: ail_def_r                           -20.0   20.0    20.0

* Plot: rud_trim                            -20.0   20.0    20.0
* Plot: rud_def                             -20.0   20.0    20.0
* Plot: prpp_l                              -20.0   20.0    20.0
* Plot: prpf                                -20.0   20.0    20.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
