head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.36;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Stall Performance
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

LZHDGHLD:    True
LZROLHLD:    True
LZRASSIST:   True
LZYASSIST:   True
LZPITHLD:    True
LZPITIASHLD: True
LZPASSIST:   True
LZFNHLD:     True
LFXSTABT:    False
KDARATE:     4


*-----------------------------------------
* tol_time_slice: kcas       74.0 84.0
* tol_time_slice: roll_att   74.0 84.0
*-----------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     90.00    10.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plotf: kcas                             70.0      110.0     20.0
Plot: hp                              9000.0    10500.0    500.0
Plot: stall                             -1.0        2.0      1.0
Plotf: stall_buffet                     -0.5        1.5     0.5

Plot: alpha_true                         0.0       30.0     10.0
Plot-: ele_def                         -30.0        0.0     10.0
Plot: pcp                               -5.0       10.0      5.0
Plot: pitch_att                        -20.0       20.0     20.0

Plot: roll_att                         -40.0       80.0     40.0
Plotf: az_corrctd                      -1.2       -0.4      0.4

* Plot: beta_true                        -10.0        0.0      5.0
* Plot: ele_trim                          10.0       14.0      2.0
* Plot: pcf                             -150.0      300.0    150.0
* Plot: roll_att                         -40.0       80.0     40.0

* Plot: heading                            0.0      200.0    100.0
* Plot: pitch_rate                       -20.0       20.0     20.0
* Plot: roll_rate                        -10.0       10.0     10.0
* Plot: yaw_rate                         -10.0       10.0     10.0

* Plot: ax_corrctd                        -0.4        0.4      0.4
* Plot: ay_corrctd                        -0.4        0.4      0.4
* Plot: az_corrctd                       -1.2       -0.6      0.2
* Plot: pitch_acc                        -40.0       20.0     20.0

* Plot: roll_acc                         -50.0       50.0     50.0
* Plot: yaw_acc                          -20.0       20.0     20.0
* Plot: pla_l                              2.0        6.0      2.0
* Plot: pla_r                              2.0        6.0      2.0

* Plot: torque_l                        1000.0     2500.0    500.0
* Plot: torque_r                        1000.0     2500.0    500.0
* Plot: fn_l                             800.0     1400.0    200.0
* Plot: fn_r                             800.0     1400.0    200.0

* Plot: ail_trim                          -5.0       10.0      5.0
* Plot: pwf                              -50.0       50.0     50.0
* Plot: pwp                             -100.0      100.0    100.0
* Plot: ail_def_l                        -40.0       40.0     40.0

* Plot: ail_def_r                        -40.0       40.0     40.0
* Plot: rud_trim                           0.0       10.0      5.0
* Plot: rud_def                          -40.0        0.0     20.0
* Plot: prpp_l                             0.0       40.0     20.0

* Plot: prpf                             -50.0      100.0     50.0
* Plot: ins_vt_spd                     -5000.0     5000.0   5000.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0

* Plot_Line: PLOT RDATIME FSTALBUF                                                       0      64      4  'Time (sec)'      0       1       .1  'Stall Buffet'                                     !

Add_Criteria: stall
Add_Criteria: stall_buffet

Stall_Warn_Spd:   88.35
Stall_Buff_Spd:   86.22  ! Based on Ny break of Crit data
Stall_Speed_Crit: 86.16  ! Based on Nz break of Crit data at time 69.18
Stall_Speed_Sim:  85.16  ! Based on Nz and Alpha break of Sim data at time 75.58

@
