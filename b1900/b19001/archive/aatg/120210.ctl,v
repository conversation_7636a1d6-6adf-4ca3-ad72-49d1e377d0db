head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.31;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Low Speed Eng Out Char - Ground
*--------------------------------------------


*-----------------------------------------
*
*--------------------------------------------------

KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  0
FMNOWINDS:         False
default_lat_rates: False
LZPASSIST:         False
LZRASSIST:         False
LZTASSIST:         False
LZYASSIST:         False
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
LZNASSIST:         False
LZCRADALT:         False
LZPITALTHLD:       False
*LZT_HDG_RWY_HLD:   True
*LZHDGHLD:          True
LZRWYHLD:          False
LZBPHLD:           True
*RFXHDGCM:          286.0
RZCUDOT:           0.0
RZCQDS:            0.0
RZCRDS:            0.0
PRE_TRIM: FGYECG=0.0
PRE_TRIM: FGYEN=0.0
PRE_TRIM: FGYEL=0.0
PRE_TRIM: FGYER=0.0
PRE_TRIM: FGXECG=0.0
PRE_TRIM: FGXEN=0.0
PRE_TRIM: FGXEL=0.0
PRE_TRIM: FGXER=0.0
PRE_TRIM: FDEMENG(1)= 4.316
PRE_TRIM: FDEMENG(2)= 4.362
PRE_TRIM: FMAXPFOR = 350
PRE_TRIM: lbaf_atg_rudbst_arm = 1
*-----------------------------------------

*--------------------------------------------------
On_Ground:       True
IFXTRIM:         0
RDTRTIME: 10.0
Field_Elevation: 360.8
FHGGND: 360.8
FDEMALT: 360.8
*--------------------------------------------------

*--------------------------------------------------
FDEMTOEB:   1.20
FDEMTOER:   1.20
*FDEMENG(1): 4.316
*FDEMENG(2): 4.362
*RCXLONSK:   -0.50
*RCXLATSK:   0.100
RCXRUPED:   0.189
*--------------------------------------------------

*------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwf_f
Add_Criteria: nwp
Add_Criteria: nwd
Add_Criteria: prop_lvr_l
Add_Criteria: prop_lvr_r
Add_Criteria: wow_l
Add_Criteria: wow_r
Add_Criteria: wow_n
*Add_Criteria: gps_dist
*Add_Criteria: gps_y
*Add_Criteria: gps_cg_y
Add_Criteria: strut_n
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     65.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Controls_Plot: PLOT RDATIME FGXECG                     0      64      4  'Time (sec)'                      -100      50    25  'CG NORTH REF DIST (ft)'                            !
Controls_Plot: PLOT RDATIME FGXEN                      0      64      4   'Time (sec)'                      -100     100    50  'NOSE GEAR NORTH REF DIST (ft)'                     !
Controls_Plot: PLOT RDATIME FGXEL                      0      64      4   'Time (sec)'                      -100     100    50  'LEFT GEAR NORTH REF DIST (ft)'                     !
Controls_Plot: PLOT RDATIME FGXER                      0      64      4   'Time (sec)'                       -100     100    50  'RIGHT GEAR NORTH REF DIST (ft)'                    !
Controls_Plot: PLOT RDATIME FGYECG                     0      64      4  'Time (sec)'                       -100      50    25  'CG EAST REF DIST (ft)'                            !
Controls_Plot: PLOT RDATIME FGYEN                      0      64      4   'Time (sec)'                       -100     100    50  'NOSE GEAR EAST REF DIST (ft)'                     !
Controls_Plot: PLOT RDATIME FGYEL                      0      64      4   'Time (sec)'                       -100     100    50  'LEFT GEAR EAST REF DIST (ft)'                     !
Controls_Plot: PLOT RDATIME FGYER                      0      64      4   'Time (sec)'                       -100     100    50  'RIGHT GEAR EAST REF DIST (ft)'                    !
Controls_Plot: PLOT RDATIME RAXRWCL                    0      64      4   'Time (sec)'                       -100     100    50  'A/C DIST PERP TO RUNWAY :FT'                      !
Controls_Plot: PLOT RDATIME RZRWYX                     0      64      4   'Time (sec)'                       -100     100    50  'test x runway distance from ref'                  !
Controls_Plot: PLOT RDATIME RZRWYY                     0      64      4   'Time (sec)'                       -100     100    50  'test y runway distance from ref'                  !
Controls_Plot: PLOT RDATIME RFCDELCT                     0      64      4   'Time (sec)'                       -100     100    50  'avg. diff thrust coef.'                           !
Controls_Plot: PLOT RDATIME RFCLGALT                     0      64      4   'Time (sec)'                       -100     100    50  'gear height above ground'                         !
# Plot: latdev                              -20.0   20.0    20.0
Plot: kcas                                -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: ins_gnd_sp                          -20.0   20.0    20.0
Plot: gndspd_c                            -20.0   20.0    20.0
Plot: tbp_l                               -20.0   20.0    20.0
Plot: tbp_r                               -20.0   20.0    20.0
*Plot: tbf_l                               -20.0   20.0    20.0
*Plot: tbf_r                               -20.0   20.0    20.0
Plot: pla_l                               -20.0   20.0    20.0
Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
*Plot: fn_l                                -20.0   20.0    20.0
*Plot: fn_r                                -20.0   20.0    20.0
Plot: ax_corrctd                          -20.0   20.0    20.0
Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
Plot: pitch_att                           -20.0   20.0    20.0
Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
Plot: roll_rate                           -20.0   20.0    20.0
Plot: yaw_rate                            -20.0   20.0    20.0
Plot: alpha_true                          -20.0   20.0    20.0
Plot: beta_true                           -20.0   20.0    20.0
Plot: pitch_acc                           -20.0   20.0    20.0
Plot: roll_acc                            -20.0   20.0    20.0
Plot: yaw_acc                             -20.0   20.0    20.0
Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
*Plot: pcf                                 -20.0   20.0    20.0
Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
