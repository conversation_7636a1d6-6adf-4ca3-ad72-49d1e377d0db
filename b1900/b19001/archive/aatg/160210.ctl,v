head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.33;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Ground Decel - Free Roll
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  3
*-----------------------------------------

*-------------------------------------------------------
*-------------------------------------------------------

RZCTQ(1): 3880.0
RZCTQ(2): 3880.0

*--------------------------------------------------
On_Ground:         True
IFXTRIM:           0
Field_Elevation:   850.0
*--------------------------------------------------

default_lat_rates: False
LZTQHLD:           True
LZPASSIST:         True
LZRASSIST:         True
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
LZNASSIST:         True
LZT_HDG_RWY_HLD:   True
LZHDGHLD:          True
LZCRADALT:         True
LZPITALTHLD:       True
LZTASSIST:         False
LZYASSIST:         True
LZRWYHLD:          False
RZCUDOT:           0.0
RZCQDS:            0.0
*----------------------------------
LZTQHLD: True
POST_TRIM: LZTQHLD=F
*----------------------------------

*------------------------------------------------------
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     17.50     2.00
Ignore_Iter_Rate: True
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
*Plot: ins_gnd_sp                          -20.0   20.0    20.0
Plot: pla_l                                 2.0    5.6     0.5
Plot: pla_r                                 2.0    5.6     0.5
Plot: n1_l                                 65.0  100.0     5.0
Plot: n1_r                                 65.0  100.0     5.0
*Plot: torque_l                              0.0 4000.0   500.0
*Plot: torque_r                              0.0 4000.0   500.0
*Plot: gndspd_c                             65.0  105.0     5.0
*Plot: tbp_l                               -20.0   20.0    20.0
*Plot: tbp_r                               -20.0   20.0    20.0
*Plot: tbf_l                               -20.0   20.0    20.0
*Plot: tbf_r                               -20.0   20.0    20.0
*Plot: radalt                              -20.0   20.0    20.0
*Plot: kcas                                 75.0  115.0     5.0
*Plot: hp                                  830.0  870.0    10.0
*Plot: ins_vt_spd                          -20.0   20.0    20.0
*Plot: fn_l                              -1500.0 3000.0   500.0
*Plot: fn_r                              -1500.0 3000.0   500.0
*Plot: ax_corrctd                          -20.0   20.0    20.0
*Plot: ay_corrctd                          -20.0   20.0    20.0
*Plot: az_corrctd                          -20.0   20.0    20.0
*Plot: pitch_att                           -20.0   20.0    20.0
*Plot: roll_att                            -20.0   20.0    20.0
*Plot: heading                             -20.0   20.0    20.0
*Plot: pitch_rate                          -20.0   20.0    20.0
*Plot: roll_rate                           -20.0   20.0    20.0
*Plot: yaw_rate                            -20.0   20.0    20.0
*Plot: alpha_true                          -20.0   20.0    20.0
*Plot: beta_true                           -20.0   20.0    20.0
*Plot: pitch_acc                           -20.0   20.0    20.0
*Plot: roll_acc                            -20.0   20.0    20.0
*Plot: yaw_acc                             -20.0   20.0    20.0
*Plot: ele_trim                            -20.0   20.0    20.0
*Plot: ele_def                             -20.0   20.0    20.0
*Plot: pcp                                 -20.0   20.0    20.0
*Plot: pcf                                 -20.0   20.0    20.0
*Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
*Plot: pwp                                 -20.0   20.0    20.0
*Plot: ail_def_l                           -20.0   20.0    20.0
*Plot: ail_def_r                           -20.0   20.0    20.0
*Plot: rud_trim                            -20.0   20.0    20.0
*Plot: rud_def                             -20.0   20.0    20.0
*Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
