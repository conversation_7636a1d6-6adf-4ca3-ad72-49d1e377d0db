head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.26;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Ground Acceleration
*--------------------------------------------

*LFI_Table:    PCP         1.0     0.175     0.206186  0.0
*LFI_Table:    PWP         1.0     0.175     0.010870  0.0
*LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
* LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    FN_L        1.0     0.5       1.0       0.0
LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
LFI_Table:    WNDVEL_CAL  1.0     0.000     1.00      0.0
LFI_Table:    WNDDIR_CAL  1.0     0.000     1.00      0.0
*LFI_Table:    TBP_L       1.0     0.0       1.0      0.0
*LFI_Table:    TBP_R       1.0     0.0       1.0      0.0
* LFI_Table:    TBP_L    1.0     0.0       29.938843    0.0
* LFI_Table:    TBP_R    1.0     0.0       29.938843    0.0
LFI_Table:    BRK_PX_L    1.0     0.350     1.0      0.0
LFI_Table:    BRK_PX_R    1.0     0.350     1.0      0.0

*-----------------------------------------

*-----------------------------------------
LFI_Line: LFI    RDATIME  KAXPMODE
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000 
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000 
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 8.9    0
LFI_Line: 8.901  4
LFI_Line: 500.0  4
LFI_Line: END
LFI_Line: *
*
*-----------------------------------------
KAXPMODE:  0
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  0
*-----------------------------------------

*--------------------------------------------------
On_Ground:         True
IFXTRIM:           0
Field_Elevation:   833.9
RTCVG:             0.0
* SET_WINDS: True
* CMD_WIND_SPEED:  20.56
* CMD_WIND_DIR:    176.65
FASBYP:            True   ! Turn Anti-Skid ON
QTG_Test_Airport: Takeoff  834  0.0   0.0   0.0  0.0  180

*--------------------------------------------------

default_lat_rates: False
LZBPHLD:           True
LZTQHLD:           True
LZPASSIST:         True
LZRASSIST:         True
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
* LZNASSIST:         True
* LZT_HDG_RWY_HLD:   True
LZHDGHLD:          True
* LZCRADALT:         True
* LZPITALTHLD:       True
LZTASSIST:         False
* LZYASSIST:         True
* LZRWYHLD:          True
RZCUDOT:           0.0
RZCQDS:            0.0
*----------------------------------

*--------------------------------------------------
Speed_Signal:     ins_gnd_sp
Plot-: gndspd_c                         -50.0      150.0     50.0
Init_Speed:       5.0
Init_Speed_Mode:  Increase
Final_Speed:      100.0
Final_Speed_Mode: Increase
*--------------------------------------------------

*----------------------------------
* FDEMENG(1): 4.85
* FDEMENG(2): 4.85
* RCXLONSK:   0.000
* RCXLATSK:   0.000
* RCXRUPED:   0.000

*------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: tbf_l
Add_Criteria: tbf_r
Add_Criteria: mg_spd
Add_Criteria: ins_gnd_dist
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     18.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
* Plot-: ins_gnd_sp                      -50.0      150.0     50.0
Plot-: gndspd_c                         -50.0      150.0     50.0
Plot-: ins_gnd_dist                   -500.0     1500.0    500.0

Plot: brk_px_l                        -500.0     1000.0    500.0
Plot: brk_px_r                        -400.0      800.0    400.0
Plot: tbp_l                             -2.0        2.0      2.0
Plot: tbp_r                             -2.0        2.0      2.0

Plot: tbf_l                           -100.0      200.0    100.0
Plot: tbf_r                           -100.0      200.0    100.0
Plot: torque_l                        3000.0     4500.0    500.0
Plot: torque_r                        3000.0     4500.0    500.0

Plot: fn_l                            2500.0     3500.0    500.0
Plot: fn_r                            2500.0     3500.0    500.0
Plot: pla_l                              2.0        6.0      2.0
Plot: pla_r                              2.0        6.0      2.0

Plot: ax_corrctd                        -2.0        2.0      2.0
Plot: kcas                               0.0      200.0    100.0
Plot: ins_vt_spd                      -500.0      500.0    500.0
Plot: gndspd_c                           0.0      200.0    100.0

Plot: ay_corrctd                        -2.0        2.0      2.0
Plot: az_corrctd                        -2.0        2.0      2.0
Plot: pitch_att                         -5.0        5.0      5.0
Plot: roll_att                          -5.0        5.0      5.0

Plot: heading                          160.0      190.0     10.0
Plot: pitch_rate                        -4.0        4.0      4.0
Plot: roll_rate                         -2.0        2.0      2.0
Plot: yaw_rate                         -10.0       10.0     10.0

Plot: alpha_true                        -5.0        5.0      5.0
Plot: beta_true                         -5.0        5.0      5.0

Plot: pitch_acc                        -10.0       10.0     10.0
Plot: roll_acc                         -10.0       10.0     10.0
Plot: yaw_acc                          -40.0       40.0     40.0
Plot: ele_trim                           2.0        6.0      2.0

Plot: ele_def                          -20.0       20.0     20.0
Plot: pcp                               -5.0        5.0      5.0
Plot: pcf                             -200.0      200.0    200.0
Plot: ail_trim                          -5.0        5.0      5.0

Plot: pwf                              -20.0       20.0     20.0
Plot: pwp                                0.0       20.0     10.0
Plot: ail_def_l                         -5.0       10.0      5.0
Plot: ail_def_r                          0.0       10.0      5.0

Plot: rud_trim                          -5.0        5.0      5.0
Plot: rud_def                          -40.0       40.0     40.0
Plot: prpp_l                           -40.0       40.0     40.0
Plot: prpf                            -400.0      400.0    400.0

Plot: ktas_meas                          0.0      200.0    100.0
Plot: mg_spd                             0.0     1600.0    800.0

#            Var       x_off  y_off     y_mult
* Crit_Offset: ins_gnd_sp  0   -1.875      1
* Crit_Offset: tbp_l       0      0     29.938843
* Crit_Offset: tbp_r       0      0     29.938843
Crit_Offset: ins_gnd_dist  0   -69.752   1
@
