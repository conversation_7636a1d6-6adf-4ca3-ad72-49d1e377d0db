head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.34;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* <PERSON>yn <PERSON> to Yaw Input
*--------------------------------------------


FDEMRTAB: 0.383
LFXRUDT: False


*-----------------------------------------
KAXPMODE:  3
KAXRMODE:  3
KAXYMODE:  3
*-----------------------------------------

ZERO_RATES:  True

*-----------------------------------------------------
#           Min       <PERSON>
X_Scale:   0.00     32.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
*Plot: rud_trim                            -20.0   20.0    20.0
*Plot: rud_def                             -20.0   20.0    20.0
Plot-: prpp_l                           -5.0       20.0     5.0
*Plot: prpf                                -20.0   20.0    20.0


pre_trim: *
pre_trim: C CLS_RBDFASTOFF(3) 1        ! fast release for dynamics
pre_trim: C CLS_RBDGFASTOFF(3) 100       ! speed up release for dynamics
pre_trim: *

clean_up: *
clean_up: C CLS_RBDFASTOFF(3) 0
clean_up: *
@
