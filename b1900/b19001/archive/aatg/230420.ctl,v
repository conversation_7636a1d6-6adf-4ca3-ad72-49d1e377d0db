head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.35;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Gear Change Dynamics
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

RZCUDOT:   0.0
RZCQDS:    0.0
LZROLHLD:  True
LZFNHLD:   True
Pre_Trim: RFNMIN  = 2.8
Pre_Trim: RFNMINT = 2.8

*-----------------------------------------------------
#           Min       <PERSON>    Delta
X_Scale:   0.00     40.00    4.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: gear_pos                          -1.0        2.0      1.0
Plot: lg_dn_lk                          -1.0        2.0      1.0
Plot: lg_up_lk                          -1.0        2.0      1.0
Plot: kcas            3.0              120.0      140.0     10.0

Plot: hp            100.0             4500.0     6000.0    500.0
*Plot-: ins_vt_spd                    -2000.0     1000.0   1000.0
Plot: pitch_att       1.5    20.0       -5.0        5.0      5.0
Plot: roll_att                          -5.0        5.0      5.0

*Plot: heading                           10.0       30.0     10.0
*Plot: pitch_rate                        -2.0        2.0      2.0
*Plot: roll_rate                         -2.0        2.0      2.0
*Plot: yaw_rate                          -2.0        2.0      2.0

*Plot: alpha_true                        -5.0       10.0      5.0
*Plot: beta_true                         -5.0        5.0      5.0
*Plot: ax_corrctd                        -0.1        0.1      0.1
*Plot: ay_corrctd                        -0.1        0.1      0.1

*Plot: az_corrctd                        -2.0        2.0      2.0
*Plot: pitch_acc                         -2.0        2.0      2.0
*Plot: roll_acc                          -5.0        5.0      5.0
*Plot: yaw_acc                           -2.0        4.0      2.0

*Plot-: pla_l                             3.0        5.0      1.0
*Plot-: pla_r                             3.0        5.0      1.0
* Plot: torque_l                        1000.0     2000.0    500.0
* Plot: torque_r                        1000.0     2000.0    500.0

*Plot: fn_l                             600.0     1000.0    200.0
*Plot: fn_r                             600.0     1000.0    200.0
Plot: ele_trim                           2.0        6.0      2.0
Plot: ele_def                           -5.0        5.0      5.0

Plot: pcp                               -5.0        5.0      5.0
*Plot: pcf                              -10.0       10.0     10.0
*Plot: ail_trim                          -5.0       10.0      5.0
*Plot: pwf                              -20.0       20.0     20.0

*Plot: pwp                              -10.0       20.0     10.0
*Plot: ail_def_l                         -5.0       10.0      5.0
*Plot: ail_def_r                          0.0       10.0      5.0
*Plot: rud_trim                          -5.0        5.0      5.0

*Plot: rud_def                            0.0       10.0      5.0
*Plot: prpp_l                            -4.0        2.0      2.0
*Plot: prpf                             -20.0       20.0     20.0
* Plot: wnddir_cal                       120.0      180.0     20.0

* Plot: wndvel_cal                         2.0        4.0      1.0

Add_Criteria: lg_dn_lk
Add_Criteria: lg_up_lk
Add_Criteria: rg_dn_lk
Add_Criteria: rg_up_lk
Add_Criteria: ng_dn_lk
Add_Criteria: ng_up_lk
@
