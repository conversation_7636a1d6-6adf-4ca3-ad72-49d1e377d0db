head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.26;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Ground Effect
*--------------------------------------------

LFI_Table:    PLA_L       1.0     0.000     1.0       0.0  
LFI_Table:    PLA_R       1.0     0.000     1.0       0.0  
LFI_Table:    ROC_HP      1.0     0.000     1.0       0.0
LFI_Table:    RUD_TRIM    1.0     0.000     1.0       0.0
LFI_Table:    ELE_TRIM    1.0     0.000     1.0       0.0
LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.175     0.010870  0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
* LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    FN_L        1.0     0.5       1.0       0.0
LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
LFI_Table:    WNDVEL_CAL  1.0     0.000     1.00      0.0
LFI_Table:    WNDDIR_CAL  1.0     0.000     1.00      0.0
*----------------------------------------------

LFI_LINE: *
LFI_LINE: LFI    RDATIME  LZCRADALT
LFI_LINE: ABS    F
LFI_LINE: XMULT  1.00000
LFI_LINE: XOFF   0.00000
LFI_LINE: YMULT  1.00000
LFI_LINE: YOFF   0.00000
LFI_LINE: 0    0.00000
LFI_LINE: 15   1.00000
LFI_LINE: 34   1.00000
LFI_LINE: END
LFI_LINE: *
LFI_LINE: *LFI    RDATIME  LZPITALTHLD
LFI_LINE: *ABS    F
LFI_LINE: *XMULT  1.00000
LFI_LINE: *XOFF   0.00000
LFI_LINE: *YMULT  1.00000
LFI_LINE: *YOFF   0.00000
LFI_LINE: *0    1.00000
LFI_LINE: *14.8 0.00000
LFI_LINE: *34   0.00000
LFI_LINE: *END
LFI_LINE: *

*----------------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*----------------------------------------------

*----------------------------------------------
IFXTRIM:     3
FMNOWINDS:   False
default_lat_rates: False
RZCQDS:      0.0
RZCPDS:      0.0
RZCRDS:      0.0
LZROLHLD:    True
LZPITHLD:    True
LZHDGHLD:    True
LZPASSIST:   True
LZRASSIST:   True
LZYASSIST:   True
LZTQHLD:     True
RZCUDOT:     0.0
RZCROC:     -381.7   !-523.7        # -381.7
LZFLARE:     False
RZFLAREH:    215
Field_Elevation: 802.05
*RDATRMLM(6): 0.022
LZCRADALT:   True
LZPITALTHLD: True
LDATRMON(8): 0
*----------------------------------------------

*----------------------------------------------
Pre_Trim: RZPITALTK=0.15
*Pre_Trim: RZPGAIN(1)=0.03
*Pre_Trim: RZDGAIN(1)=6.0
*----------------------------------------------

Radalt_Height: 25.0
Add_Criteria: comp_radalt

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    240.00    20.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: comp_radalt     5.0       10.0      -20.0   20.0    20.0
Plot: radalt          5.0       10.0      -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: alpha_true      1.0                 -20.0   20.0    20.0
Plot: beta_true                           -20.0   20.0    20.0
Plot: pitch_att       1.0                 -20.0   20.0    20.0
Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def         1.0                 -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
Plot: roll_rate                           -20.0   20.0    20.0
Plot: yaw_rate                            -20.0   20.0    20.0
Plot: pla_l                               -20.0   20.0    20.0
Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
Plot: fn_l                                -20.0   20.0    20.0
Plot: fn_r                                -20.0   20.0    20.0
Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
Plot: prpf                                -20.0   20.0    20.0
Plot: ail_trim                            -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: pitch_acc                           -20.0   20.0    20.0
Plot: roll_acc                            -20.0   20.0    20.0
Plot: yaw_acc                             -20.0   20.0    20.0
Plot: ax_corrctd                          -20.0   20.0    20.0
Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
@
