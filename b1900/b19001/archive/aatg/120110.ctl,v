head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.31;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Ground Acceleration
*--------------------------------------------


*-----------------------------------------

*-----------------------------------------
*
*-----------------------------------------
KAXPMODE:  0
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  0
*-----------------------------------------

*--------------------------------------------------
On_Ground:         True
IFXTRIM:           0
Field_Elevation:   833.9
RTCVG:             0.0
* SET_WINDS: True
* CMD_WIND_SPEED:  20.56
* CMD_WIND_DIR:    176.65
FASBYP:            True   ! Turn Anti-Skid ON
*--------------------------------------------------

default_lat_rates: False
LZBPHLD:           True
LZTQHLD:           True
LZPASSIST:         True
LZRASSIST:         True
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
* LZNASSIST:         True
* LZT_HDG_RWY_HLD:   True
LZHDGHLD:          True
* LZCRADALT:         True
* LZPITALTHLD:       True
LZTASSIST:         False
* LZYASSIST:         True
* LZRWYHLD:          True
RZCUDOT:           0.0
RZCQDS:            0.0
*----------------------------------

*--------------------------------------------------
Speed_Signal:     ins_gnd_sp
Init_Speed:       0.1
Init_Speed_Mode:  Increase
Final_Speed:      100.0
Final_Speed_Mode: Increase
*--------------------------------------------------

*----------------------------------
* FDEMENG(1): 4.85
* FDEMENG(2): 4.85
* RCXLONSK:   0.000
* RCXLATSK:   0.000
* RCXRUPED:   0.000

*------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: tbf_l
Add_Criteria: tbf_r
Add_Criteria: mg_spd
Add_Criteria: ins_gnd_dist
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     18.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: ins_gnd_sp                          -20.0   20.0    20.0
Plot: ins_gnd_dist                        -20.0   20.0    20.0
* Plot: gndspd_c                            -20.0   20.0    20.0
* Plot: kcas                                -20.0   20.0    20.0
* Plot: hp                                  -20.0   20.0    20.0
* Plot: ins_vt_spd                          -20.0   20.0    20.0
* Plot: radalt                              -20.0   20.0    20.0
* Plot: tbp_l                               -20.0   20.0    20.0
* Plot: tbp_r                               -20.0   20.0    20.0
Plot: brk_px_l                            -20.0   20.0    20.0
Plot: brk_px_r                            -20.0   20.0    20.0
* Plot: tbf_l                               -20.0   20.0    20.0
* Plot: tbf_r                               -20.0   20.0    20.0
Plot: pitch_att                           -20.0   20.0    20.0
* Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
* Plot: alpha_true                          -20.0   20.0    20.0
* Plot: beta_true                           -20.0   20.0    20.0
* Plot: pitch_rate                          -20.0   20.0    20.0
* Plot: roll_rate                           -20.0   20.0    20.0
* Plot: yaw_rate                            -20.0   20.0    20.0
* Plot: ele_trim                            -20.0   20.0    20.0
* Plot: ele_def                             -20.0   20.0    20.0
* Plot: pcp                                 -20.0   20.0    20.0
* Plot: pcf                                 -20.0   20.0    20.0
* Plot: ail_trim                            -20.0   20.0    20.0
* Plot: pwf                                 -20.0   20.0    20.0
* Plot: pwp                                 -20.0   20.0    20.0
* Plot: ail_def_l                           -20.0   20.0    20.0
* Plot: ail_def_r                           -20.0   20.0    20.0
* Plot: rud_trim                            -20.0   20.0    20.0
* Plot: rud_def                             -20.0   20.0    20.0
* Plot: prpp_l                              -20.0   20.0    20.0
* Plot: prpf                                -20.0   20.0    20.0
* Plot: ax_corrctd                          -20.0   20.0    20.0
* Plot: ay_corrctd                          -20.0   20.0    20.0
* Plot: az_corrctd                          -20.0   20.0    20.0
* Plot: pitch_acc                           -20.0   20.0    20.0
* Plot: roll_acc                            -20.0   20.0    20.0
* Plot: yaw_acc                             -20.0   20.0    20.0
* Plot: pla_l                               -20.0   20.0    20.0
* Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
* Plot: fn_l                                -20.0   20.0    20.0
* Plot: fn_r                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0


#            Var       x_off  y_off     y_mult
* Crit_Offset: ins_gnd_sp  0   -1.875      1
Crit_Offset: ins_gnd_dist  0   -69.752   1
@
