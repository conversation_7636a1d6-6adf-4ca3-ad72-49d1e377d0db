head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.32;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* All Engine Climb
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

*--------------------------------------------------
IFXTRIM:  2
Climb_Test: True
*--------------------------------------------------

*--------------------------------------------------
zero_rates:  True
LZROLHLD:    True
LZPITHLD:    True
LZHDGHLD:    True
LZSSLIP:     True
LZYASSIST:   True
LZPASSIST:   True
LZRASSIST:   True
LZPITIASHLD: True
LZFNHLD:     True
RTCVC: 136.5
RZCUDOT: 0.45
*--------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    120.00    20.00
Ignore_Iter_Rate: True
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas            3.0              120.0      140.0     10.0
Plot: hp                                 0.0    15000.0   5000.0
Plot: ins_vt_spd                      1600.0     3200.0    800.0
*Plot: pitch_att                         10.0       20.0      5.0

*Plot: torque_l                        3000.0     4000.0    500.0
*Plot: torque_r                        3000.0     4500.0    500.0
*Plot: fn_l                            1600.0     2400.0    400.0
*Plot: fn_r                            1800.0     2520.0    360.0

*Plot: pitch_rate                        -2.0        2.0      2.0
*Plot: pitch_acc                         -5.0        5.0      5.0
Plot: pla_l                              4.0        8.0      2.0
Plot: pla_r                              2.0        8.0      2.0

*Plot: ele_trim                           2.0        6.0      2.0
*Plot: ele_def                          -10.0        5.0      5.0
*Plot: pcp                               -5.0        5.0      5.0
*Plot: pcf                              -20.0       20.0     20.0

*Plot: roll_att                          -5.0        5.0      5.0
*Plot: heading                          310.0      340.0     10.0
*Plot: alpha_true                         0.0       10.0      5.0
*Plot: beta_true                         -5.0        5.0      5.0

*Plot: ail_trim                           0.0       10.0      5.0
*Plot: pwf                              -20.0       20.0     20.0
*Plot: pwp                              -10.0       20.0     10.0
*Plot: ail_def_l                         -5.0       10.0      5.0

*Plot: rud_trim                          -5.0        5.0      5.0
*Plot: rud_def                           -5.0       10.0      5.0
*Plot: prpp_l                            -2.0        2.0      2.0
*Plot: prpf                             -20.0       20.0     20.0

*Plot: roll_acc                          -2.0        2.0      2.0
*Plot: yaw_acc                           -2.0        2.0      2.0
*Plot: roll_rate                         -2.0        2.0      2.0
*Plot: yaw_rate                          -2.0        2.0      2.0

*Plot: ax_corrctd                        -2.0        2.0      2.0
*Plot: ay_corrctd                        -0.1        0.1      0.1
*Plot: az_corrctd                        -2.0        2.0      2.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
