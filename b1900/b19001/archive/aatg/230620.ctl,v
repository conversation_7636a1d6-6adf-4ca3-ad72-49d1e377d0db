head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.35;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Long Trim
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

IFXTRIM:     3
* ZERO_RATES:  True
* RZCUDOT: -0.106

*-----------------------------------------------------
#           Min       <PERSON>    Delta
X_Scale:   0.00     10.00     1.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas                             105.0      115.0      5.0
Plot: hp                              4500.0     5500.0    500.0
Plot: alpha_true                        -5.0        5.0      5.0
Plot-: pitch_att      1.0               -4.0        8.0      4.0

Plot-: ele_def        1.0               -8.0        4.0      4.0
Plot: ele_trim                           4.0       10.0      2.0
Plot: pcp                               -5.0        5.0      5.0
Plot: pcf                              -10.0       10.0     10.0

Plot: fn_l            0.0     5.0      800.0     1200.0    200.0
Plot: fn_r            0.0     5.0      800.0     1200.0    200.0

* Plot: torque_l                        1000.0     2000.0    500.0
* Plot: torque_r                        1000.0     2000.0    500.0

* Plot: pitch_rate                        -2.0        2.0      2.0
* Plot: roll_rate                         -2.0        2.0      2.0
* Plot: pla_l                              2.0        6.0      2.0
* Plot: pla_r                              2.0        6.0      2.0

* Plot: roll_att                          -5.0        5.0      5.0
* Plot: heading                          340.0      360.0     10.0
* Plot: beta_true                         -5.0        5.0      5.0
* Plot: ail_trim                          -5.0        5.0      5.0

* Plot: ax_corrctd                        -0.1        0.1      0.1
* Plot: ay_corrctd                        -0.1        0.1      0.1
* Plot: az_corrctd                        -2.0        2.0      2.0
* Plot: yaw_rate                          -2.0        2.0      2.0

* Plot: pitch_acc                         -2.0        2.0      2.0
* Plot: roll_acc                          -4.0        4.0      4.0
* Plot: yaw_acc                           -2.0        2.0      2.0
* Plot: pwf                              -20.0       20.0     20.0

* Plot: pwp                              -10.0       20.0     10.0
* Plot: ail_def_l                          0.0       10.0      5.0
* Plot: ail_def_r                          0.0       10.0      5.0
* Plot: rud_trim                          -5.0        5.0      5.0

* Plot: rud_def                           -5.0        5.0      5.0
* Plot: prpp_l                            -2.0        2.0      2.0
* Plot: prpf                             -20.0       20.0     20.0

* Plot: wnddir_cal                       220.0      280.0     20.0
* Plot: wndvel_cal                       -10.0       10.0     10.0

@
