head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.37;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Engine Out Landing
*--------------------------------------------
LFI_Rate_Hz:  10
*----------------------------------------------

*----------------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
Pre_Trim: RZPGAIN(7) = 0.04
Pre_Trim: RZDGAIN(7) = 2.00
Pre_Trim: RZIGAIN(7) = 2.00
Pre_Trim: RZIDGAIN(7) = 3.00
Pre_Trim: RZIGAIN(10) = 5.00
Pre_Trim: RZPGAIN(10) = 0.01
Pre_Trim: RZDGAIN(10) = 2.00
Pre_Trim: RZFLAREK = 0.0055
FDEMENG(1): 4.20
FDEMENG(2): 2.98
RZCTQ(1): 2290.0   ! torque
RZCTQ(2):   49.3   ! torque
LFXSTABT: True

*----------------------------------------------

*----------------------------------------------

*----------------------------------

*Post_Trim: WAIT 36 RDATIME gt 32
*Post_Trim: WAIT 10 FBWOW eq 1
*Post_Trim: LZROLHLD=T
*Post_Trim: WAIT 5 RDATIME gt 36
*Post_Trim: LZPITHLD=T

*----------------------------------------------
IFXTRIM:     2
FMNOWINDS:   False
default_lat_rates: False
RZCQDS:      0.0
RZCPDS:      0.0
RZCRDS:      0.0
LZROCHLD:    True
LFXSTABT:    True
LZROLHLD:    True
LZPITHLD:    True
LZHDGHLD:    True
LZPASSIST:   True
LZRASSIST:   True
LZYASSIST:   True
LZTQHLD:     True
RZCUDOT:     0.00
RZCROC:      -900
LZFLARE:     True
RZFLAREH:    250
Field_Elevation: 344.00
LZCRADALT:   True
LZPITALTHLD: True
LZTRIM_ROC_UDOT: True
LDATRMON(8): 0
RFXPITCM:    2.05
*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    110.00    10.00
*-----------------------------------------------------

*-----------------------------------------------------
Add_Criteria: wow_l
Add_Criteria: wow_r
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
* PLOT_LINE:  PLOT RDATIME RAXRDALT             '../../tst/critdata/0220440comptd_radalt'        0.00       110.00      10.00 'Time (sec)'      -20.0       20.0        20.0   'Computed Radio Altitude (ft)'                              !
*Plot: comp_radalt    10.0       10.0      -20.0   20.0    20.0
Plot: radalt         10.0       10.0      -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
*Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: alpha_true      1.5                 -20.0   20.0    20.0
Plot: beta_true       2.0                 -20.0   20.0    20.0
Plot: pitch_att       1.5                 -20.0   20.0    20.0
Plot: roll_att        2.0                 -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
*Plot: pitch_rate                          -20.0   20.0    20.0
*Plot: roll_rate                           -20.0   20.0    20.0
*Plot: pla_l                               -20.0   20.0    20.0
*Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
*Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
*Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
*Plot: ail_trim                            -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: tbp_l                               -20.0   20.0    20.0
Plot: tbp_r                               -20.0   20.0    20.0
*Plot: tbf_l                               -20.0   20.0    20.0
*Plot: tbf_r                               -20.0   20.0    20.0
*Plot: pitch_acc                           -20.0   20.0    20.0
*Plot: roll_acc                            -20.0   20.0    20.0
*Plot: yaw_acc                             -20.0   20.0    20.0
*Plot: ax_corrctd                          -20.0   20.0    20.0
*Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
*Plot: pla_l                               -20.0   20.0    20.0
*Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
*Plot: fn_l                                -20.0   20.0    20.0
*Plot: fn_r                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0

@
