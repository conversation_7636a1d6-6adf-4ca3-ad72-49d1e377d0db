head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.35;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Gear Change Dynamics
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

RZCUDOT:   0.0
IFXTRIM:   2
LZROLHLD:  True
LZFNHLD:   True
Pre_Trim: RFNMIN  = 2.8
Pre_Trim: RFNMINT = 2.8

Post_trim: LZFNHLD=F

*-----------------------------------------------------
#           Min       <PERSON>
X_Scale:   0.00     30.00    2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: gear_pos                          -1.0        2.0      1.0
Plot: lg_dn_lk                          -1.0        2.0      1.0
Plot: lg_up_lk                          -1.0        2.0      1.0
Plot: kcas            3.0              100.0      120.0     10.0

Plot: hp            100.0             4500.0     5500.0    500.0
*Plot-: ins_vt_spd                     -1000.0     2000.0   1000.0
Plot: pitch_att       1.5    20.0        0.0       15.0      5.0
Plot: roll_att                          -5.0        5.0      5.0

*Plot: heading                          150.0      170.0     10.0
*Plot: pitch_rate                        -2.0        2.0      2.0
*Plot: roll_rate                         -2.0        2.0      2.0
*Plot: yaw_rate                          -2.0        2.0      2.0

*Plot: alpha_true                         0.0       10.0      5.0
*Plot: beta_true                         -5.0        5.0      5.0
*Plot: ax_corrctd                        -0.4        0.4      0.4
*Plot: ay_corrctd                        -0.1        0.1      0.1

*Plot: az_corrctd                        -2.0        2.0      2.0
*Plot: pitch_acc                         -2.0        2.0      2.0
*Plot: roll_acc                          -5.0        5.0      5.0
*Plot: yaw_acc                           -2.0        2.0      2.0

*Plot-: pla_l                             3.0        5.0      1.0
*Plot-: pla_r                             3.0        5.0      1.0
* Plot: torque_l                        1000.0     2000.0    500.0
* Plot: torque_r                        1000.0     2000.0    500.0

*Plot: fn_l                             600.0     1200.0    200.0
*Plot: fn_r                             600.0     1200.0    200.0
Plot: ele_trim                           4.0        8.0      2.0
Plot: ele_def                          -10.0        0.0      5.0

Plot: pcp                               -5.0        5.0      5.0
*Plot: pcf                              -20.0       20.0     20.0
*Plot: ail_trim                           0.0       10.0      5.0
*Plot: pwf                              -20.0       20.0     20.0

*Plot: pwp                              -10.0       20.0     10.0
*Plot: ail_def_l                         -5.0       10.0      5.0
*Plot: ail_def_r                          0.0       10.0      5.0
*Plot: rud_trim                          -5.0        5.0      5.0

*Plot: rud_def                           -5.0        5.0      5.0
*Plot: prpp_l                            -2.0        4.0      2.0
*Plot: prpf                             -20.0       20.0     20.0
* Plot: wnddir_cal                       160.0      176.0      8.0

* Plot: wndvel_cal                         7.0        9.0      1.0

Add_Criteria: lg_dn_lk
Add_Criteria: lg_up_lk
Add_Criteria: rg_dn_lk
Add_Criteria: rg_up_lk
Add_Criteria: ng_dn_lk
Add_Criteria: ng_up_lk
@
