head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.41.54;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Crosswind Takeoff
*--------------------------------------------

LFI_Table:    TBP_L       1.0     0.0       1.0       0.0
LFI_Table:    TBP_R       1.0     0.0       1.0       0.0
*LFI_Table:    PCP         1.0     0.175     0.206186  0.0
*LFI_Table:    PWP         1.0     0.175     0.010870  0.0
*LFI_Table:    PRPP_L      1.0     0.175    -0.303030  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0 T
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0      0.0
LFI_Table:    RADALT      1.0     0.4       1.0      0.0
LFI_Table:    KCAS        1.0     0.4       1.0      0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0      0.0 T
LFI_Table:    ROLL_ATT    1.0     0.7       1.0      0.0 T
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0      0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0      0.0
*LFI_Table:    FN_L        1.0     0.5       1.0      0.0
*LFI_Table:    FN_R        1.0     0.5       1.0      0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0      0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0      0.0
LFI_Table:    ELE_TRIM    1.0     0.0       1.0      0.0
LFI_Table:    RUD_TRIM    1.0     0.0       1.0      0.0
LFI_Table:    WNDVEL_CAL  1.0     0.0       1.0      0.0
LFI_Table:    WNDDIR_CAL  1.0     0.0      -1.0    180.0
*LFI_Table:    NWD         1.0     0.0       1.0      0.0
LFI_Table:    PLA_L       1.0     0.0       1.0      0.0
LFI_Table:    PLA_R       1.0     0.0       1.0      0.0
LFI_Table:    BRK_PX_L    1.0     0.175     1.0      0.0
LFI_Table:    BRK_PX_R    1.0     0.175     1.0      0.0

*--------------------------------------------------
LFI_Line: *
LFI_Line: LFI    RDATIME  KAXYMODE
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000 
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000 
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 7.3    0
LFI_Line: 7.301  4
LFI_Line: 500.0  4
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  KAXPMODE
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000 
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000 
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 12.1   0
LFI_Line: 12.101 4
LFI_Line: 500.0  4
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZPITHLD
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 24.0   0
LFI_Line: 24.001 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZROLHLD
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 22.0   0
LFI_Line: 22.001 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
*-------------------------------------------------------

*-------------------------------------------------------
KAXPMODE:  0
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  0
*-------------------------------------------------------

*-------------------------------------------------------
FMNOWINDS:         False
default_lat_rates: False
LZBPHLD:           True
LZTQHLD:           False
LZPASSIST:         True
LZRASSIST:         True
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
LZNASSIST:         False
LZHDGHLD:          True
LZCRADALT:         True
LZPITALTHLD:       True
LZTASSIST:         False
LZYASSIST:         True
LZRWYHLD:          False
RZCUDOT:           0.0
RZCQDS:            0.0
*FDEMNW:            19.0
*-------------------------------------------------------

*-------------------------------------------------------
On_Ground:       True
IFXTRIM:         0
Field_Elevation: 400.1
SET_WINDS: True
CMD_WIND_SPEED:  21.63   
CMD_WIND_DIR:    85.09
PRE_TRIM: RZPGAIN(7)=0.06
PRE_TRIM: RZIGAIN(7)=2.00
PRE_TRIM: RZDGAIN(7)=2.00
QTG_Test_Airport: Takeoff  400  0.0   0.0   0.0  0.0   89.5

*
*-------------------------------------------------------

*-------------------------------------------------------
RCXLONSK:   -0.51
RCXLATSK:   0.331
RCXRUPED:   0.3033
*-------------------------------------------------------

*-------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: windvel
Add_Criteria: winddir
CRIT_OFFSET: WNDDIR_CAL 0.0 180.0 -1.0
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     65.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: radalt         20.0                 -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: ins_gnd_sp                          -20.0   20.0    20.0
Plot: gndspd_c                            -20.0   20.0    20.0
Plot: tbp_l                               -20.0   20.0    20.0
Plot: tbp_r                               -20.0   20.0    20.0
Plot: tbf_l                               -20.0   20.0    20.0
Plot: tbf_r                               -20.0   20.0    20.0
Plot: pitch_att       1.5                 -20.0   20.0    20.0
Plot: roll_att        2.0                 -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: alpha_true      1.5                 -20.0   20.0    20.0
Plot: beta_true       2.0                 -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
Plot: roll_rate                           -20.0   20.0    20.0
Plot: yaw_rate                            -20.0   20.0    20.0
Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
Plot: ail_trim                            -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
Plot: prpf                                -20.0   20.0    20.0
Plot: ax_corrctd                          -20.0   20.0    20.0
Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
Plot: pitch_acc                           -20.0   20.0    20.0
Plot: roll_acc                            -20.0   20.0    20.0
Plot: yaw_acc                             -20.0   20.0    20.0
Plot: pla_l                               -20.0   20.0    20.0
Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
Plot: fn_l                                -20.0   20.0    20.0
Plot: fn_r                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0

PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_GAIN          0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_GAIN'
PLOT_LINE: PLOT RDATIME CLS_NW_SPEED                0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_SPEED'
PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_ANG           0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_ANG'
PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_VEL           0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_VEL'

@
