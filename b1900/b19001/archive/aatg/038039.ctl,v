head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.30;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Normal Landing
*--------------------------------------------

LFI_Table:    PLA_L       1.0     0.0       1.0       0.0 T
LFI_Table:    PLA_R       1.0     0.0       1.0       0.0 T
LFI_Table:    ROC_HP      1.0     0.0       1.0       0.0
LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.175     0.010870  0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
*LFI_Table:    FN_L        1.0     0.5       1.0       0.0
*LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
LFI_Table:    ELE_TRIM    1.0     0.0       1.0       0.0
LFI_Table:    RUD_TRIM    1.0     0.0       1.0       0.0
* LFI_Table:    WNDVEL_CAL  1.0     0.000     1.00      0.0
* LFI_Table:    WNDDIR_CAL  1.0     0.000     1.00      0.0

*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------
LFI_Line: *
LFI_Line: LFI    RDATIME  LZTQHLD
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    1
LFI_Line: 27.5   1
LFI_Line: 27.501 0
LFI_Line: 500.0  0
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZTASSIST
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 27.5   0
LFI_Line: 27.501 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZREVERSEP(1)
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 29.0   0
LFI_Line: 29.001 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZREVERSEP(2)
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 29.0   0
LFI_Line: 29.001 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  RFNMIN
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    2.74
LFI_Line: 29.0   2.74
LFI_Line: 29.001 0
LFI_Line: 500.0  0
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RAXRDALT  RZCROC
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   5.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0     0.0
LFI_Line: 5.0    -272
LFI_Line: 15.00  -566
LFI_Line: 80.00  -432
LFI_Line: 200.0  -653
LFI_Line: END
*----------------------------------------------

*----------------------------------------------
*PRE_TRIM: LZTQIASHLD=T
*----------------------------------------------

*----------------------------------------------
*Post_Trim: WAIT 36 RDATIME gt 32
*Post_Trim: WAIT 10 FBWOW eq 1
*Post_Trim: LZROLHLD=F
*Post_Trim: WAIT 5 RDATIME gt 36
*Post_Trim: LZPITHLD=F
*----------------------------------------------

*----------------------------------------------
IFXTRIM:     2
FMNOWINDS:   False
default_lat_rates: False
RZCQDS:      0.0
RZCPDS:      0.0
RZCRDS:      0.0
LZROLHLD:    True
LZPITHLD:    True
LZHDGHLD:    True
LZPASSIST:   True
LZRASSIST:   True
LZYASSIST:   True
LZTQHLD:     True
RZCUDOT:     0.0
RZCROC:      -750
LZFLARE:     True
RZFLAREH:    50.0
Field_Elevation: 173.3
LZCRADALT:   True
LZPITALTHLD: True
LDATRMON(8): 0
*----------------------------------------------

*----------------------------------------------
Add_Criteria: wow_l
Add_Criteria: wow_r
*----------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     80.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: radalt         10.0       10.0      -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: alpha_true      1.5                 -20.0   20.0    20.0
Plot: beta_true                           -20.0   20.0    20.0
Plot: pitch_att       1.5                 -20.0   20.0    20.0
Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf             5.0       10.0      -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
Plot: roll_rate                           -20.0   20.0    20.0
Plot: yaw_rate                            -20.0   20.0    20.0
Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
Plot: prpf                                -20.0   20.0    20.0
Plot: ail_trim                            -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: tbp_l                               -20.0   20.0    20.0
Plot: tbp_r                               -20.0   20.0    20.0
Plot: tbf_l                               -20.0   20.0    20.0
Plot: tbf_r                               -20.0   20.0    20.0
Plot: pitch_acc                           -20.0   20.0    20.0
Plot: roll_acc                            -20.0   20.0    20.0
Plot: yaw_acc                             -20.0   20.0    20.0
Plot: ax_corrctd                          -20.0   20.0    20.0
Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
Plot: pla_l                               -20.0   20.0    20.0
Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
Plot: fn_l                                -20.0   20.0    20.0
Plot: fn_r                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
