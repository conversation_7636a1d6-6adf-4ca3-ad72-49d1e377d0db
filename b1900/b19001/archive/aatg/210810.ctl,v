head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.33;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* <PERSON><PERSON> Brake Force Calibration
*--------------------------------------------

*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

On_Ground:  True
IFXTRIM:    0
LFXSTABT:   False
LFXAILT:    False
LFXRUDT:    False

Set_Winds: True
Cmd_Wind_Speed: 0.0
Cmd_Wind_Dir: 0.0


*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     65.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
* Plot: tbp_l                               -20.0   20.0    20.0
* Plot: tbp_r                               -20.0   20.0    20.0
* Plot: brk_px_l                            -20.0   20.0    20.0
* Plot: brk_px_r                            -20.0   20.0    20.0
* Plot: tbf_l                               -20.0   20.0    20.0
* Plot: tbf_r                               -20.0   20.0    20.0
* Plot: rud_trim                            -20.0   20.0    20.0
* Plot: rud_def                             -20.0   20.0    20.0
* Plot: prpp_l                              -20.0   20.0    20.0
* Plot: prpf                                -20.0   20.0    20.0
* Plot: ins_gnd_sp                          -20.0   20.0    20.0
* Plot: gndspd_c                            -20.0   20.0    20.0

Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: tbf_l
Add_Criteria: tbf_r


Controls_Plot: PLOT FACTTOEB FACTTBFL            '../../tst/critdata/0390240tb_pvf_l'      0      64      4  'LEFT TOE BRAKE POS (IN)'    0       1       .1  'LEFT TOE BRAKE FORCE (LB))'            !
Controls_Plot: PLOT FACTTOER FACTTBFR            '../../tst/critdata/0390240tb_pvf_l'      0      64      4  'RIGHT TOE BRAKE POS (IN)'    0       1       .1  'RIGHT TOE BRAKE FORCE (LB))'          !

Controls_Plot: PLOT FACTTOEB   FLPBRKM           '../../tst/critdata/0390240brk_px_l'    -1.0   2.0     0.5   'LEFT TOE BRAKE POS (IN)'           -50          500         50       'LEFT TOE BRAKE PRESSURE (PSI)'   !
Controls_Plot: PLOT FACTTOER   FRPBRKM           '../../tst/critdata/0390240brk_px_l'    -1.0   2.0     0.5   'RIGHT TOE BRAKE POS (IN)'          -50          500         50       'RIGHT TOE BRAKE PRESSURE (PSI)'   !

*Controls_Plot: PLOT rfmxc(5)   ramxm(5)           '../../tst/critdata/0390240tbf_l'       -0.5   1.5     0.5   'LEFT TOE BRAKE POS (IN)'           -50          200         50       'LEFT TOE BRAKE FORCE (LB)'   !
*Controls_Plot: PLOT rfmxc(6)   ramxm(6)           '../../tst/critdata/0390240tbf_l'       -0.5   1.5     0.5   'RIGHT TOE BRAKE POS (IN)'          -50          200         50       'RIGHT TOE BRAKE FORCE (LB)'   !

*Controls_Plot: PLOT rfmxc(5)   FLPBRKM           '../../tst/critdata/0390240brk_px_l'    -0.5   1.5     0.5   'LEFT TOE BRAKE POS (IN)'           -50          500         50       'LEFT TOE BRAKE PRESSURE (PSI)'   !
*Controls_Plot: PLOT rfmxc(6)   FRPBRKM           '../../tst/critdata/0390240brk_px_l'    -0.5   1.5     0.5   'RIGHT TOE BRAKE POS (IN)'          -50          500         50       'RIGHT TOE BRAKE PRESSURE (PSI)'   !

@
