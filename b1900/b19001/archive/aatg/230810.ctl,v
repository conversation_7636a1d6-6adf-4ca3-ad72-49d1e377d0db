head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.35;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Long Static Stability
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

LZROLHLD:     True
LZPITHLD:     True
LZPITIASHLD:  True
LZFNHLD:      True
LZRASSIST:    True
LZPASSIST:    True
LFI_Rate_Hz:  5

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     450.0    50.0
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas                             100.0      160.0     20.0
Plot: hp                             10000.0    14000.0   2000.0
* Plot: ins_vt_spd                     -1000.0     1000.0   1000.0
* Plot: ele_trim                           2.0        6.0      2.0

Plot: ele_def       1.0                -10.0        0.0      5.0
* Plot: pcp                               -5.0        5.0      5.0
Plot: pcf                              -50.0      100.0     50.0
Plot: pitch_att                          0.0       16.0      8.0

* Plot: roll_att                          -5.0        5.0      5.0
* Plot: heading                          160.0      240.0     40.0
* Plot: pitch_rate                        -2.0        2.0      2.0
* Plot: roll_rate                         -2.0        2.0      2.0

* Plot: yaw_rate                          -2.0        2.0      2.0
Plot: alpha_true                         0.0       10.0      5.0
* Plot: beta_true                         -5.0        5.0      5.0
* Plot: ax_corrctd                        -0.4        0.4      0.4

* Plot: ay_corrctd                        -0.1        0.1      0.1
* Plot: az_corrctd                        -2.0        2.0      2.0
* Plot: pitch_acc                        -10.0       10.0     10.0
* Plot: roll_acc                         -20.0       20.0     20.0

* Plot: yaw_acc                           -5.0        5.0      5.0
* Plot: pla_l                              2.0        6.0      2.0
* Plot: pla_r                              2.0        6.0      2.0
* Plot: torque_l                        1500.0     3000.0    500.0

* Plot: torque_r                        1500.0     3000.0    500.0
Plot: fn_l                             750.0     1500.0    250.0
Plot: fn_r                             750.0     1250.0    250.0
* Plot: ail_trim                           0.0       10.0      5.0

* Plot: pwf                              -20.0       20.0     20.0
* Plot: pwp                              -10.0       20.0     10.0
* Plot: ail_def_l                         -5.0       10.0      5.0
* Plot: ail_def_r                          0.0       10.0      5.0

* Plot: rud_trim                          -5.0        5.0      5.0
* Plot: rud_def                           -5.0       10.0      5.0
* Plot: prpp_l                            -2.0        2.0      2.0
* Plot: prpf                             -20.0       20.0     20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
