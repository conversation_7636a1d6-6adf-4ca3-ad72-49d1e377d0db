head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.17;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Eng-Out Approach Climb
*--------------------------------------------

LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.175     0.010870  0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    FN_L        1.0     0.5       1.0       0.0
LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
* LFI_Table:    WNDVEL_CAL  1.0     0.000     1.00      0.0
* LFI_Table:    WNDDIR_CAL  1.0     0.000     1.00      0.0

*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
RDTRTIME: 50.0
*-----------------------------------------

*--------------------------------------------------
Climb_Test: True
Pre_Trim:   FDEMCL(1)=0
RFNMIN:  2.778
RFNMINT: 2.778
LZFNHLD: True
RZCUDOT: 0.11
RFXROLCM:   3.5
RFXHDGCM: 327.050
Clean_Up:   wait 1
Clean_Up:   FDEMCL(1)=2
Clean_Up: c EBSTART 1
*--------------------------------------------------

*--------------------------------------------------
zero_rates:  True
LZROLHLD:    True
LZPITHLD:    True
LZPITIASHLD: True
LZHDGHLD:    True
LZSSLIP:     True
LZPASSIST:   True
LZRASSIST:   True
LZYASSIST:   True
LFXRUDT:     False
LFXAILT:     True
*--------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    130.00    10.00
Ignore_Iter_Rate: True
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: pitch_rate                        -2.0        2.0      2.0
Plot-: kcas           3.0              100.0      130.0     10.0
Plot: hp                              4000.0     7000.0   1000.0
Plot: pitch_att                          0.0       15.0      5.0

Plotf: fn_l                           -200.0      200.0    200.0
Plot: fn_r                            2200.0     2800.0    200.0
Plot: torque_l                        -500.0      500.0    500.0
Plot: torque_r                        3500.0     4500.0    500.0

Plot: rud_def                          -20.0       -5.0      5.0
Plot: rud_trim                          -5.0        5.0      5.0
Plot: beta_true                         -5.0       10.0      5.0
Plot: roll_att                          -5.0       10.0      5.0

Plot: pwp                               10.0       40.0     10.0
Plot: ail_def_l                          5.0       15.0      5.0
Plot: ail_def_r                        -10.0        5.0      5.0
Plot: heading                          320.0      340.0     10.0

Plot: roll_rate                         -2.0        2.0      2.0
Plot: yaw_rate                          -1.0        1.0      1.0

* Plot: kcas            3.0              112.0      128.0      8.0
* Plot: hp                              4000.0     7000.0   1000.0
* Plot: ins_vt_spd                         0.0     1500.0    500.0
* Plot: pitch_att                          0.0       15.0      5.0
*
* Plot: prpp_l                            10.0       20.0      5.0
* Plot: prpf                               0.0      200.0    100.0
* Plot: rud_trim                          -5.0        5.0      5.0
* Plot: rud_def                          -20.0       -5.0      5.0
*
* Plot: roll_att                          -5.0       10.0      5.0
* Plot-: heading                         310.0      350.0     20.0
* Plot: alpha_true                         0.0       10.0      5.0
* Plot: beta_true                         -5.0       10.0      5.0
*
* Plot: pitch_acc                         -5.0        5.0      5.0
* Plot: roll_acc                          -8.0        8.0      8.0
* Plot: yaw_acc                           -4.0        2.0      2.0
* Plot: pitch_rate                        -2.0        2.0      2.0
*
* Plot: roll_rate                         -2.0        2.0      2.0
* Plot: yaw_rate                          -1.0        1.0      1.0
* Plot-: fn_l                           -200.0      200.0    200.0
* Plot: fn_r                            2200.0     2800.0    200.0
*
* Plot: pla_l                              2.0        6.0      2.0
* Plot: pla_r                              2.0        8.0      2.0
* Plot: torque_l                        -500.0      500.0    500.0
* Plot: torque_r                        3500.0     4500.0    500.0
*
* Plot: ele_trim                           4.0        8.0      2.0
* Plot: ele_def                           -5.0        5.0      5.0
* Plot: pcp                               -5.0        5.0      5.0
* Plot: pcf                              -20.0       20.0     20.0
*
* Plot: pwf                              -40.0       20.0     20.0
* Plot: pwp                               10.0       40.0     10.0
* Plot: ail_def_l                          5.0       15.0      5.0
* Plot: ail_def_r                        -10.0        5.0      5.0
*
* Plot: ax_corrctd                        -0.4        0.4      0.4
* Plot: ay_corrctd                        -0.2        0.2      0.2
* Plot: az_corrctd                        -2.0        2.0      2.0
* Plot: ail_trim                         -20.0      -10.0      5.0
*
* Plot: pitch_acc                         -5.0        5.0      5.0
* Plot: roll_acc                          -8.0        8.0      8.0
* Plot: yaw_acc                           -4.0        2.0      2.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
