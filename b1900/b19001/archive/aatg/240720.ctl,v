head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.37;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Dutch Roll Dynamics
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
LZTQHLD:  True
LZPITHLD: False
Default_Lat_Rates: False
RZCPDS: 0.0554
RZCRDS: 0.0147
*-----------------------------------------

*--------------------------------------------
* Analysis Section
*--------------------------------------------
*                   Sim      Sim
*                   Time_0   Time_1
Analyze: yaw_rate     10.0   22.0
Analyze: roll_rate    11.2   22.0
*
*                       Time     Yaw_Rate
Crit_Peak_Yaw_Rate:     11.04    6.345
Crit_Peak_Yaw_Rate:     12.70   -4.402
Crit_Peak_Yaw_Rate:     14.31    2.619
Crit_Peak_Yaw_Rate:     15.90   -2.035
Crit_Peak_Yaw_Rate:     17.58    0.901
Crit_Peak_Yaw_Rate:     19.13   -0.772
*                       Time     Roll_Rate
Crit_Peak_Roll_Rate:    12.20    5.141
Crit_Peak_Roll_Rate:    13.84   -3.074
Crit_Peak_Roll_Rate:    15.50    2.530
Crit_Peak_Roll_Rate:    17.05   -1.280
Crit_Peak_Roll_Rate:    18.76    0.910
Crit_Peak_Roll_Rate:    20.38   -0.463
*
*--------------------------------------------

LZPITHLD:     True
LZPITIASHLD:  True

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     28.00     4.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: rud_trim                          -5.0        5.0      5.0
Plot: rud_def                          -20.0       20.0     20.0
Plot: prpp_l                           -20.0       20.0     20.0
Plot: prpf                            -200.0      200.0    200.0

Plot: pitch_rate                        -4.0        2.0      2.0
Plot: roll_rate                        -20.0       20.0     20.0
Plot: yaw_rate                         -20.0       20.0     20.0
Plot: pitch_att                         -5.0        5.0      5.0

Plot: roll_att                         -30.0       15.0     15.0
Plot: heading                          310.0      340.0     10.0
Plot: pitch_acc                        -10.0       10.0     10.0
Plot: roll_acc                         -20.0       20.0     20.0

Plot: yaw_acc                          -20.0       40.0     20.0
Plot: ail_trim                           0.0       30.0     15.0
Plot: pwf                              -20.0       40.0     20.0
Plot: pwp                              -10.0       20.0     10.0

Plot: ail_def_l                          0.0       10.0      5.0
Plot: ail_def_r                          0.0       10.0      5.0
Plot: kcas                             145.0      160.0      5.0
Plot: hp                              9000.0    10000.0    500.0

Plot: ins_vt_spd                     -1000.0     1000.0   1000.0
Plot: ax_corrctd                        -0.1        0.1      0.1
Plot: ay_corrctd                        -2.0        2.0      2.0
Plot: az_corrctd                        -2.0        2.0      2.0

Plot: alpha_true                        -5.0        5.0      5.0
Plot: beta_true                        -10.0       10.0     10.0
Plot: pla_l                              2.0        6.0      2.0
Plot: pla_r                              2.0        6.0      2.0

Plot: torque_l                        1400.0     2000.0    200.0
Plot: torque_r                        1400.0     2000.0    200.0
Plot: fn_l                             600.0     1000.0    200.0
Plot: fn_r                             600.0     1000.0    200.0

Plot: ele_trim                           0.0        4.0      2.0
Plot: ele_def                           -5.0        5.0      5.0
Plot: pcp                               -5.0        5.0      5.0
Plot: pcf                              -20.0       20.0     20.0

* Plot: wnddir_cal                         0.0      500.0    250.0
* Plot: wndvel_cal                       -20.0       40.0     20.0
@
