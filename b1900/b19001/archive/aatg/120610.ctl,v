head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.32;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Cross<PERSON> Takeoff
*--------------------------------------------


*--------------------------------------------------
*-------------------------------------------------------

*-------------------------------------------------------
KAXPMODE:  0
KAXRMODE:  4
KAXYMODE:  0
KAXNMODE:  0
*-------------------------------------------------------

*-------------------------------------------------------
FMNOWINDS:         False
default_lat_rates: False
LZBPHLD:           True
LZTQHLD:           False
LZPASSIST:         True
LZRASSIST:         True
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
LZNASSIST:         False
LZHDGHLD:          True
LZCRADALT:         True
LZPITALTHLD:       True
LZTASSIST:         False
LZYASSIST:         True
LZRWYHLD:          False
RZCUDOT:           0.0
RZCQDS:            0.0
*FDEMNW:            19.0
*-------------------------------------------------------

*-------------------------------------------------------
On_Ground:       True
IFXTRIM:         0
Field_Elevation: 400.1
SET_WINDS: True
CMD_WIND_SPEED:  21.63
CMD_WIND_DIR:    85.09
PRE_TRIM: RZPGAIN(7)=0.06
PRE_TRIM: RZIGAIN(7)=2.00
PRE_TRIM: RZDGAIN(7)=2.00
*
*-------------------------------------------------------

*-------------------------------------------------------
RCXLONSK:   -0.51
RCXLATSK:   0.331
RCXRUPED:   0.3033
*-------------------------------------------------------

*-------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: windvel
Add_Criteria: winddir
CRIT_OFFSET: WNDDIR_CAL 0.0 180.0 -1.0
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     65.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: radalt         20.0                 -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: pitch_att       1.5                 -20.0   20.0    20.0

Plot: roll_att        2.0                 -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: alpha_true      1.5                 -20.0   20.0    20.0
Plot: beta_true       2.0                 -20.0   20.0    20.0

Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0

Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
* Plot: ail_def_r                           -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0

Plot: prpf                                -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
Plot: wnddir_cal                          -20.0   20.0    20.0
Plot: wndvel_cal                          -20.0   20.0    20.0

* Plot: ins_vt_spd                          -20.0   20.0    20.0
* Plot: ins_gnd_sp                          -20.0   20.0    20.0
* Plot: gndspd_c                            -20.0   20.0    20.0
* Plot: tbp_l                               -20.0   20.0    20.0
* Plot: tbp_r                               -20.0   20.0    20.0
* Plot: tbf_l                               -20.0   20.0    20.0
* Plot: tbf_r                               -20.0   20.0    20.0

* Plot: pitch_rate                          -20.0   20.0    20.0
* Plot: roll_rate                           -20.0   20.0    20.0
* Plot: yaw_rate                            -20.0   20.0    20.0
* Plot: ele_trim                            -20.0   20.0    20.0

* Plot: ail_trim                            -20.0   20.0    20.0

* Plot: rud_trim                            -20.0   20.0    20.0
* Plot: ax_corrctd                          -20.0   20.0    20.0
* Plot: ay_corrctd                          -20.0   20.0    20.0
* Plot: az_corrctd                          -20.0   20.0    20.0
* Plot: pitch_acc                           -20.0   20.0    20.0
* Plot: roll_acc                            -20.0   20.0    20.0
* Plot: yaw_acc                             -20.0   20.0    20.0
* Plot: pla_l                               -20.0   20.0    20.0
* Plot: pla_r                               -20.0   20.0    20.0
* Plot: torque_l                            -20.0   20.0    20.0
* Plot: torque_r                            -20.0   20.0    20.0
* Plot: fn_l                                -20.0   20.0    20.0
* Plot: fn_r                                -20.0   20.0    20.0

PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_GAIN          0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_GAIN'
PLOT_LINE: PLOT RDATIME CLS_NW_SPEED                0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_SPEED'
PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_ANG           0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_ANG'
PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_VEL           0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_VEL'

@
