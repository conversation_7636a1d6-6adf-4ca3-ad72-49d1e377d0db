head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.38;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Normal Takeoff
*--------------------------------------------

*-----------------------------------------

*-----------------------------------------
*
*-----------------------------------------

*--------------------------------------------------
On_Ground:       True
IFXTRIM:       0
Field_Elevation:   230.0
RTCVG:           0.0
*--------------------------------------------------

*--------------------------------------------------
*PRE_TRIM: LZTQIASHLD=T
*--------------------------------------------------

*--------------------------------------------------
KAXPMODE:          4
KAXRMODE:          4
KAXYMODE:          4
KAXNMODE:          3
*--------------------------------------------------

*--------------------------------------------------
FMNOWINDS:         False
default_lat_rates: False
LZTQHLD:           True
LZPASSIST:         True
LZRASSIST:         True
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
LZNASSIST:         True
*LZT_HDG_RWY_HLD:   True
LZHDGHLD:          True
LZCRADALT:         True
LZPITALTHLD:       True
LZTASSIST:         False
LZYASSIST:         True
LZRWYHLD:          False
RZCUDOT:           0.0
RZCQDS:            0.0
*--------------------------------------------------

*--------------------------------------------------
RCXLONSK:  -0.5100
RCXLATSK:   0.1240
RCXRUPED:   0.0135
*--------------------------------------------------


*------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: tbf_l
Add_Criteria: tbf_r
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    100.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
*Plot: radalt         20.0       10.0      -20.0   20.0    20.0
*Plot: kcas            3.0                 -20.0   20.0    20.0
*Plot: hp                                  -20.0   20.0    20.0
*Plot: ins_vt_spd                          -20.0   20.0    20.0
*Plot: ins_gnd_sp                          -20.0   20.0    20.0
*Plot: gndspd_c                            -20.0   20.0    20.0
*Plot: tbp_l                               -20.0   20.0    20.0
*Plot: tbp_r                               -20.0   20.0    20.0
*Plot: tbf_l                               -20.0   20.0    20.0
*Plot: tbf_r                               -20.0   20.0    20.0
*Plot: pitch_att       1.5                 -20.0   20.0    20.0
*Plot: roll_att                            -20.0   20.0    20.0
*Plot: heading                             -20.0   20.0    20.0
*Plot: alpha_true      1.5                 -20.0   20.0    20.0
*Plot: beta_true                           -20.0   20.0    20.0
*Plot: pitch_rate                          -20.0   20.0    20.0
*Plot: roll_rate                           -20.0   20.0    20.0
*Plot: yaw_rate                            -20.0   20.0    20.0
*Plot: ele_trim                            -20.0   20.0    20.0
*Plot: ele_def                             -20.0   20.0    20.0
*Plot: pcp                                 -20.0   20.0    20.0
*Plot: pcf             5.0       10.0      -20.0   20.0    20.0
*Plot: ax_corrctd                          -20.0   20.0    20.0
*Plot: ay_corrctd                          -20.0   20.0    20.0
*Plot: az_corrctd                          -20.0   20.0    20.0
*Plot: pla_l                               -20.0   20.0    20.0
*Plot: pla_r                               -20.0   20.0    20.0
*Plot: torque_l                            -20.0   20.0    20.0
*Plot: torque_r                            -20.0   20.0    20.0
*Plot: fn_l                                -20.0   20.0    20.0
*Plot: fn_r                                -20.0   20.0    20.0
*Plot: pitch_acc                           -20.0   20.0    20.0
*Plot: roll_acc                            -20.0   20.0    20.0
*Plot: yaw_acc                             -20.0   20.0    20.0
*Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
*Plot: pwp                                 -20.0   20.0    20.0
*Plot: ail_def_l                           -20.0   20.0    20.0
*Plot: ail_def_r                           -20.0   20.0    20.0
*Plot: rud_trim                            -20.0   20.0    20.0
*Plot: rud_def                             -20.0   20.0    20.0
*Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
*Plot: brk_px_l                            -20.0   20.0    20.0
*Plot: brk_px_r                            -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0


controls_plot: PLOT RDATIME RAXRDALT         0.00      100.00       5.00  'Time (sec)'        0.0     2500.0       500.0   'Radio Altitude (ft)'                              !
controls_plot: PLOT RDATIME FVCAL            0.00      100.00       5.00  'Time (sec)'        0.0      160.0        20.0   'Calib Airspeed (kt)'                              !
controls_plot: PLOT RDATIME FHP              0.00      100.00       5.00  'Time (sec)'        0.0     3000.0       500.0   'Calib Press Altitude (ft)'                        !
controls_plot: PLOT RDATIME FROC             0.00      100.00       5.00  'Time (sec)'     -200.0     2000.0       200.0   'Ins Vertical Speed (ft/min)'                      !
controls_plot: PLOT RDATIME FVPGND           0.00      100.00       5.00  'Time (sec)'        0.0      160.0        20.0   'Ins Gnd Speed (kt)'                               !
*controls_plot: PLOT RDATIME FVPGND           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Gnd Speed (Beech) (kt)'                           !
*controls_plot: PLOT RDATIME FACTTOEB         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Toe Brake Posn (in)'                           !
*controls_plot: PLOT RDATIME FACTTOER         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Toe Brake Posn (in)'                           !
*controls_plot: PLOT RDATIME FACTTBFL         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Toe Brake Force (lb)'                          !
*controls_plot: PLOT RDATIME FACTTBFR         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Toe Brake Force (lb)'                          !
controls_plot: PLOT RDATIME FTHETA           0.00      100.00       5.00  'Time (sec)'       -1.0       10.0         1.0   'Pitch Attitude (deg)'                             !
controls_plot: PLOT RDATIME FPHI             0.00      100.00       5.00  'Time (sec)'       -0.4        1.4         0.2   'Roll Attitude (deg)'                              !
*controls_plot: PLOT RDATIME FPSIDEG          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Heading (deg)'                                    !
controls_plot: PLOT RDATIME FALPD            0.00      100.00       5.00  'Time (sec)'       -1.0        5.0         1.0   'True Angle Of Attack (deg)'                       !
*controls_plot: PLOT RDATIME FBETAD           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'True Angle Of Sideslip (deg)'                     !
*controls_plot: PLOT RDATIME FQADEG           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pitch Rate (deg/sec)'                             !
*controls_plot: PLOT RDATIME FPADEG           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Roll Rate (deg/sec)'                              !
*controls_plot: PLOT RDATIME FRADEG           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Yaw Rate (deg/sec)'                               !
*controls_plot: PLOT RDATIME RLXETTAB         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Elevator Trim Defl (deg)'                         !
*controls_plot: PLOT RDATIME RZELA            0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Elevator Defl (deg)'                              !
*controls_plot: PLOT RDATIME FACTSSP          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Column Posn (in)'                           !
*controls_plot: PLOT RDATIME FACTSFOR         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Column Force (lb)'                          !
*controls_plot: PLOT RDATIME FAXA             0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Corr Long Accel (g)'                              !
*controls_plot: PLOT RDATIME FAYA             0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Corr Lateral Accel (g)'                           !
*controls_plot: PLOT RDATIME FAZA             0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Corr Vert Accel (g)'                              !
*controls_plot: PLOT RDATIME EPLA(1)          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Power Lever Posn (in)'                         !
*controls_plot: PLOT RDATIME EPLA(2)          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Power Lever Posn (in)'                         !
*controls_plot: PLOT RDATIME ETRQ(1)          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Engine Torque (ft-lb)'                         !
*controls_plot: PLOT RDATIME ETRQ(2)          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Engine Torque (ft-lb)'                         !
*controls_plot: PLOT RDATIME EFN(1)           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Net Thrust (lb)'                               !
*controls_plot: PLOT RDATIME EFN(2)           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Net Thrust (lb)'                               !
*controls_plot: PLOT RDATIME FDQADEG          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pitch Accel (deg/sec2)'                           !
*controls_plot: PLOT RDATIME FDPADEG          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Roll Accel (deg/sec2)'                            !
*controls_plot: PLOT RDATIME FDRADEG          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Yaw Accel (deg/sec2)'                             !
*controls_plot: PLOT RDATIME RLXATTAB         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Aileron Trim Defl (deg)'                          !
*controls_plot: PLOT RDATIME FACTWFOR         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Wheel Force (lb)'                           !
*controls_plot: PLOT RDATIME FACTSWP          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Wheel Posn (deg)'                           !
*controls_plot: PLOT RDATIME RLZAIL           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Aileron Defl (deg)'                            !
*controls_plot: PLOT RDATIME RRZAIL           0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Aileron Defl (deg)'                            !
*controls_plot: PLOT RDATIME RLXRTTAB         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rudder Trim Defl (deg)'                           !
*controls_plot: PLOT RDATIME RZRUD            0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rudder Defl (deg)'                                !
*controls_plot: PLOT RDATIME FACTSPP          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Rudder Pedal Posn (deg)'                       !
*controls_plot: PLOT RDATIME FACTPFOR         0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Rudder Pedal Force (lb)'                    !
*controls_plot: PLOT RDATIME FLPBRKM          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Brake Press (psi)'                             !
*controls_plot: PLOT RDATIME FRPBRKM          0.00      100.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Brake Press (psi)'                             !
*plot_line: PLOT RDATIME FWGRSS           0      64      4  'Time (sec)'      0       1       .1  'Aircraft Weight (lb)'                             !
*plot_line: PLOT RDATIME FHP              0      64      4  'Time (sec)'      0       1       .1  'ADC Press Altitude (ft)'                          !
*plot_line: PLOT RDATIME FROC             0      64      4  'Time (sec)'      0       1       .1  'ADC Vertical Speed (ft/min)'                      !
*plot_line: PLOT RDATIME FALPD            0      64      4  'Time (sec)'      0       1       .1  'Filt. True AOA (deg)'                             !
*plot_line: PLOT RDATIME FAXA             0      64      4  'Time (sec)'      0       1       .1  'Long Accel (g)'                                   !
*plot_line: PLOT RDATIME FAYA             0      64      4  'Time (sec)'      0       1       .1  'Lateral Accel (g)'                                !
*plot_line: PLOT RDATIME FBETAD           0      64      4  'Time (sec)'      0       1       .1  'Filt. Angle Of Sideslip (deg)'                    !
*plot_line: PLOT RDATIME FLXCG            0      64      4  'Time (sec)'      0       1       .1  'Long C.G. (ft)'                                   !
*plot_line: PLOT RDATIME FCGMAC           0      64      4  'Time (sec)'      0       1       .1  'Long C.G. (%mac)'                                 !
*plot_line: PLOT RDATIME ECL(1)           0      64      4  'Time (sec)'      0       1       .1  'Lt Cond Lever Posn (in)'                          !
*plot_line: PLOT RDATIME ECL(2)           0      64      4  'Time (sec)'      0       1       .1  'Rt Cond Lever Posn (in)'                          !
*plot_line: PLOT RDATIME ETC(1)           0      64      4  'Time (sec)'      0       1       .1  'Lt Coeff Of Thrust'                               !
*plot_line: PLOT RDATIME ETC(2)           0      64      4  'Time (sec)'      0       1       .1  'Rt Coeff Of Thrust'                               !
*plot_line: PLOT RDATIME EWF(1)           0      64      4  'Time (sec)'      0       1       .1  'Lt Eng Fuel Flow (lb/hr)'                         !
*plot_line: PLOT RDATIME EWF(2)           0      64      4  'Time (sec)'      0       1       .1  'Rt Eng Fuel Flow (lb/hr)'                         !
*plot_line: PLOT RDATIME FACTFLAP         0      64      4  'Time (sec)'      0       1       .1  'Flap Defl (deg)'                                  !
*plot_line: PLOT RDATIME FLAP_LVR         0      64      4  'Time (sec)'      0       1       .1  'Flap Lever Posn (deg)'                            !
*plot_line: PLOT RDATIME EFNP(1)          0      64      4  'Time (sec)'      0       1       .1  'Lt Net Propeller Thrust (lb)'                     !
*plot_line: PLOT RDATIME EFNP(2)          0      64      4  'Time (sec)'      0       1       .1  'Rt Net Propeller Thrust (lb)'                     !
*plot_line: PLOT RDATIME GTQTY            0      64      4  'Time (sec)'      0       1       .1  'Total Fuel Remaining (lb)'                        !
*plot_line: PLOT RDATIME FKGEAR           0      64      4  'Time (sec)'      0       1       .1  'Gear Posn (1-dn)'                                 !
*plot_line: PLOT RDATIME RAXRDALT         0      64      4  'Time (sec)'      0       1       .1  'Ins Altitude (ft)'                                !
*plot_line: PLOT RDATIME FNAZA            0      64      4  'Time (sec)'      0       1       .1  'Ins Vertical Accel (g)'                           !
*plot_line: PLOT RDATIME ETIT(1)          0      64      4  'Time (sec)'      0       1       .1  'Lt ITT (deg C)'                                   !
*plot_line: PLOT RDATIME ETIT(2)          0      64      4  'Time (sec)'      0       1       .1  'Rt ITT (deg C)'                                   !
*plot_line: PLOT RDATIME FVIAS            0      64      4  'Time (sec)'      0       1       .1  'Meas Indicated Airspeed (kt)'                     !
*plot_line: PLOT RDATIME FVPKN            0      64      4  'Time (sec)'      0       1       .1  'Meas True Airspeed (kt)'                          !
*plot_line: PLOT RDATIME HLGDL            0      64      4  'Time (sec)'      0       1       .1  'Lt Land Gear Down Lock (1-dn)'                    !
*plot_line: PLOT RDATIME HLGUL            0      64      4  'Time (sec)'      0       1       .1  'Lt Land Gear Up Lock (1-up)'                      !
*plot_line: PLOT RDATIME FMACH            0      64      4  'Time (sec)'      0       1       .1  'Mach Number'                                      !
*plot_line: PLOT RDATIME FMACH            0      64      4  'Time (sec)'      0       1       .1  'Meas Mach Number'                                 !
*plot_line: PLOT RDATIME EFNJ(1)          0      64      4  'Time (sec)'      0       1       .1  'Lt Net Jet Thrust (lb)'                           !
*plot_line: PLOT RDATIME EFNJ(2)          0      64      4  'Time (sec)'      0       1       .1  'Rt Net Jet Thrust (lb)'                           !
*plot_line: PLOT RDATIME EPN1(1)          0      64      4  'Time (sec)'      0       1       .1  'Lt Gas Generator Speed (%)'                       !
*plot_line: PLOT RDATIME EPN1(2)          0      64      4  'Time (sec)'      0       1       .1  'Rt Gas Generator Speed (%)'                       !
*plot_line: PLOT RDATIME HNGDL            0      64      4  'Time (sec)'      0       1       .1  'Nose Land Gear Down Lock (1-dn)'                  !
*plot_line: PLOT RDATIME HNGUL            0      64      4  'Time (sec)'      0       1       .1  'Nose Land Gear Up Lock (1-up)'                    !
*plot_line: PLOT RDATIME RLXNWANG         0      64      4  'Time (sec)'      0       1       .1  'Nose Wheel Defl (deg)'                            !
*plot_line: PLOT RDATIME FTOAC            0      64      4  'Time (sec)'      0       1       .1  'Ambient Temp (deg C)'                             !
*plot_line: PLOT RDATIME EOILP(1)         0      64      4  'Time (sec)'      0       1       .1  'Lt Oil Press (psi)'                               !
*plot_line: PLOT RDATIME EOILP(2)         0      64      4  'Time (sec)'      0       1       .1  'Rt Oil Press (psi)'                               !
*plot_line: PLOT RDATIME EOILT(1)         0      64      4  'Time (sec)'      0       1       .1  'Lt Oil Temp (deg C)'                              !
*plot_line: PLOT RDATIME EOILT(2)         0      64      4  'Time (sec)'      0       1       .1  'Rt Oil Temp (deg C)'                              !
*plot_line: PLOT RDATIME EPL(1)           0      64      4  'Time (sec)'      0       1       .1  'Lt Propeller Lever Posn (in)'                     !
*plot_line: PLOT RDATIME EPL(2)           0      64      4  'Time (sec)'      0       1       .1  'Rt Propeller Lever Posn (in)'                     !
*plot_line: PLOT RDATIME EN2(1)           0      64      4  'Time (sec)'      0       1       .1  'Lt Propeller Rpm (rpm)'                           !
*plot_line: PLOT RDATIME EN2(2)           0      64      4  'Time (sec)'      0       1       .1  'Rt Propeller Rpm (rpm)'                           !
*plot_line: PLOT RDATIME FACTSPP          0      64      4  'Time (sec)'      0       1       .1  'Rt Rudder Pedal Posn (deg)'                       !
*plot_line: PLOT RDATIME FPADEG           0      64      4  'Time (sec)'      0       1       .1  'Corr Roll Rate (deg/sec)'                         !
*plot_line: PLOT RDATIME FPADEG           0      64      4  'Time (sec)'      0       1       .1  'Filt Corr Roll Rate (deg/sec)'                    !
*plot_line: PLOT RDATIME FQADEG           0      64      4  'Time (sec)'      0       1       .1  'Corr Pitch Rate (deg/sec)'                        !
*plot_line: PLOT RDATIME FQADEG           0      64      4  'Time (sec)'      0       1       .1  'Filt Corr Pitch Rate (deg/sec)'                   !
*plot_line: PLOT RDATIME HRGDL            0      64      4  'Time (sec)'      0       1       .1  'Rt Land Gear Down Lock (1-dn)'                    !
*plot_line: PLOT RDATIME HRGUL            0      64      4  'Time (sec)'      0       1       .1  'Rt Land Gear Up Lock (1-up)'                      !
*plot_line: PLOT RDATIME FRADEG           0      64      4  'Time (sec)'      0       1       .1  'Corr Yaw Rate (deg/sec)'                          !
*plot_line: PLOT RDATIME FRADEG           0      64      4  'Time (sec)'      0       1       .1  'Filt Axis Yaw Rate (deg/sec)'                     !
*plot_line: PLOT RDATIME FSWHORN          0      64      4  'Time (sec)'      0       1       .1  'Stall Warning Horn (1-stall)'                     !
*plot_line: PLOT RDATIME FRAMK            0      64      4  'Time (sec)'      0       1       .1  'ADC Total Air Temp (deg K)'                       !
*plot_line: PLOT RDATIME FWDHDG           0      64      4  'Time (sec)'      0       1       .1  'Wind Direction (deg)'                             !
*plot_line: PLOT RDATIME FWDVEL           0      64      4  'Time (sec)'      0       1       .1  'Wind Velocity (kt)'                               !
*plot_line: PLOT RDATIME FWDHDG           0      64      4  'Time (sec)'      0       1       .1  'Corr Wind Direction (deg)'                        !
*plot_line: PLOT RDATIME FWDVEL           0      64      4  'Time (sec)'      0       1       .1  'Corr Wind Velocity (kt)'                          !
*plot_line: PLOT RDATIME FLBWO            0      64      4  'Time (sec)'      0       1       .1  'Weight On Lt Main Gear (1-on)'                    !
*plot_line: PLOT RDATIME FNBWO            0      64      4  'Time (sec)'      0       1       .1  'Weight On Nose Gear (1-on)'                       !
*plot_line: PLOT RDATIME FRBWO            0      64      4  'Time (sec)'      0       1       .1  'Weight On Rt Main Gear (1-on)'                    !
*plot_line: PLOT RDATIME FROC             0      64      4  'Time (sec)'      0       1       .1  'Rate of Climb (ft/min)'                           !
*plot_line: PLOT RDATIME FDIST            0      64      4  'Time (sec)'      0       1       .1  'Ground Distance (ft)'                             !
*plot_line: PLOT RDATIME RZCCL            0      64      4  'Time (sec)'      0       1       .1  'RZCCL'                                            !
*plot_line: PLOT RDATIME RZCCD            0      64      4  'Time (sec)'      0       1       .1  'RZCCD'                                            !
*plot_line: PLOT RDATIME RZCCM            0      64      4  'Time (sec)'      0       1       .1  'RZCCM'                                            !
*plot_line: PLOT RDATIME RZCCY            0      64      4  'Time (sec)'      0       1       .1  'RZCCY'                                            !
*plot_line: PLOT RDATIME RZCCN            0      64      4  'Time (sec)'      0       1       .1  'RZCCN'                                            !
*plot_line: PLOT RDATIME RZCCR            0      64      4  'Time (sec)'      0       1       .1  'RZCCR'                                            !
controls_plot: PLOT RDATIME FAAUEW           0     100.0    5  'Time (sec)'   -100       10    10.0  'Head Wind Velocity (ft/sec)'                      !
controls_plot: PLOT RDATIME TAAVEW           0     100.0    5  'Time (sec)'     -1       1      0.5  'Cross Wind Velocity (ft/sec)'                     !
controls_plot: PLOT RDATIME TAAWEW           0     100.0    5  'Time (sec)'    -30      30      5.0  'Downward Wind Velocity (ft/sec)'                  !
@
