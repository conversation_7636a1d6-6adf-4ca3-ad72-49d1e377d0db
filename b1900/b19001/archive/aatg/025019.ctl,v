head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.11;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Phugoid Dynamics
*--------------------------------------------

LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.175     0.010870  0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    FN_L        1.0     0.5       1.0       0.0
LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
* LFI_Table:    WNDVEL_CAL  1.0     0.000     1.00      0.0
* LFI_Table:    WNDDIR_CAL  1.0     0.000     1.00      0.0

*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

IFXTRIM:     3
LZROLHLD:    True
* LZRASSIST:   True
* LZPASSIST:   True
LZPITHLD:    True
* LZPITIASHLD: True
* LZTQHLD:     True
* post_trim:   LZTQHLD=F

Analyze_Pitch_Att_Del_X: 0
Analyze_Pitch_Att_Del_Y: 0

LFI_Line: *
LFI_Line: LFI RDATIME LZPITHLD
LFI_Line: ABS T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0      1
LFI_Line: 45.35  1
LFI_Line: 45.351 0
LFI_Line: 500    0
LFI_Line: END
LFI_Line: *

*--------------------------------------------
* Analysis Section
*--------------------------------------------
*                   Sim      Sim
*                   Time_0   Time_1
Analyze: pitch_att   55.0    236
*
*                      Time     Pitch_Att
Crit_Peak_Pitch_Att:    64.150    -5.230
Crit_Peak_Pitch_Att:    93.650     3.137
Crit_Peak_Pitch_Att:   122.450    -2.274
Crit_Peak_Pitch_Att:   148.850     2.098
Crit_Peak_Pitch_Att:   177.050    -2.269
Crit_Peak_Pitch_Att:   201.450     1.296
Crit_Peak_Pitch_Att:   222.350    -0.912
*--------------------------------------------


*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    260.00    20.00
Ignore_Iter_Rate: True
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas                             180.0      220.0     20.0
Plot: hp                              9500.0    11000.0    500.0
Plot: ins_vt_spd                     -4000.0     2000.0   2000.0
Plot: pitch_att                         -8.0        8.0      8.0

Plot: ele_trim                          -2.0        2.0      2.0
Plot: ele_def                           -5.0       10.0      5.0
Plot: pcp                               -5.0        5.0      5.0
Plot: pcf                              -20.0       40.0     20.0

Plot: roll_att                          -5.0        5.0      5.0
Plot: heading                          300.0      330.0     10.0
Plot: pitch_rate                        -2.0        2.0      2.0
Plot: roll_rate                         -2.0        2.0      2.0

Plot: alpha_true                        -5.0        5.0      5.0
Plot: beta_true                         -5.0        5.0      5.0
Plot: pla_l                              2.0        6.0      2.0
Plot: pla_r                              2.0        6.0      2.0

Plot: torque_l                        2500.0     3500.0    500.0
Plot: torque_r                        2500.0     3500.0    500.0
Plot: fn_l                             600.0     1200.0    200.0
Plot: fn_r                             600.0     1200.0    200.0

Plot: yaw_rate                          -2.0        2.0      2.0
Plot: pitch_acc                         -8.0        8.0      8.0
Plot: roll_acc                         -10.0       10.0     10.0
Plot: yaw_acc                           -2.0        2.0      2.0

Plot: ail_trim                          -5.0        5.0      5.0
Plot: ax_corrctd                        -0.1        0.1      0.1
Plot: ay_corrctd                        -0.1        0.1      0.1
Plot: az_corrctd                        -2.0        2.0      2.0

Plot: pwf                              -20.0       20.0     20.0
Plot: pwp                              -10.0       20.0     10.0
Plot: ail_def_l                          0.0       10.0      5.0
Plot: ail_def_r                          0.0       10.0      5.0

Plot: rud_trim                          -5.0       10.0      5.0
Plot: rud_def                           -5.0        5.0      5.0
Plot: prpp_l                            -4.0        4.0      4.0
Plot: prpf                             -20.0       20.0     20.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
