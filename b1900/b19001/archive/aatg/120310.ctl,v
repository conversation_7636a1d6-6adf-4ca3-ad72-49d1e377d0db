head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.31;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Minimum Unstick Speed
*--------------------------------------------


*-----------------------------------------

*-----------------------------------------
*
*
*-----------------------------------------
KAXPMODE:  0
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  0
*-----------------------------------------

*--------------------------------------------------
On_Ground:       True
IFXTRIM:         0
Field_Elevation: 500.0
SET_WINDS: True
CMD_WIND_SPEED:  20.81
CMD_WIND_DIR:    182.0
PRE_TRIM: RZPGAIN(7)=0.06
PRE_TRIM: RZIGAIN(7)=2.00
PRE_TRIM: RZDGAIN(7)=2.00
QTG_Test_Airport: Takeoff  500  0.0   0.0   0.0  0.0   184

*--------------------------------------------------

LZBPHLD:   True
LZTQHLD:   True
LZRWYHLD:  False
LZHDGHLD:  True
LZPASSIST: True
LZRASSIST: True
LFXSTABT:  False
LFXAILT:   False
LFXRUDT:   False
*--------------------------------------------------

*--------------------------------------------------
FDEMENG(1): 35.0
FDEMENG(2): 35.0
FDEMENG(3): 35.0
FDEMENG(4): 35.0
FDEMTOEB:   1.19
FDEMTOER:   1.19
RCXLONSK:  -0.505
RCXRUPED:   0.300
*--------------------------------------------------

*--------------------------------------------
Min_Unstick_Speed: 87.61
Min_Unstick_Pitch:  4.10
*--------------------------------------------

*------------------------------------------------------
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: nwd
Add_Criteria: wow_n
Add_Criteria: wow_l
Add_Criteria: wow_r
Add_Criteria: radalt
Add_Criteria: comp_radalt
CRIT_OFFSET: WNDDIR_CAL 0.0  366.0  -1.0
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     70.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas           3.0                  -20.0   20.0    20.0
Plot: gndspd_c                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pitch_att      1.5                  -20.0   20.0    20.0
Plot: wow_l                               -20.0   20.0    20.0
Plot: wow_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0

*
PLOT_LINE: PLOT RDATIME FNDOLEO               0      64      4  'Time (sec)'      0       1       .1  'FNDOLEO'                                          !
PLOT_LINE: PLOT RDATIME FLDOLEO               0      64      4  'Time (sec)'      0       1       .1  'FLDOLEO'                                          !
PLOT_LINE: PLOT RDATIME FRDOLEO               0      64      4  'Time (sec)'      0       1       .1  'FRDOLEO'                                          !
*
@
