head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.41.54;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Crosswind Landings
*--------------------------------------------

LFI_Table:    PLA_L       1.0     0.0       1.0       0.0 T
LFI_Table:    PLA_R       1.0     0.0       1.0       0.0 T
LFI_Table:    ROC_HP      1.0     0.0       1.0       0.0  
LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.175     0.010870  0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0 T
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0  
LFI_Table:    ROLL_ATT    1.0     0.4       1.0       0.0
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    FN_L        1.0     0.5       1.0       0.0
LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
LFI_Table:    ELE_TRIM    1.0     0.0       1.0       0.0
LFI_Table:    RUD_TRIM    1.0     0.0       1.0       0.0
LFI_Table:    WNDVEL_CAL  1.0     0.0       1.0       0.0
LFI_Table:    WNDDIR_CAL  1.0     0.0       1.0       0.0
LFI_Table:    TBP_L       1.0     0.0       1.0       0.0
LFI_Table:    TBP_R       1.0     0.0       1.0       0.0
*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
Pre_Trim: RZPGAIN(7) = 0.06
Pre_Trim: RZDGAIN(7) = 2.00
Pre_Trim: RZIGAIN(7) = 2.00
Pre_Trim: RZIDGAIN(7) = 3.00
Pre_Trim: RZIGAIN(10) = 5.00
Pre_Trim: RZPGAIN(10) = 0.01
Pre_Trim: RZDGAIN(10) = 1.00
RZCTQ(1): 1020.0   ! torque
RZCTQ(2): 1020.0   ! torque
LFXSTABT: True

*-----------------------------------------

*----------------------------------------------
LFI_Line: *
LFI_Line: LFI    RDATIME  LZTQHLD
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    1
LFI_Line: 19.16  1
LFI_Line: 19.161 0
LFI_Line: 35.00  0
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZTASSIST
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 19.16  0
LFI_Line: 19.161 1
LFI_Line: 35.00  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZREVERSEP(1)
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 25.3   0
LFI_Line: 25.301 1
LFI_Line: 35.00  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZREVERSEP(2)
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 25.3   0
LFI_Line: 25.301 1
LFI_Line: 35.00  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  RFNMIN
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    2.74
LFI_Line: 25.3   2.74
LFI_Line: 25.301 0
LFI_Line: 35.00  0
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZPITHLD
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    1
LFI_Line: 30.00  1
LFI_Line: 30.001 0
LFI_Line: 35.00  0
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RAXRDALT  RZCROC
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0     0
LFI_Line: 5.0    -120
LFI_Line: 10.0   -450
LFI_Line: 15.0   -600
LFI_Line: 20.00  -600
LFI_Line: 50.00  -650
LFI_Line: 75.00  -700
LFI_Line: 200.0  -630
LFI_Line: 225.0  -830
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  RZCALTRAD
LFI_Line: ABS    F
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.40000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.000    2.212000e+02
LFI_Line: 19.000   2.822050e+01
LFI_Line: 19.050   2.792260e+01
LFI_Line: 19.100   2.762470e+01
LFI_Line: 19.150   2.688010e+01
LFI_Line: 19.200   2.628430e+01
LFI_Line: 19.250   2.568860e+01
LFI_Line: 19.300   2.539070e+01
LFI_Line: 19.350   2.524180e+01
LFI_Line: 19.400   2.419920e+01
LFI_Line: 19.450   2.345460e+01
LFI_Line: 19.500   2.285880e+01
LFI_Line: 19.550   2.256100e+01
LFI_Line: 19.600   2.226310e+01
LFI_Line: 19.650   2.211410e+01
LFI_Line: 19.700   2.151840e+01
LFI_Line: 19.750   2.092270e+01
LFI_Line: 19.800   2.017800e+01
LFI_Line: 19.850   1.958230e+01
LFI_Line: 19.900   1.913550e+01
LFI_Line: 19.950   1.853970e+01
LFI_Line: 20.000   1.779500e+01
LFI_Line: 20.050   1.764610e+01
LFI_Line: 20.100   1.705040e+01
LFI_Line: 20.150   1.645460e+01
LFI_Line: 20.200   1.615680e+01
LFI_Line: 20.250   1.556100e+01
LFI_Line: 20.300   1.556100e+01
LFI_Line: 20.350   1.481630e+01
LFI_Line: 20.400   1.392270e+01
LFI_Line: 20.450   1.332700e+01
LFI_Line: 20.500   1.288020e+01
LFI_Line: 20.550   1.258230e+01
LFI_Line: 20.600   1.168870e+01
LFI_Line: 20.650   1.094400e+01
LFI_Line: 20.700   1.049720e+01
LFI_Line: 20.750   1.005040e+01
LFI_Line: 20.800   9.752570e+00
LFI_Line: 20.850   9.007900e+00
LFI_Line: 20.900   8.710030e+00
LFI_Line: 20.950   8.114290e+00
LFI_Line: 21.000   7.667490e+00
LFI_Line: 21.050   7.071750e+00
LFI_Line: 21.100   6.476010e+00
LFI_Line: 21.150   6.029210e+00
LFI_Line: 21.200   5.582400e+00
LFI_Line: 21.250   5.433470e+00
LFI_Line: 21.300   4.986660e+00
LFI_Line: 21.350   4.539860e+00
LFI_Line: 21.400   3.795190e+00
LFI_Line: 21.450   3.497320e+00
LFI_Line: 21.500   3.050510e+00
LFI_Line: 21.550   2.901580e+00
LFI_Line: 21.600   2.901580e+00
LFI_Line: 21.650   2.156900e+00
LFI_Line: 21.700   1.561170e+00
LFI_Line: 21.750   1.561170e+00
LFI_Line: 21.800   1.263300e+00
LFI_Line: 21.850   9.654270e-01
LFI_Line: 21.900   5.186230e-01
LFI_Line: 21.950   3.696900e-01
LFI_Line: 22.000   3.696900e-01
LFI_Line: 22.050   0.000000e+00
LFI_Line: 38.000   0.000000e+00
LFI_Line: END
LFI_Line: *

*----------------------------------

*----------------------------------

*Post_Trim: WAIT 36 RDATIME gt 32
*Post_Trim: WAIT 10 FBWOW eq 1
*Post_Trim: LZROLHLD=T
*Post_Trim: WAIT 5 RDATIME gt 36
*Post_Trim: LZPITHLD=T

*----------------------------------------------
IFXTRIM:     2
FMNOWINDS:   False
default_lat_rates: False
RZCQDS:      0.0
RZCPDS:      0.9417
RZCRDS:      0.0
LZROLHLD:    True
LZPITHLD:    True
LZHDGHLD:    True
LZPASSIST:   True
LZRASSIST:   True
LZYASSIST:   True
LZTQHLD:     True
RZCUDOT:     0.0
RZCROC:      -750
LZFLARE:     True
RZFLAREH:    250 
Field_Elevation: 402.2
LZCRADALT:   True
LZPITALTHLD: True
LZTRIM_ROC_UDOT: True
LDATRMON(8): 0
RFXROLCM:    1.000
RZCRDS:      -0.05

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    110.00    10.00
*-----------------------------------------------------

*-----------------------------------------------------
Add_Criteria: wow_l
Add_Criteria: wow_r
*----------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
PLOT_LINE:  PLOT RDATIME RAXRDALT             '../../tst/critdata/0210110comptd_radalt'        0.00       110.00      10.00 'Time (sec)'      -20.0       20.0        20.0   'Computed Radio Altitude (ft)'                              !
*Plot: comp_radalt    10.0       10.0      -20.0   20.0    20.0
Plot: radalt         10.0       10.0      -20.0   20.0    20.0
Plot: kcas            3.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: alpha_true      1.5                 -20.0   20.0    20.0
Plot: beta_true       2.0                 -20.0   20.0    20.0
Plot: pitch_att       1.5                 -20.0   20.0    20.0
Plot: roll_att        2.0                 -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
Plot: roll_rate                           -20.0   20.0    20.0
Plot: yaw_rate                            -20.0   20.0    20.0
Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
Plot: pcf                                 -20.0   20.0    20.0
Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
Plot: prpf                                -20.0   20.0    20.0
Plot: ail_trim                            -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: tbp_l                               -20.0   20.0    20.0
Plot: tbp_r                               -20.0   20.0    20.0
Plot: tbf_l                               -20.0   20.0    20.0
Plot: tbf_r                               -20.0   20.0    20.0
Plot: pitch_acc                           -20.0   20.0    20.0
Plot: roll_acc                            -20.0   20.0    20.0
Plot: yaw_acc                             -20.0   20.0    20.0
Plot: ax_corrctd                          -20.0   20.0    20.0
Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0
Plot: pla_l                               -20.0   20.0    20.0
Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
Plot: fn_l                                -20.0   20.0    20.0
Plot: fn_r                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0


Controls_Plot: PLOT RDATIME RAXRDALT             '../../tst/critdata/0210110comptd_radalt' 0.00       110.00      10.00 'Time (sec)'      -20.0       20.0        20.0   'Computed Radalt (ft)'                             !

QTG_Test_Airport:  Landing   402   0.1922893907E-02  0.1388167958E-01  0.0  0.0   81.0

@
