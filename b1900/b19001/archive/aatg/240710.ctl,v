head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.37;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Dutch Roll Dynamics
*--------------------------------------------


*-----------------------------------------
LZTQHLD: True
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
LZTQHLD: True
*-----------------------------------------


*--------------------------------------------
* Analysis Section
*--------------------------------------------
*                   Sim      Sim
*                   Time_0   Time_1
Analyze: yaw_rate    7.0     20.0
Analyze: roll_rate   8.0     20.0
*
*                       Time     Yaw_Rate
Crit_Peak_Yaw_Rate:     7.40     7.945
Crit_Peak_Yaw_Rate:     8.65    -5.203
Crit_Peak_Yaw_Rate:     9.86     4.143
Crit_Peak_Yaw_Rate:    11.11    -2.600
Crit_Peak_Yaw_Rate:    12.41     2.232
Crit_Peak_Yaw_Rate:    13.70    -1.088
Crit_Peak_Yaw_Rate:    15.05     1.186
Crit_Peak_Yaw_Rate:    16.31    -0.339
Crit_Peak_Yaw_Rate:    17.60     0.762
*
*                       Time     Roll_Rate
Crit_Peak_Roll_Rate:    8.30     7.895
Crit_Peak_Roll_Rate:    9.55    -5.188
Crit_Peak_Roll_Rate:   10.70     4.371
Crit_Peak_Roll_Rate:   12.03    -2.830
Crit_Peak_Roll_Rate:   13.36     2.345
Crit_Peak_Roll_Rate:   14.63    -1.081
Crit_Peak_Roll_Rate:   16.00     1.164
Crit_Peak_Roll_Rate:   17.30    -0.411
Crit_Peak_Roll_Rate:   18.64     0.798
*--------------------------------------------

LZPITHLD:     True
LZPITIASHLD:  True

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     24.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: rud_trim                          -5.0        5.0      5.0
Plot: rud_def                          -10.0       20.0     10.0
Plot: prpp_l                           -20.0       20.0     20.0
*Plot: prpf                            -200.0      200.0    200.0

Plot: pitch_rate                        -4.0        4.0      4.0
Plot: roll_rate                        -20.0       20.0     20.0
Plot: yaw_rate                         -20.0       20.0     20.0
Plot: pitch_att                         -5.0        5.0      5.0

Plot: roll_att                         -20.0       20.0     20.0
Plot: heading                          260.0      290.0     10.0
*Plot: pitch_acc                        -10.0       20.0     10.0
*Plot: roll_acc                         -50.0       50.0     50.0

*Plot: yaw_acc                          -50.0       50.0     50.0
Plot: ail_trim                          -5.0        5.0      5.0
*Plot: pwf                              -20.0       20.0     20.0
Plot: pwp                              -10.0       20.0     10.0

Plot: ail_def_l                          0.0       10.0      5.0
Plot: ail_def_r                          0.0       10.0      5.0
Plot: kcas                             185.0      200.0      5.0
Plot: hp                             20500.0    21500.0    500.0

*Plot: ins_vt_spd                     -1600.0      800.0    800.0
*Plot: ax_corrctd                        -0.1        0.1      0.1
*Plot: ay_corrctd                        -2.0        2.0      2.0
*Plot: az_corrctd                        -2.0        2.0      2.0

Plot: alpha_true                        -5.0        5.0      5.0
Plot: beta_true                        -10.0       10.0     10.0
Plot: pla_l                              2.0        6.0      2.0
Plot: pla_r                              2.0        6.0      2.0

Plot: torque_l                        2400.0     3000.0    200.0
Plot: torque_r                        2400.0     2800.0    200.0
Plot: fn_l                             600.0     1000.0    200.0
Plot: fn_r                             600.0     1000.0    200.0

Plot: ele_trim                          -2.0        4.0      2.0
Plot: ele_def                           -5.0       10.0      5.0
Plot: pcp                               -5.0        5.0      5.0
*Plot: pcf                              -40.0       20.0     20.0

* Plot: wnddir_cal                       250.0      350.0     50.0
* Plot: wndvel_cal                        10.0       30.0     10.0
@
