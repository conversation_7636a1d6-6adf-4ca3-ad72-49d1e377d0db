head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.41.43;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* <PERSON><PERSON> to Roll Input
*--------------------------------------------

LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.110     0.01070   0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0
*LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    FN_L        1.0     0.5       1.0       0.0
LFI_Table:    FN_R        1.0     0.5       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
* LFI_Table:    WNDVEL_CAL  1.0     0.000     1.00      0.0
* LFI_Table:    WNDDIR_CAL  1.0     0.000     1.00      0.0

#            Var    x_off  y_off     y_mult
Crit_Offset: pwp    -0.012   0.003    1

FDEMATAB:  1.100
LFXAILT: False

*-----------------------------------------
KAXPMODE:  3
KAXRMODE:  3
KAXYMODE:  3
*-----------------------------------------

ZERO_RATES:  True

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     30.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Controls_Plot: PLOT RDATIME CLS_SURF_Q(2)                                                  0      64      4  'Time (sec)'      0       1       .1  'Surface Q - Left  Aileron'                          !
Controls_Plot: PLOT RDATIME CLS_SURF_Q(8)                                                  0      64      4  'Time (sec)'      0       1       .1  'Surface Q - Right Aileron'                          !

Plot: ail_trim                            -20.0   20.0    20.0
Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0

LFI_Line: *Takeoff Release LFI's
LFI_Line: *
LFI_Line: LFI     RDATIME KAXRMODE
LFI_Line: ABS T
LFI_Line: XMULT 1.0
LFI_Line: XOFF 0.0
LFI_Line: YMULT 1.0
LFI_Line: YOFF 0.0
LFI_Line: 0.0              3
LFI_Line: 0.5              3
LFI_Line: 2.0              3
LFI_Line: 2.032            3
LFI_Line: 2.03201          0
LFI_Line: 4.0              0
LFI_Line: END

pre_trim: *
pre_trim: C CLS_RBDFASTOFF(2) 1        ! fast release for dynamics 
pre_trim: C CLS_RBDGFASTOFF(2) 100       ! speed up release for dynamics
pre_trim: *

clean_up: *
clean_up: C CLS_RBDFASTOFF(2) 0 
clean_up: *
@
