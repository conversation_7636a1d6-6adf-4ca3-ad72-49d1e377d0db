head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.36;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Inflight Low Speed Eng Out Char
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

* ramgm(1) 500.0 fixes LDATRMON(6): 0 LDATRMON(7): 0
*
Default_Lat_Rates: False
RZCPDS: 0.0
RZCQDS: 0.0
RZCRDS: 0.015
RZCUDOT: 0.25
RDTRTIME:   50.0
* LZHDGHLD:    True
LZROLHLD:    True
LZPITHLD:    True
LZTQHLD:     True
LZSSLIP:     False
* LZYASSIST:   True
LZPASSIST:   True
LZPITIASHLD: True
LFXAILT:     False
LFXRUDT:     False

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    120.00    10.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----

Plot-: rud_def                          -30.0      -15.0      5.0
Plot: rud_trim                           0.0       10.0      5.0
Plot: beta_true                          0.0       10.0      5.0
Plot-: heading                         120.0      160.0     20.0

Plot: kcas            3.0               80.0      120.0     20.0
Plot: hp                              3000.0     6000.0   1000.0
Plot: pitch_att                          5.0       20.0      5.0
Plot: ele_def                           -5.0        5.0      5.0

Plot: roll_att                           0.0       10.0      5.0
Plot: pwp                                0.0       80.0     40.0
Plot: ail_def_l                         10.0       20.0      5.0
Plot: ail_def_r                        -20.0        0.0     10.0

* Plot: fn_l                            -200.0      200.0    200.0
* Plot: fn_r                            2600.0     3000.0    200.0
Plot: torque_l                        -500.0      500.0    500.0
Plot: torque_r                        3500.0     4500.0    500.0

Plot: roll_rate                         -2.0        2.0      2.0
Plot: yaw_rate                          -1.0        1.0      1.0
Plot: pitch_rate                        -4.0        2.0      2.0


* Plot: pitch_att                          5.0       15.0      5.0
* Plot: roll_att                           0.0       10.0      5.0
* Plot: heading                          120.0      150.0     10.0
* Plot: pitch_rate                        -4.0        2.0      2.0
*
* Plot: roll_rate                         -2.0        2.0      2.0
* Plot: yaw_rate                          -1.0        1.0      1.0
* Plot: pitch_acc                         -5.0        5.0      5.0
* Plot: roll_acc                          -5.0        5.0      5.0
*
* Plot: yaw_acc                           -2.0        2.0      2.0
* Plot: ax_corrctd                        -0.4        0.4      0.4
* Plot: ay_corrctd                        -0.4        0.4      0.4
* Plot: az_corrctd                        -2.0        2.0      2.0
*
* Plot-: kcas           3.0               80.0      120.0     20.0
* Plot: hp                                 0.0    10000.0   5000.0
* Plot: ins_vt_spd                         0.0    10000.0   5000.0
* Plot-: ail_trim                        -20.0        0.0     10.0
*
* Plot: pwf                              -20.0       20.0     20.0
* Plot: pwp                              -50.0      100.0     50.0
* Plot: ail_def_l                          0.0       60.0     20.0
* Plot: ail_def_r                        -20.0       10.0     10.0
*
* Plot: rud_trim                         -10.0       10.0     10.0
* Plot: rud_def                          -40.0       20.0     20.0
* Plot: prpp_l                           -40.0       40.0     40.0
* Plot: prpf                               0.0      150.0     50.0
*
* Plot: alpha_true                         0.0      100.0     50.0
* Plot: beta_true                          0.0       10.0      5.0
* Plot: pla_l                              0.0       20.0     10.0
* Plot: pla_r                              2.0        6.0      2.0
*
* Plot: torque_l                        -500.0      500.0    500.0
* Plot: torque_r                           0.0     6000.0   2000.0
* Plot: fn_l                           -4000.0     8000.0   4000.0
* Plot: fn_r                               0.0     4000.0   2000.0
*
* Plot: ele_trim                           0.0     4000.0   2000.0
* Plot: ele_def                           -5.0        5.0      5.0
* Plot: pcp                            -2000.0     4000.0   2000.0
* Plot: pcf                              -20.0       40.0     20.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
