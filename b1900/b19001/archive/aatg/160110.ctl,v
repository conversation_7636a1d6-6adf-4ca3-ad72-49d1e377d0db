head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.32;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Engine Accel, Go-Around
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

*-------------------------------------------------------
*-------------------------------------------------------

RZCTQ(1): 1100.0
RZCTQ(2): 1125.0

*--------------------------------------------------
* On_Ground:       True
* IFXTRIM:         0
* Field_Elevation: 500.0
*--------------------------------------------------

*--------------------------------------------------
LFXSTABT:  False
LFXAILT:   False
LFXRUDT:   False
*--------------------------------------------------
LZTQHLD: True
POST_TRIM: LZTQHLD=F
*--------------------------------------------------
* FDEMENG(1): 35.0
* FDEMENG(2): 35.0
* FDEMENG(3): 35.0
* FDEMENG(4): 35.0
*--------------------------------------------------

*----------------------------------------------
Engine:      2
Start_Time:  5.00  ! Initial PLA Movement
Final_Time:  22.0   ! Time that N1 reaches final value
TLA_Tol:     0.10   ! Tolerance of PLA from initial PLA
*----------------------------------------------



*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     25.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: pla_l                                 2.0   6.00     0.5
Plot: pla_r                                 2.0   6.00     0.5
Plot: n1_l                                 85.0  100.0     5.0
Plot: n1_r                                 85.0  100.0     5.0
*Plot: ff_l                                300.0  750.0    20.0
*Plot: ff_r                                300.0  750.0    20.0
*Plot: torque_l                            500.0 4500.0   500.0
*Plot: torque_r                            500.0 4500.0   500.0
*Plot: fn_l                                600.0 2600.0   200.0
*Plot: fn_r                                600.0 2600.0   200.0
*Plot: ins_gnd_sp                          -20.0   20.0    20.0
*Plot: gndspd_c                            -20.0   20.0    20.0
*Plot: tbp_l                               -20.0   20.0    20.0
*Plot: tbp_r                               -20.0   20.0    20.0
*Plot: tbf_l                               -20.0   20.0    20.0
*Plot: tbf_r                               -20.0   20.0    20.0
*Plot: radalt                              -20.0   20.0    20.0
*Plot: kcas                                 110.0  135.0   100.0
*Plot: hp                                  4100.0 4800.0   100.0
*Plot: ins_vt_spd                          -20.0   20.0    20.0
*Plot: ax_corrctd                          -20.0   20.0    20.0
*Plot: ay_corrctd                          -20.0   20.0    20.0
*Plot: az_corrctd                          -20.0   20.0    20.0
*Plot: pitch_att                           -20.0   20.0    20.0
*Plot: roll_att                            -20.0   20.0    20.0
*Plot: heading                             -20.0   20.0    20.0
*Plot: pitch_rate                          -20.0   20.0    20.0
*Plot: roll_rate                           -20.0   20.0    20.0
*Plot: yaw_rate                            -20.0   20.0    20.0
*Plot: alpha_true                          -20.0   20.0    20.0
*Plot: beta_true                           -20.0   20.0    20.0
*Plot: pitch_acc                           -20.0   20.0    20.0
*Plot: roll_acc                            -20.0   20.0    20.0
*Plot: yaw_acc                             -20.0   20.0    20.0
*Plot: ele_trim                            -20.0   20.0    20.0
*Plot: ele_def                             -20.0   20.0    20.0
*Plot: pcp                                 -20.0   20.0    20.0
*Plot: pcf                                 -20.0   20.0    20.0
*Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
*Plot: pwp                                 -20.0   20.0    20.0
*Plot: ail_def_l                           -20.0   20.0    20.0
*Plot: ail_def_r                           -20.0   20.0    20.0
*Plot: rud_trim                            -20.0   20.0    20.0
*Plot: rud_def                             -20.0   20.0    20.0
*Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
