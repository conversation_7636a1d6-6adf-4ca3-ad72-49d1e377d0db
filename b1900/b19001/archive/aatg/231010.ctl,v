head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.36;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Phugoid Dynamics
*--------------------------------------------


*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------

IFXTRIM:     3
LZROLHLD:    True
LZPITHLD:    True


*--------------------------------------------
* Analysis Section
*--------------------------------------------
*                   Sim      Sim
*                   Time_0   Time_1
Analyze: pitch_att   73.0     265.0
*
*                      Time     Pitch_Att
Crit_Peak_Pitch_Att:   82.485   -6.418
Crit_Peak_Pitch_Att:  109.101    5.980
Crit_Peak_Pitch_Att:  136.85    -4.071
Crit_Peak_Pitch_Att:  163.85     4.235
Crit_Peak_Pitch_Att:  190.85    -2.802
Crit_Peak_Pitch_Att:  217.85     3.153
Crit_Peak_Pitch_Att:  245.129   -1.632
*--------------------------------------------


*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    280.00    20.00
Ignore_Iter_Rate: True
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: kcas                             160.0      220.0     20.0
Plot: hp                             20000.0    22000.0   1000.0
Plot: ins_vt_spd                     -5000.0     5000.0   5000.0
Plot: pitch_att                        -10.0       10.0     10.0

Plot: ele_trim                          -2.0        4.0      2.0
Plot: ele_def                           -5.0       10.0      5.0
Plot: pcp                               -5.0        5.0      5.0
*Plot: pcf                              -20.0       40.0     20.0

Plot: roll_att                          -5.0       10.0      5.0
Plot: heading                          300.0      340.0     20.0
Plot: pitch_rate                        -2.0        2.0      2.0
*Plot: roll_rate                         -2.0        4.0      2.0

Plot: alpha_true                        -5.0        5.0      5.0
Plot: beta_true                         -5.0        5.0      5.0
Plot: pla_l                              2.0        6.0      2.0
Plot: pla_r                              2.0        6.0      2.0

Plot: torque_l                        2000.0     3500.0    500.0
Plot: torque_r                        2000.0     3500.0    500.0
Plot: fn_l                             600.0     1000.0    200.0
Plot: fn_r                             600.0     1000.0    200.0

*Plot: yaw_rate                          -2.0        2.0      2.0
*Plot: pitch_acc                        -10.0       10.0     10.0
*Plot: roll_acc                         -20.0       10.0     10.0
*Plot: yaw_acc                           -5.0        5.0      5.0

Plot: ail_trim                          -5.0        5.0      5.0
*Plot: ax_corrctd                        -0.2        0.2      0.2
*Plot: ay_corrctd                        -0.1        0.1      0.1
Plot: az_corrctd                        -2.0        2.0      2.0

*Plot: pwf                              -20.0       20.0     20.0
Plot: pwp                              -10.0       20.0     10.0
Plot: ail_def_l                          0.0       10.0      5.0
Plot: ail_def_r                          0.0       10.0      5.0

Plot: rud_trim                          -5.0       10.0      5.0
Plot: rud_def                           -5.0       10.0      5.0
Plot: prpp_l                            -4.0        4.0      4.0
*Plot: prpf                             -20.0       20.0     20.0

* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0
@
