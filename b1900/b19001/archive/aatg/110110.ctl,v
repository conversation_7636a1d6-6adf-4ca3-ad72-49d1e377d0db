head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.31;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Minimum Radius Turn
*--------------------------------------------



*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  3
*-----------------------------------------

*--------------------------------------------------
On_Ground:         True
IFXTRIM:           0
Field_Elevation:   500.0
cmd_wind_speed: 0.0
cmd_wind_dir: 0.0
*--------------------------------------------------

default_lat_rates: False
LZTQHLD:           False
LZFNHLD:           True
LZPASSIST:         False
LZRASSIST:         False
LFXSTABT:          False
LFXAILT:           False
LFXRUDT:           False
LZNASSIST:         False
LZT_HDG_RWY_HLD:   False
LZHDGHLD:          True
LZCRADALT:         False
LZPITALTHLD:       False
LZTASSIST:         False
LZYASSIST:         False
LZRWYHLD:          False
RZCUDOT:           0.0
RZCQDS:            0.0
*----------------------------------

*----------------------------------
FDEMENG(1): 89.0
FDEMENG(2): 89.0
FDEMENG(3): 89.0
FDEMENG(4): 89.0
RCXLONSK:   0.000
RCXLATSK:   0.000
RCXRUPED:   0.000

*------------------------------------------------------
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     60.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----

*Controls_Plot: PLOT FGYECG FGXECG                                                           -50      50    25  'CG EAST  REF DIST (ft)'           -100      50    25  'CG NORTH REF DIST (ft)'                            !
Controls_Plot: PLOT FGYEN FGXEN                                                             -100     100    50  'NOSE GEAR EAST REF DIST (ft)'     -100     100    50  'NOSE GEAR NORTH REF DIST (ft)'                     !
*Controls_Plot: PLOT FGYEL FGXEL                                                             -100     100    50  'LEFT GEAR EAST REF DIST (ft)'     -100     100    50  'LEFT GEAR NORTH REF DIST (ft)'                     !
*Controls_Plot: PLOT FGYER FGXER                                                             -100     100    50  'RIGHT GEAR EAST REF DIST (ft) '   -100     100    50  'RIGHT GEAR NORTH REF DIST (ft)'                    !
Controls_Plot: PLOT RDATIME RLXNWANG             '../../tst/critdata/0100260nwd'           0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Nose Wheel Defl (deg)'                            !
*Controls_Plot: PLOT RDATIME FVPGND               '../../tst/critdata/0100260ins_gnd_sp'    0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Ins Gnd Speed (kt)'                               !
*Controls_Plot: PLOT RDATIME FVPGND               '../../tst/critdata/0100260gndspd_c'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Gnd Speed (Beech) (kt)'                           !
*Controls_Plot: PLOT RDATIME FQADEG               '../../tst/critdata/0100260pitch_rate'    0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pitch Rate (deg/sec)'                             !
*Controls_Plot: PLOT RDATIME FPADEG               '../../tst/critdata/0100260roll_rate'     0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Roll Rate (deg/sec)'                              !
*Controls_Plot: PLOT RDATIME FRADEG               '../../tst/critdata/0100260yaw_rate'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Yaw Rate (deg/sec)'                               !
Controls_Plot: PLOT RDATIME FVCAL                '../../tst/critdata/0100260kcas'          0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Calib Airspeed (kt)'                              !
*Controls_Plot: PLOT RDATIME FHP                  '../../tst/critdata/0100260hp'            0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Calib Press Altitude (ft)'                        !
*Controls_Plot: PLOT RDATIME FROC                 '../../tst/critdata/0100260ins_vt_spd'    0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Ins Vertical Speed (ft/min)'                      !
*Controls_Plot: PLOT RDATIME FTHETA               '../../tst/critdata/0100260pitch_att'     0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pitch Attitude (deg)'                             !
*Controls_Plot: PLOT RDATIME FPHI                 '../../tst/critdata/0100260roll_att'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Roll Attitude (deg)'                              !
Controls_Plot: PLOT RDATIME FPSIDEG              '../../tst/critdata/0100260heading'       0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Heading (deg)'                                    !
Controls_Plot: PLOT RDATIME EPLA(1)              '../../tst/critdata/0100260pla_l'         0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Power Lever Posn (in)'                         !
Controls_Plot: PLOT RDATIME EPLA(2)              '../../tst/critdata/0100260pla_r'         0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Power Lever Posn (in)'                         !
*Controls_Plot: PLOT RDATIME ETRQ(1)              '../../tst/critdata/0100260torque_l'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Engine Torque (ft-lb)'                         !
*Controls_Plot: PLOT RDATIME ETRQ(2)              '../../tst/critdata/0100260torque_r'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Engine Torque (ft-lb)'                         !
*Controls_Plot: PLOT RDATIME EFN(1)               '../../tst/critdata/0100260fn_l'          0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Net Thrust (lb)'                               !
*Controls_Plot: PLOT RDATIME EFN(2)               '../../tst/critdata/0100260fn_r'          0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Net Thrust (lb)'                               !
*Controls_Plot: PLOT RDATIME EFNP(1)              '../../tst/critdata/0100260fnp_l'         0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Net Propeller Thrust (lb)'                     !
*Controls_Plot: PLOT RDATIME EFNP(2)              '../../tst/critdata/0100260fnp_r'         0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Net Propeller Thrust (lb)'                     !
*Controls_Plot: PLOT RDATIME EFNJ(1)              '../../tst/critdata/0100260mfnj_1'        0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Net Jet Thrust (lb)'                           !
*Controls_Plot: PLOT RDATIME EFNJ(2)              '../../tst/critdata/0100260mfnj_2'        0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Net Jet Thrust (lb)'                           !
*Controls_Plot: PLOT RDATIME RLXETTAB             '../../tst/critdata/0100260ele_trim'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Elevator Trim Defl (deg)'                         !
*Controls_Plot: PLOT RDATIME RZELA                '../../tst/critdata/0100260ele_def'       0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Elevator Defl (deg)'                              !
*Controls_Plot: PLOT RDATIME FACTSSP              '../../tst/critdata/0100260pcp'           0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Column Posn (in)'                           !
*Controls_Plot: PLOT RDATIME FACTSFOR             '../../tst/critdata/0100260pcf'           0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Column Force (lb)'                          !
*Controls_Plot: PLOT RDATIME RLXATTAB             '../../tst/critdata/0100260ail_trim'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Aileron Trim Defl (deg)'                          !
*Controls_Plot: PLOT RDATIME FACTWFOR             '../../tst/critdata/0100260pwf'           0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Wheel Force (lb)'                           !
*Controls_Plot: PLOT RDATIME FACTSWP              '../../tst/critdata/0100260pwp'           0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Wheel Posn (deg)'                           !
*Controls_Plot: PLOT RDATIME RZAIL                '../../tst/critdata/0100260ail_def_l'     0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Aileron Defl (deg)'                            !
*Controls_Plot: PLOT RDATIME RRZAIL               '../../tst/critdata/0100260ail_def_r'     0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rt Aileron Defl (deg)'                            !
*Controls_Plot: PLOT RDATIME RLXRTTAB             '../../tst/critdata/0100260rud_trim'      0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rudder Trim Defl (deg)'                           !
*Controls_Plot: PLOT RDATIME RZRUD                '../../tst/critdata/0100260rud_def'       0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Rudder Defl (deg)'                                !
*Controls_Plot: PLOT RDATIME FACTSPP              '../../tst/critdata/0100260prpp_l'        0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Lt Rudder Pedal Posn (deg)'                       !
*Controls_Plot: PLOT RDATIME FACTPFOR             '../../tst/critdata/0100260prpf'          0.00       60.00       5.00  'Time (sec)'      -20.0       20.0        20.0   'Pilot Rudder Pedal Force (lb)'                    !
*Controls_Plot: PLOT RDATIME FWGRSS               '../../tst/critdata/0100260ac_weight'     0      64      4  'Time (sec)'      0       1       .1  'Aircraft Weight (lb)'                             !
*Controls_Plot: PLOT RDATIME FALPD                '../../tst/critdata/0100260alpha_f'       0      64      4  'Time (sec)'      0       1       .1  'Filt. True AOA (deg)'                             !
*Controls_Plot: PLOT RDATIME FALPD                '../../tst/critdata/0100260alpha_true'    0      64      4  'Time (sec)'      0       1       .1  'True Angle Of Attack (deg)'                       !
*Controls_Plot: PLOT RDATIME FNAZA                '../../tst/critdata/0100260an'            0      64      4  'Time (sec)'      0       1       .1  'Ins Normal Accel (g)'                             !
*Controls_Plot: PLOT RDATIME FAXA                 '../../tst/critdata/0100260ax'            0      64      4  'Time (sec)'      0       1       .1  'Long Accel (g)'                                   !
*Controls_Plot: PLOT RDATIME FAXA                 '../../tst/critdata/0100260ax_corrctd'    0      64      4  'Time (sec)'      0       1       .1  'Corr Long Accel (g)'                              !
*Controls_Plot: PLOT RDATIME FAYA                 '../../tst/critdata/0100260ay'            0      64      4  'Time (sec)'      0       1       .1  'Lateral Accel (g)'                                !
*Controls_Plot: PLOT RDATIME FAYA                 '../../tst/critdata/0100260ay_corrctd'    0      64      4  'Time (sec)'      0       1       .1  'Corr Lateral Accel (g)'                           !
*Controls_Plot: PLOT RDATIME FAZA                 '../../tst/critdata/0100260az_corrctd'    0      64      4  'Time (sec)'      0       1       .1  'Corr Vert Accel (g)'                              !
*Controls_Plot: PLOT RDATIME FBETAD               '../../tst/critdata/0100260beta_f'        0      64      4  'Time (sec)'      0       1       .1  'Filt. Angle Of Sideslip (deg)'                    !
*Controls_Plot: PLOT RDATIME FBETAD               '../../tst/critdata/0100260beta_true'     0      64      4  'Time (sec)'      0       1       .1  'True Angle Of Sideslip (deg)'                     !
*Controls_Plot: PLOT RDATIME FLXCG                                                          0      64      4  'Time (sec)'      0       1       .1  'Long C.G. (ft)'                                   !
*Controls_Plot: PLOT RDATIME FCGMAC               '../../tst/critdata/0100260cg_pct_mac'    0      64      4  'Time (sec)'      0       1       .1  'Long C.G. (%mac)'                                 !
*Controls_Plot: PLOT RDATIME ECL(1)               '../../tst/critdata/0100260cond_lvr_l'    0      64      4  'Time (sec)'      0       1       .1  'Lt Cond Lever Posn (in)'                          !
*Controls_Plot: PLOT RDATIME ECL(2)               '../../tst/critdata/0100260cond_lvr_r'    0      64      4  'Time (sec)'      0       1       .1  'Rt Cond Lever Posn (in)'                          !
*Controls_Plot: PLOT RDATIME ECT(1)               '../../tst/critdata/0100260ct_l'          0      64      4  'Time (sec)'      0       1       .1  'Lt Coeff Of Thrust'                               !
*Controls_Plot: PLOT RDATIME ECT(2)               '../../tst/critdata/0100260ct_r'          0      64      4  'Time (sec)'      0       1       .1  'Rt Coeff Of Thrust'                               !
*Controls_Plot: PLOT RDATIME EWF(1)               '../../tst/critdata/0100260ff_l'          0      64      4  'Time (sec)'      0       1       .1  'Lt Eng Fuel Flow (lb/hr)'                         !
*Controls_Plot: PLOT RDATIME EWF(2)               '../../tst/critdata/0100260ff_r'          0      64      4  'Time (sec)'      0       1       .1  'Rt Eng Fuel Flow (lb/hr)'                         !
*Controls_Plot: PLOT RDATIME FACTFLAP             '../../tst/critdata/0100260flap_def'      0      64      4  'Time (sec)'      0       1       .1  'Flap Defl (deg)'                                  !
*Controls_Plot: PLOT RDATIME FLAP_LVR                                                       0      64      4  'Time (sec)'      0       1       .1  'Flap Lever Posn (deg)'                            !
*Controls_Plot: PLOT RDATIME GTQTY                                                          0      64      4  'Time (sec)'      0       1       .1  'Total Fuel Remaining (lb)'                        !
*Controls_Plot: PLOT RDATIME FKGEAR               '../../tst/critdata/0100260gear_pos'      0      64      4  'Time (sec)'      0       1       .1  'Gear Posn (1=dn)'                                 !
*Controls_Plot: PLOT RDATIME FNAZA                                                          0      64      4  'Time (sec)'      0       1       .1  'Ins Vertical Accel (g)'                           !
*Controls_Plot: PLOT RDATIME ETIT(1)              '../../tst/critdata/0100260itt_l'         0      64      4  'Time (sec)'      0       1       .1  'Lt ITT (deg C)'                                   !
*Controls_Plot: PLOT RDATIME ETIT(2)              '../../tst/critdata/0100260itt_r'         0      64      4  'Time (sec)'      0       1       .1  'Rt ITT (deg C)'                                   !
*Controls_Plot: PLOT RDATIME FVIAS                '../../tst/critdata/0100260kias_meas'     0      64      4  'Time (sec)'      0       1       .1  'Meas Indicated Airspeed (kt)'                     !
*Controls_Plot: PLOT RDATIME FVPKN                '../../tst/critdata/0100260ktas_meas'     0      64      4  'Time (sec)'      0       1       .1  'Meas True Airspeed (kt)'                          !
*Controls_Plot: PLOT RDATIME FMACH                '../../tst/critdata/0100260mach'          0      64      4  'Time (sec)'      0       1       .1  'Mach Number'                                      !
*Controls_Plot: PLOT RDATIME EPCNH(1)             '../../tst/critdata/0100260n1_l'          0      64      4  'Time (sec)'      0       1       .1  'Lt Gas Generator Speed (%)'                       !
*Controls_Plot: PLOT RDATIME EPCNH(2)             '../../tst/critdata/0100260n1_r'          0      64      4  'Time (sec)'      0       1       .1  'Rt Gas Generator Speed (%)'                       !
*Controls_Plot: PLOT RDATIME FTOAC                '../../tst/critdata/0100260oat'           0      64      4  'Time (sec)'      0       1       .1  'Ambient Temp (deg C)'                             !
*Controls_Plot: PLOT RDATIME EOILP(1)             '../../tst/critdata/0100260oil_px_l'      0      64      4  'Time (sec)'      0       1       .1  'Lt Oil Press (psi)'                               !
*Controls_Plot: PLOT RDATIME EOILP(2)             '../../tst/critdata/0100260oil_px_r'      0      64      4  'Time (sec)'      0       1       .1  'Rt Oil Press (psi)'                               !
*Controls_Plot: PLOT RDATIME EOILT(1)             '../../tst/critdata/0100260oil_temp_l'    0      64      4  'Time (sec)'      0       1       .1  'Lt Oil Temp (deg C)'                              !
*Controls_Plot: PLOT RDATIME EOILT(2)             '../../tst/critdata/0100260oil_temp_r'    0      64      4  'Time (sec)'      0       1       .1  'Rt Oil Temp (deg C)'                              !
*Controls_Plot: PLOT RDATIME FDQADEG              '../../tst/critdata/0100260pitch_acc'     0      64      4  'Time (sec)'      0       1       .1  'Pitch Accel (deg/sec2)'                           !
*Controls_Plot: PLOT RDATIME EPCNL(1)             '../../tst/critdata/0100260prpm_l'        0      64      4  'Time (sec)'      0       1       .1  'Lt Propeller Rpm (rpm)'                           !
*Controls_Plot: PLOT RDATIME EPCNL(2)             '../../tst/critdata/0100260prpm_r'        0      64      4  'Time (sec)'      0       1       .1  'Rt Propeller Rpm (rpm)'                           !
*Controls_Plot: PLOT RDATIME FACTSPP              '../../tst/critdata/0100260prpp_r'        0      64      4  'Time (sec)'      0       1       .1  'Rt Rudder Pedal Posn (deg)'                       !
*Controls_Plot: PLOT RDATIME FPADEG               '../../tst/critdata/0100260p_corrctd'     0      64      4  'Time (sec)'      0       1       .1  'Corr Roll Rate (deg/sec)'                         !
*Controls_Plot: PLOT RDATIME FQADEG               '../../tst/critdata/0100260q_corrctd'     0      64      4  'Time (sec)'      0       1       .1  'Corr Pitch Rate (deg/sec)'                        !
*Controls_Plot: PLOT RDATIME RAXRDALT             '../../tst/critdata/0100260radalt'        0      64      4  'Time (sec)'      0       1       .1  'Radio Altitude (ft)'                              !
*Controls_Plot: PLOT RDATIME FDEMGEAR                                                       0      64      4  'Time (sec)'      0       1       .1  'Rt Land Gear Down Lock (1=dn)'                    !
*Controls_Plot: PLOT RDATIME FDPADEG              '../../tst/critdata/0100260roll_acc'      0      64      4  'Time (sec)'      0       1       .1  'Roll Accel (deg/sec2)'                            !
*Controls_Plot: PLOT RDATIME FRADEG               '../../tst/critdata/0100260r_corrctd'     0      64      4  'Time (sec)'      0       1       .1  'Corr Yaw Rate (deg/sec)'                          !
*Controls_Plot: PLOT RDATIME FRAMK                '../../tst/critdata/0100260tat'           0      64      4  'Time (sec)'      0       1       .1  'ADC Total Air Temp (deg K)'                       !
*Controls_Plot: PLOT RDATIME RHLBP                                                          0      64      4  'Time (sec)'      0       1       .1  'Lt Brake Press (psi)'                             !
*Controls_Plot: PLOT RDATIME RHRBP                                                          0      64      4  'Time (sec)'      0       1       .1  'Rt Brake Press (psi)'                             !
*Controls_Plot: PLOT RDATIME FACTTBFL                                                       0      64      4  'Time (sec)'      0       1       .1  'Lt Toe Brake Force (lb)'                          !
*Controls_Plot: PLOT RDATIME FACTTBFR                                                       0      64      4  'Time (sec)'      0       1       .1  'Rt Toe Brake Force (lb)'                          !
*Controls_Plot: PLOT RDATIME FACTTOEB             '../../tst/critdata/0100260tbp_l'         0      64      4  'Time (sec)'      0       1       .1  'Lt Toe Brake Posn (in)'                           !
*Controls_Plot: PLOT RDATIME FACTTOER             '../../tst/critdata/0100260tbp_r'         0      64      4  'Time (sec)'      0       1       .1  'Rt Toe Brake Posn (in)'                           !
*Controls_Plot: PLOT RDATIME FWDHDG               '../../tst/critdata/0100260wnddir_cal'    0      64      4  'Time (sec)'      0       1       .1  'Corr Wind Direction (deg)'                        !
*Controls_Plot: PLOT RDATIME FWDVEL               '../../tst/critdata/0100260wndvel_cal'    0      64      4  'Time (sec)'      0       1       .1  'Corr Wind Velocity (kt)'                          !
*Controls_Plot: PLOT RDATIME FLBWO                                                          0      64      4  'Time (sec)'      0       1       .1  'Weight On Lt Main Gear (1=on)'                    !
*Controls_Plot: PLOT RDATIME FNBWO                                                          0      64      4  'Time (sec)'      0       1       .1  'Weight On Nose Gear (1=on)'                       !
*Controls_Plot: PLOT RDATIME FRBWO                                                          0      64      4  'Time (sec)'      0       1       .1  'Weight On Rt Main Gear (1=on)'                    !
*Controls_Plot: PLOT RDATIME FDRADEG              '../../tst/critdata/0100260yaw_acc'       0      64      4  'Time (sec)'      0       1       .1  'Yaw Accel (deg/sec2)'                             !
*Controls_Plot: PLOT RDATIME RZCCL                                                          0      64      4  'Time (sec)'      0       1       .1  'RZCCL'                                            !
*Controls_Plot: PLOT RDATIME RZCCD                                                          0      64      4  'Time (sec)'      0       1       .1  'RZCCD'                                            !
*Controls_Plot: PLOT RDATIME RZCCM                                                          0      64      4  'Time (sec)'      0       1       .1  'RZCCM'                                            !
*Controls_Plot: PLOT RDATIME RZCCY                                                          0      64      4  'Time (sec)'      0       1       .1  'RZCCY'                                            !
*Controls_Plot: PLOT RDATIME RZCCN                                                          0      64      4  'Time (sec)'      0       1       .1  'RZCCN'                                            !
*Controls_Plot: PLOT RDATIME RZCCR                                                          0      64      4  'Time (sec)'      0       1       .1  'RZCCR'                                            !
@
