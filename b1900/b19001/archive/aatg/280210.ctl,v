head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.38;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Normal Landing
*--------------------------------------------

*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
*-----------------------------------------
*-----------------------------------------

*----------------------------------------------
*PRE_TRIM: LZTQIASHLD=T
*----------------------------------------------

*Post_Trim: WAIT 36 RDATIME gt 32
*Post_Trim: WAIT 10 FBWOW eq 1
*Post_Trim: LZROLHLD=F
*Post_Trim: WAIT 5 RDATIME gt 36
*Post_Trim: LZPITHLD=F

*----------------------------------------------
IFXTRIM:           2
FMNOWINDS:         False
*default_lat_rates: False
zero_rates:        true
RZCQDS:           -0.16
RZCPDS:           -0.33
RZCRDS:           -0.4014
LZROLHLD:          True
LZPITHLD:          True
LZHDGHLD:          True
LZPASSIST:         True
LZRASSIST:         True
LZYASSIST:         True
LZTQHLD:           True
RZCUDOT:          -0.655
RZCROC:           -630
LZFLARE:           True
RZFLAREH:          50.0
Field_Elevation:   248.6
LZCRADALT:         True
LZPITALTHLD:       True
LDATRMON(8):       0
*----------------------------------------------

*----------------------------------------------
Add_Criteria: wow_l
Add_Criteria: wow_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r

*----------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     34.00     4.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: radalt         10.0    10.0     -400.0      800.0    400.0
Plot: kcas            3.0               80.0      120.0     20.0
Plot: hp                              -500.0     1000.0    500.0
Plot: ins_vt_spd                     -1000.0     1000.0   1000.0
Plot: ins_alt        10.0    10.0     -400.0      800.0    400.0

Plot: alpha_true      1.5               -8.0        8.0      8.0
Plot: beta_true                         -5.0       10.0      5.0
Plot: pitch_att       1.5              -10.0       10.0     10.0
Plot: roll_att                          -5.0        5.0      5.0

Plot: heading                          -10.0       20.0     10.0
Plot: ele_trim                           4.0        8.0      2.0
Plot: ele_def                          -20.0       20.0     20.0
Plot: pcp                               -5.0        5.0      5.0

Plot: pcf             5.0    10.0     -250.0      250.0    250.0
Plot: pitch_rate                       -10.0       20.0     10.0
Plot: roll_rate                        -10.0       10.0     10.0
Plot: yaw_rate                         -10.0        5.0      5.0

Plot: rud_trim                          -5.0       10.0      5.0
Plot: rud_def                           -5.0       10.0      5.0
Plot: prpp_l                           -10.0        5.0      5.0
Plot: prpf                             -40.0       40.0     40.0

Plot: ail_trim                          -5.0        5.0      5.0
Plot: pwf                              -40.0       40.0     40.0
Plot: pwp                              -20.0       20.0     20.0
Plot: ail_def_l                          0.0       10.0      5.0

Plot: ail_def_r                          0.0       10.0      5.0
Plot: pitch_acc                        -40.0       40.0     40.0
Plot: roll_acc                         -50.0       50.0     50.0
Plot: yaw_acc                         -400.0      200.0    200.0

Plot: ax_corrctd                      -360.0        0.0    180.0
Plot: ay_corrctd                      -250.0      250.0    250.0
Plot: az_corrctd                       -50.0       50.0     50.0
Plot: pla_l                            -10.0       10.0     10.0

Plot: pla_r                             -4.0        4.0      4.0
Plot: torque_l                       -1000.0     1000.0   1000.0
Plot: torque_r                       -1000.0     2000.0   1000.0
Plot: fn_l                           -2000.0     2000.0   2000.0

Plot: fn_r                           -2000.0     2000.0   2000.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0

Add_Criteria: ins_alt
@
