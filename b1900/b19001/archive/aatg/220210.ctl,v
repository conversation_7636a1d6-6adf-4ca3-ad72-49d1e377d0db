head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.34;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* <PERSON>yn <PERSON> to Roll Input
*--------------------------------------------


#            Var    x_off  y_off     y_mult
Crit_Offset: pwp    -0.02   0.003    1

FDEMATAB:  1.100
LFXAILT: False

*-----------------------------------------
KAXPMODE:  3
KAXRMODE:  3
KAXYMODE:  3
*-----------------------------------------

ZERO_RATES:  True

*-----------------------------------------------------
#           Min       <PERSON>    Delta
X_Scale:   0.00     30.00     2.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    <PERSON>    Delta
#                    ----       ----       ---    ---    -----
*Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
*Plot: ail_def_l                           -20.0   20.0    20.0
*Plot: ail_def_r                           -20.0   20.0    20.0


pre_trim: *
pre_trim: C CLS_RBDFASTOFF(2) 1        ! fast release for dynamics
pre_trim: C CLS_RBDGFASTOFF(2) 100       ! speed up release for dynamics
pre_trim: *

clean_up: *
clean_up: C CLS_RBDFASTOFF(2) 0
clean_up: *
@
