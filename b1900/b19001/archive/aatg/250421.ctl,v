head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.37;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Dir <PERSON>rl with Asym Rev Thrust
*--------------------------------------------


*-----------------------------------------
*
*-----------------------------------------
KAXPMODE:  4
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  0
*-----------------------------------------
On_Ground:       True
Pre_Trim: FMAXPFOR = 310
Post_Trim: FRA = 0.05235
IFXTRIM:         0
Field_Elevation: 316.0
*RTCVG:           83.8
RCXRUPED: -0.3
QTG_Test_Airport:  Takeoff   500   0.0               0.0               0.0  0.0  183.0
*-----------------------------------------
FMNOWINDS:   False
default_lat_rates: False
LZBPHLD:   True
LZTQHLD:   False
LZPASSIST: True
LZRASSIST: True
LFXSTABT:  False
LFXAILT:   False
LFXRUDT:   False
LZNASSIST:       False
LZT_HDG_RWY_HLD: False
LZHDGHLD:        False
LZCRADALT:       False
LZPITALTHLD:     False
LZTASSIST:       True
LZYASSIST:       True
LZRWYHLD:        False

*-----------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: ins_gnd_dist
Add_Criteria: gps_dist
*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00    110.00    10.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: rud_trim                            -20.0   20.0    20.0
Plot: rud_def                             -20.0   20.0    20.0
Plot: prpp_l                              -20.0   20.0    20.0
*Plot: prpf                                -20.0   20.0    20.0
Plot: pla_l                               -20.0   20.0    20.0
Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
*Plot: fn_l                                -20.0   20.0    20.0
*Plot: fn_r                                -20.0   20.0    20.0
Plot: radalt                              -20.0   20.0    20.0
Plot: pitch_att                           -20.0   20.0    20.0
Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
Plot: pitch_rate                          -20.0   20.0    20.0
Plot: roll_rate                           -20.0   20.0    20.0
Plot: yaw_rate                            -20.0   20.0    20.0
Plot: alpha_true                          -20.0   20.0    20.0
Plot: beta_true                           -20.0   20.0    20.0
Plot: kcas            5.0                 -20.0   20.0    20.0
Plot: hp                                  -20.0   20.0    20.0
Plot: ins_vt_spd                          -20.0   20.0    20.0
Plot: ele_trim                            -20.0   20.0    20.0
Plot: ele_def                             -20.0   20.0    20.0
Plot: pcp                                 -20.0   20.0    20.0
*Plot: pcf                                 -20.0   20.0    20.0
Plot: ail_trim                            -20.0   20.0    20.0
*Plot: pwf                                 -20.0   20.0    20.0
Plot: pwp                                 -20.0   20.0    20.0
Plot: ail_def_l                           -20.0   20.0    20.0
Plot: ail_def_r                           -20.0   20.0    20.0
Plot: tbp_l                               -20.0   20.0    20.0
Plot: tbp_r                               -20.0   20.0    20.0
*Plot: tbf_l                               -20.0   20.0    20.0
*Plot: tbf_r                               -20.0   20.0    20.0
Plot: pitch_acc                           -20.0   20.0    20.0
Plot: roll_acc                            -20.0   20.0    20.0
Plot: yaw_acc                             -20.0   20.0    20.0
Plot: ax_corrctd                          -20.0   20.0    20.0
Plot: ay_corrctd                          -20.0   20.0    20.0
Plot: az_corrctd                          -20.0   20.0    20.0

PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_GAIN          0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_GAIN'
PLOT_LINE: PLOT RDATIME CLS_NW_SPEED                0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_SPEED'
PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_ANG           0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_ANG'
PLOT_LINE: PLOT RDATIME CLS_NW_CASTOR_VEL           0      64      4  'Time (sec)'      0       1       .1  'CLS_NW_CASTOR_VEL'

@
