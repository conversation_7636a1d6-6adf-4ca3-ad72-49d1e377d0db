head	1.1;
access;
symbols;
locks; strict;
comment	@# @;


1.1
date	2008.01.30.02.42.27;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@*--------------------------------------------
* Rejected Takeoff
*--------------------------------------------

LFI_Table:    PCP         1.0     0.175     0.206186  0.0
LFI_Table:    PWP         1.0     0.175     0.010870  0.0
LFI_Table:    PRPP_L      1.0     0.175    -0.030303  0.0
LFI_Table:    ELE_DEF     1.0     0.175    -0.04      0.0 T
LFI_Table:    AIL_DEF_L   1.0     0.175     0.05      0.0
LFI_Table:    RUD_DEF     1.0     0.175    -0.037037  0.0
LFI_Table:    HP          1.0     0.4       1.0       0.0
LFI_Table:    RADALT      1.0     0.4       1.0       0.0
LFI_Table:    KCAS        1.0     0.4       1.0       0.0
LFI_Table:    PITCH_ATT   1.0     0.4       1.0       0.0
LFI_Table:    ROLL_ATT    1.0     0.7       1.0       0.0
LFI_Table:    HEADING     1.0     0.4       1.0       0.0
LFI_Table:    ALPHA_TRUE  1.0     0.4       1.0       0.0
LFI_Table:    BETA_TRUE   1.0     0.4       1.0       0.0
LFI_Table:    PLA_L       1.0     0.0       1.0       0.0
LFI_Table:    PLA_R       1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_L    1.0     0.0       1.0       0.0
LFI_Table:    TORQUE_R    1.0     0.0       1.0       0.0
LFI_Table:    ELE_TRIM    1.0     0.0       1.0       0.0
LFI_Table:    RUD_TRIM    1.0     0.0       1.0       0.0
LFI_Table:    WNDVEL_CAL  1.0     0.0       1.0       0.0  
LFI_Table:    WNDDIR_CAL  1.0     0.0       1.0       0.0
LFI_Table:    NWD         1.0     0.0       1.0       0.0
LFI_Table:    BRK_PX_L    1.0     0.350     1.0       0.0 T
LFI_Table:    BRK_PX_R    1.0     0.350     1.0       0.0 T
  
*-----------------------------------------

*-----------------------------------------
LFI_Line: *
LFI_Line: LFI    RDATIME  KAXPMODE
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0      0
LFI_Line: 9.3      0
LFI_Line: 9.301    4
LFI_Line: 25.600   4
LFI_Line: 25.601   0
LFI_Line: 500.0    0
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZREVERSE(1)
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 16.4   0
LFI_Line: 16.401 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZREVERSE(2)
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    0
LFI_Line: 16.4   0
LFI_Line: 16.401 1
LFI_Line: 500.0  1
LFI_Line: END
LFI_Line: *
LFI_Line: LFI    RDATIME  LZTQHLD
LFI_Line: ABS    T
LFI_Line: XMULT  1.00000
LFI_Line: XOFF   0.00000
LFI_Line: YMULT  1.00000
LFI_Line: YOFF   0.00000
LFI_Line: 0.0    1
LFI_Line: 16.4   1
LFI_Line: 16.401 0
LFI_Line: 500.0  0
LFI_Line: END
LFI_Line: *
* LFI_Line: LFI    RDATIME  FDEMTOEB
* LFI_Line: ABS    T
* LFI_Line: XMULT  1.00000
* LFI_Line: XOFF   0.00000
* LFI_Line: YMULT  1.00000
* LFI_Line: YOFF   0.00000
* LFI_Line: 0.0    11.0
* LFI_Line: 8.4    11.0
* LFI_Line: 8.9    0
* LFI_Line: 22.0   0
* LFI_Line: 22.001 11.0
* LFI_Line: 23.0   11.0
* LFI_Line: 35.0   11.0
* LFI_Line: END
* LFI_Line: *
* LFI_Line: LFI    RDATIME  FDEMTOER
* LFI_Line: ABS    T
* LFI_Line: XMULT  1.00000
* LFI_Line: XOFF   0.00000
* LFI_Line: YMULT  1.00000
* LFI_Line: YOFF   0.00000
* LFI_Line: 0.0    11.0
* LFI_Line: 8.4    11.0
* LFI_Line: 8.9    0
* LFI_Line: 22.0   0
* LFI_Line: 22.001 11.0
* LFI_Line: 23.0   11.0
* LFI_Line: 35.0   11.0
* LFI_Line: END
*-----------------------------------------

*-----------------------------------------
PRE_TRIM: NTPXSICO = 0.007907
On_Ground:       True
IFXTRIM:         0
Field_Elevation: 848.1
RTCVG:           0.0
* SET_WINDS: True
* CMD_WIND_SPEED:  23.115   ! Ground speed data that went into wind calc
* CMD_WIND_DIR:    181.82   ! was offset +1.875 kts, so offset put into winds
* RZCTQ(1): 3650.0
* RZCTQ(2): 3650.0

*-----------------------------------------

*-----------------------------------------
KAXPMODE:  0
KAXRMODE:  4
KAXYMODE:  4
KAXNMODE:  3
*-----------------------------------------

*-----------------------------------------
FMNOWINDS:   False
default_lat_rates: False
LZBPHLD:   True
LZTQHLD:   True
LZPASSIST: True
LZRASSIST: True
LFXSTABT:  False
LFXAILT:   False
LFXRUDT:   False
LZNASSIST:       True
LZT_HDG_RWY_HLD: True
LZHDGHLD:        True
LZCRADALT:       False
LZPITALTHLD:     False
LZTASSIST:       True
LZYASSIST:       True
LZRWYHLD:        False
RZCUDOT:         0.0
RZCQDS:          0.0
Speed_Signal:     ins_gnd_sp
Init_Speed:       4.0
Init_Speed_Mode:  Increase
Final_Speed:      2.1
Final_Speed_Mode: Decrease
QTG_Test_Airport: Takeoff  848  0.0   0.0   0.0  0.0   181

*--------------------------------------------------

*--------------------------------------------------
FDEMNW:      0.0
FSETNW:     True
*--------------------------------------------------

*--------------------------------------------------
RCXLONSK:  -0.5500
RCXLATSK:   0.1570
RCXRUPED:   0.0897

*------------------------------------------------------
Add_Criteria: brk_px_l
Add_Criteria: brk_px_r
Add_Criteria: tbp_l
Add_Criteria: tbp_r
Add_Criteria: nwd
Add_Criteria: ins_gnd_dist
Add_Criteria: gps_dist
Add_Criteria: brk_temp_l
Add_Criteria: brk_temp_r

#            Var       x_off  y_off     y_mult
*Crit_Offset: ins_gnd_sp  0   -1.875      1
*Crit_Offset: gndspd_c    0   -1.875      1
Crit_Offset: ins_gnd_dist 0   -64.083     1
Crit_Offset: brk_temp_l   0  -100.0       1
Crit_Offset: brk_temp_r   0  -100.0       1
*------------------------------------------------------

*-----------------------------------------------------
#           Min       Max    Delta
X_Scale:   0.00     55.00     5.00
*-----------------------------------------------------

#                    TOLa       TOLb       Min    Max    Delta
#                    ----       ----       ---    ---    -----
Plot: ins_gnd_sp                          -20.0   20.0    20.0
Plot: ins_gnd_dist                        -20.0   20.0    20.0
* Plot: gndspd_c                            -20.0   20.0    20.0
* Plot: kcas                                -20.0   20.0    20.0
* Plot: hp                                  -20.0   20.0    20.0
* Plot: ins_vt_spd                          -20.0   20.0    20.0
* Plot: radalt                              -20.0   20.0    20.0
* Plot: tbp_l                               -20.0   20.0    20.0
* Plot: tbp_r                               -20.0   20.0    20.0
Plot: brk_px_l                            -20.0   20.0    20.0
Plot: brk_px_r                            -20.0   20.0    20.0
* Plot: tbf_l                               -20.0   20.0    20.0
* Plot: tbf_r                               -20.0   20.0    20.0
Plot: pitch_att                           -20.0   20.0    20.0
* Plot: roll_att                            -20.0   20.0    20.0
Plot: heading                             -20.0   20.0    20.0
* Plot: alpha_true                          -20.0   20.0    20.0
* Plot: beta_true                           -20.0   20.0    20.0
* Plot: pitch_rate                          -20.0   20.0    20.0
* Plot: roll_rate                           -20.0   20.0    20.0
* Plot: yaw_rate                            -20.0   20.0    20.0
* Plot: ele_trim                            -20.0   20.0    20.0
* Plot: ele_def                             -20.0   20.0    20.0
* Plot: pcp                                 -20.0   20.0    20.0
* Plot: pcf                                 -20.0   20.0    20.0
* Plot: ail_trim                            -20.0   20.0    20.0
* Plot: pwf                                 -20.0   20.0    20.0
* Plot: pwp                                 -20.0   20.0    20.0
* Plot: ail_def_l                           -20.0   20.0    20.0
* Plot: ail_def_r                           -20.0   20.0    20.0
* Plot: rud_trim                            -20.0   20.0    20.0
* Plot: rud_def                             -20.0   20.0    20.0
* Plot: prpp_l                              -20.0   20.0    20.0
* Plot: prpf                                -20.0   20.0    20.0
* Plot: ax_corrctd                          -20.0   20.0    20.0
* Plot: ay_corrctd                          -20.0   20.0    20.0
* Plot: az_corrctd                          -20.0   20.0    20.0
* Plot: pitch_acc                           -20.0   20.0    20.0
* Plot: roll_acc                            -20.0   20.0    20.0
* Plot: yaw_acc                             -20.0   20.0    20.0
* Plot: pla_l                               -20.0   20.0    20.0
* Plot: pla_r                               -20.0   20.0    20.0
Plot: torque_l                            -20.0   20.0    20.0
Plot: torque_r                            -20.0   20.0    20.0
Plot: brk_temp_l                            -20.0   20.0    20.0
Plot: brk_temp_r                            -20.0   20.0    20.0
* Plot: fn_l                                -20.0   20.0    20.0
* Plot: fn_r                                -20.0   20.0    20.0
* Plot: wnddir_cal                          -20.0   20.0    20.0
* Plot: wndvel_cal                          -20.0   20.0    20.0

PLOT_LINE: PLOT RDATIME FLIOMEG               0      64      4  'Time (sec)'      0       1       .1  'FLIOMEG'                                          !
PLOT_LINE: PLOT RDATIME FLOOMEG               0      64      4  'Time (sec)'      0       1       .1  'FLOOMEG'                                          !
PLOT_LINE: PLOT RDATIME FRIOMEG               0      64      4  'Time (sec)'      0       1       .1  'FRIOMEG'                                          !
PLOT_LINE: PLOT RDATIME FROOMEG               0      64      4  'Time (sec)'      0       1       .1  'FROOMEG'                                          !
PLOT_LINE: PLOT RDATIME FNDOLEO               0      64      4  'Time (sec)'      0       1       .1  'FNDOLEO'                                          !
PLOT_LINE: PLOT RDATIME FLDOLEO               0      64      4  'Time (sec)'      0       1       .1  'FLDOLEO'                                          !
PLOT_LINE: PLOT RDATIME FRDOLEO               0      64      4  'Time (sec)'      0       1       .1  'FRDOLEO'                                          !
*
@
