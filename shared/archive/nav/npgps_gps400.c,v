head	1.1;
access;
symbols;
locks; strict;
comment	@ * @;


1.1
date	2008.01.30.03.41.55;	author relmgr;	state Exp;
branches;
next	;


desc
@@


1.1
log
@Initial Revision
@@: Initial Revision
@
text
@/*****************************************************************************
* %OPLICENSE%
* RESTRICTED  -- SIMULATOR LICENSED DATA
* Copyright � 2008 OPINICUS Corporation.  All rights reserved.                 *
*                                                                              *
* This software is considered SIMULATOR LICENSED DATA.  It may not             *
* be copied or used except in accordance with the license agreement            *
* as defined in a file named 'SIMULATOR_LICENSED_DATA'.                        *
*                                                                              *
* The 'SIMULATOR_LICENSED_DATA' file is maintained and shall continue          *
* to be maintained in this directory or the standard directory                 *
* (/sds_var1/flyright/lv3/std).                                                *
* You should have received a copy of this license along with this software;    *
* If not, contact OPINICUS Corporation (www.opinicus.com)                      *
*                                                                              *
* This software shall reside only on the Software Development                  *
* Server (SDS), the Host Computer,  and/or the REALFeel Computer (RFC),        *
* and/or the REALCue Computer and have                                         *
* its access restricted to only authorized personnel for the                   *
* sole purpose of maintaining and/or modifying the licensed system.            *
* Backup copies are authorized provided that the backups are used solely       *
* for restoring the RFC, RealCue Computer, and/or the SDS.                     *
* %OPLICENSE%
*****************************************************************************
*/
/* File:        npgps_gps400.c
 * Description: Garmin GPS 400 Emulation 
 * Revision:    -
 * Date:        08 Mar 07
 * Where used:  Linux
 * Property of: Opinicus
 */

#include <stdio.h>
#include <math.h>
#include "npgps_gps400.h"
#include "npgps_gps400.so"
#include <fcntl.h>   /* for ioctls */
#include <termios.h> /* for serial */
#include <stdlib.h>

#define RX_DEBUG  /* uncomment for RX debug messages */
#define DLE  0x10
#define STX  0x02
#define ETX  0x03
#define CR   0x0D
#define LF   0x0A

/* --- globals --- */
struct termios oldmode2;  /* old serial mode  */
struct termios newmode2;  /* new  serial mode */
int    sfd2;              /* serial port file descriptor GPS */

//struct gpsshmtyp *gpsshm;
unsigned char xmtbuf[512];
unsigned char rcvbuf[256];
unsigned char msgbuf[256];
unsigned char rxbuf[RXMAX];  /* rx data buffer */
int txnrdGar = 0;
int txnwrGar = 0;

/* --- prototypes --- */
void npgps_gps400(void);
void npgps_gps400_init(void);
void npgps_gps400_close(void);
int gps400_initserial(void);
int gps400_initdata(void);
int xmitmsgGar(void);
int packmsgGar(void);
int txpackGar(int xmtndx, int len);
int rcvremuGar(void);

/* -------------------------------------------------------------------- */
/* --- main program --- */
void npgps_gps400()
{
   static int skip = 1;
   static int powerCheck = 1;
   int i;
   int stat, rdstat;

   /* Check for correct GPS */
   if (*NPGPSSELECT)
      return;
   
   /* --- Check for new data --- */
   rdstat = read(sfd2, &(rxbuf[gpsshm->rxnwr]), 1);
   if (rdstat == 1) {
       gpsshm->rxnwr++;
       while (read(sfd2, &(rxbuf[gpsshm->rxnwr]), 1))
       { /* read until the buffer is empty */
          gpsshm->rxnwr++;
          if (gpsshm->rxnwr == gpsshm->rxnrd) {
             gpsshm->rxwrap++;
             printf("rxbuf wrapped\n");
          }
          if (gpsshm->rxnwr >= RXMAX) gpsshm->rxnwr = 0;
       }
    }

    /* Process ouputs at 1Hz */
    if (skip < 1){
       rcvremuGar();  /* receiver emulation */
       packmsgGar();  /* pack xmt buffer */
       xmitmsgGar();  /* transmit xmt buffer */
       skip = 2;

       /* Check if the GPS is on */
       if(*QDGPS371 == 4911) //random number still set from last cycle
         powerCheck--;
       else // reset by powered GPS
         powerCheck = 3;
       if(powerCheck < 1) { // GPS is off so clear ARINC data 
         *QDGPS275 = 0;
         QDGPS275SSM[0] = 0;
         QDGPS116SSM[0] = 0;
         QDGPS310SSM[0] = 0;
         QDGPS115SSM[0] = 0;
         QDGPS251SSM[0] = 0;
       }
       *QDGPS371 = 4911; //set the GPS type to a random number to see if the GPS clears it.
    }
    skip--;

    /* Set CB status */
   *KXGPS_RELAY = (*LC_GPS_CB && !(*MBNGPSFAIL));
}

/* -------------------------------------------------------------------- */
void npgps_gps400_init()
{
   /* Check for correct GPS */
   if (*NPGPSSELECT)
      return;
/* Init */
   printf("*** init GPS data \n");
   gps400_initdata();
   printf("*** init GPS serial port \n");
   gps400_initserial();
}

/* -------------------------------------------------------------------- */
int gps400_initserial(void)
{
   /* --- open serial port --- */
   sfd2 = open("/dev/ttyS1", O_RDWR | O_NOCTTY | O_NDELAY);
   if (sfd2 < 0) {
      printf("***Error: GPS serial port open failed\n");
      perror("open failed:");
      return(sfd2);
   }
   /* --- get old mode --- */
   tcgetattr(sfd2, &oldmode2);
   /* --- set new mode --- */
   newmode2.c_iflag = IGNBRK; /* ignore break */
   newmode2.c_oflag = 0; /* no post processing */
   newmode2.c_cflag = B9600 | CS8 | CLOCAL | CREAD;
   newmode2.c_lflag = 0; /* no line discipline porcessing */
   newmode2.c_line = 0;  /* no line discipline */
   newmode2.c_cc[VMIN] = oldmode2.c_cc[VMIN];
   newmode2.c_cc[VTIME] = oldmode2.c_cc[VTIME];
   tcsetattr(sfd2, TCSADRAIN, &newmode2);

   return(0);
}

/* -------------------------------------------------------------------- */
int xmitmsgGar(void)
{
   int i;
   char STXchar = STX;
   char ETXchar = ETX;

   /* Transmit the buffer */   
   write(sfd2, &STXchar, 1);
   write(sfd2, &xmtbuf[0], txnwrGar);
   write(sfd2, &ETXchar, 1);
   txnrdGar = 0;
   txnwrGar = 0;
 
   return(0);
}

/* -------------------------------------------------------------------- */
int packmsgGar(void)
{
   static char satNum = '0';
   unsigned char buf[33];
   int i, DegInt, MinInt, j;

   txnwrGar = 0;  /* init xmtbuf next write pointer */
   /* --- ID: a Latitude --- */
   msgbuf[0] = 'a'; /* Message ID */
   /* Set North or South */
   if (gpsshm->n_latr > 0.0) 
      msgbuf[1] = 'N';
   else
      msgbuf[1] = 'S';
   msgbuf[2] = '0'; /* Spacer */
   /* Set Degrees */
   DegInt = abs((gpsshm->n_latr * RAD2DEG));   /* Latitude in radians (real) */
   sprintf(buf,"%03d",DegInt);
   msgbuf[3] = buf[0];
   if (*MBNGPSINTEG) {
      msgbuf[4] = '-';
      msgbuf[5] = '-';
   }
   else {
      msgbuf[4] = buf[1];
      msgbuf[5] = buf[2];
   }
   msgbuf[6] = '0'; /* Spacer */

   /* set minutes */
   MinInt = abs(((gpsshm->n_latr * RAD2DEG) - DegInt) * 6000);
   sprintf(buf,"%04d",MinInt);
   if (*MBNGPSINTEG) {
      msgbuf[7] = '-';
      msgbuf[8] = '-';
      msgbuf[9] = '-';
      msgbuf[10] = '-';
   }
   else {
      msgbuf[7] = buf[0];
      msgbuf[8] = buf[1];
      msgbuf[9] = buf[2];
      msgbuf[10] = buf[3];
   }
   txnwrGar = txpackGar(txnwrGar, 11);

   /* --- ID: b Longitude --- */
   msgbuf[0] = 'b'; /* Message ID */
   /* Set East or West */
   if (gpsshm->n_lonr > 0.0)
      msgbuf[1] = 'E';
   else
      msgbuf[1] = 'W';
   msgbuf[2] = '0'; /* Spacer */
   /* Set Degrees */
   DegInt = abs(gpsshm->n_lonr * RAD2DEG);   /* Latitude in radians (real) */
   sprintf(buf,"%03d",DegInt);
   if (*MBNGPSINTEG) {
      msgbuf[3] = '-';
      msgbuf[4] = '-';
      msgbuf[5] = '-';
   } 
   else {
      msgbuf[3] = buf[0];
      msgbuf[4] = buf[1];
      msgbuf[5] = buf[2];
   } 
   msgbuf[6] = '0'; /* Spacer */

   /* set minutes */
   MinInt = abs(((gpsshm->n_lonr * RAD2DEG) + DegInt) * 6000);
   sprintf(buf,"%04d",MinInt);
   if (*MBNGPSINTEG) {
      msgbuf[7] = '-';
      msgbuf[8] = '-';
      msgbuf[9] = '-';
      msgbuf[10] = '-';
   }
   else {
      msgbuf[7] = buf[0];
      msgbuf[8] = buf[1];
      msgbuf[9] = buf[2];
      msgbuf[10] = buf[3];
   }
   txnwrGar = txpackGar(txnwrGar, 11);

   /* --- ID: c Altitude in Feet --- */
   msgbuf[0] = 'c'; /* Message ID */
   i = abs(gpsshm->r_altmet * MET2FT);
   sprintf(buf,"%05d",i);
   if (*MBNGPSINTEG) {
      msgbuf[1] = '-';
      msgbuf[2] = '-';
      msgbuf[3] = '-';
      msgbuf[4] = '-';
      msgbuf[5] = '-';
   }
   else {
      msgbuf[1] = buf[0];
      msgbuf[2] = buf[1];
      msgbuf[3] = buf[2];
      msgbuf[4] = buf[3];
      msgbuf[5] = buf[4];
   }
   txnwrGar = txpackGar(txnwrGar, 6);

   /* --- ID: d Eastst velocity, in knots --- */
   msgbuf[0] = 'd'; /* Message ID */
   /* Set East or West */
   if (gpsshm->r_evelms > 0.0)
      msgbuf[1] = 'E';
   else
      msgbuf[1] = 'W';
   i = abs(gpsshm->r_evelms * MS2KNOT);
   sprintf(buf,"%03d",i);
   if (*MBNGPSINTEG) {
      msgbuf[2] = '-';
      msgbuf[3] = '-';
      msgbuf[4] = '-';
   }
   else {
      msgbuf[2] = buf[0];
      msgbuf[3] = buf[1];
      msgbuf[4] = buf[2];
   }
   txnwrGar = txpackGar(txnwrGar, 5);

   /* --- ID: e North velocity, in knots --- */
   msgbuf[0] = 'e'; /* Message ID */
   /* Set North or South */
   if (gpsshm->r_nvelms > 0.0)
      msgbuf[1] = 'N';
   else
      msgbuf[1] = 'S';
   i = abs(gpsshm->r_nvelms * MS2KNOT);
   sprintf(buf,"%03d",i);
   if (*MBNGPSINTEG) {
      msgbuf[2] = '-';
      msgbuf[3] = '-';
      msgbuf[4] = '-';
   }
   else {
      msgbuf[2] = buf[0];
      msgbuf[3] = buf[1];
      msgbuf[4] = buf[2];
   }

   txnwrGar = txpackGar(txnwrGar, 5);

   /* --- ID: f Vertical speed, in feet per minute --- */
   msgbuf[0] = 'f'; /* Message ID */
   /* Set Up or Down */
   if (gpsshm->r_uvelms > 0.0)
      msgbuf[1] = 'U';
   else
      msgbuf[1] = 'D';
   i = abs(gpsshm->r_uvelms * MET2FT * 60.0);
   sprintf(buf,"%04d",i);
   if (*MBNGPSINTEG) {
      msgbuf[2] = '-';
      msgbuf[3] = '-';
      msgbuf[4] = '-';
      msgbuf[5] = '-';
   }
   else {
      msgbuf[2] = buf[0];
      msgbuf[3] = buf[1];
      msgbuf[4] = buf[2];
      msgbuf[5] = buf[3];
   }
   txnwrGar = txpackGar(txnwrGar, 6);

   /* --- ID: g - misc inputs --- */
   msgbuf[0] = 'g'; /* Message ID */
   if (*MBNGPSINTEG)
     msgbuf[1] = 0x82; /* status */
   else
     msgbuf[1] = 0x80; /* status */
   txnwrGar = txpackGar(txnwrGar, 2);

   /* --- ID: h - Main OBS input --- */
   msgbuf[0] = 'h'; /* Message ID */
   msgbuf[1] = '0';
   msgbuf[2] = '0';
   msgbuf[3] = '0';
   msgbuf[4] = '0';
   txnwrGar = txpackGar(txnwrGar, 5);

   /* --- ID: i - Fuel on board, in gallons --- */
   msgbuf[0] = 'i'; /* Message ID */
   msgbuf[1] = '-';
   msgbuf[2] = '-';
   msgbuf[3] = '-';
   txnwrGar = txpackGar(txnwrGar, 4);

   /* --- ID: j - Fuel flow, in gallons per hour --- */
   msgbuf[0] = 'j'; /* Message ID */
   msgbuf[1] = '-';
   msgbuf[2] = '-';
   msgbuf[3] = '-';
   txnwrGar = txpackGar(txnwrGar, 4);

   /* --- ID: k - Date and Time --- */
   msgbuf[0] = 'k'; /* Message ID */
   msgbuf[1] = '2';
   msgbuf[2] = '0';
   msgbuf[3] = '0';
   msgbuf[4] = '6';
   msgbuf[5] = '0';
   msgbuf[6] = '3';
   msgbuf[7] = '1';
   msgbuf[8] = '6';
   i = gpsshm->r_hour;
   sprintf(buf,"%02d",i);
   msgbuf[9] = buf[0];
   msgbuf[10] = buf[1];

   i = gpsshm->r_min;
   sprintf(buf,"%02d",i);
   msgbuf[11] = buf[0];
   msgbuf[12] = buf[1];

   i = gpsshm->r_sec;
   sprintf(buf,"%02d",i);
   msgbuf[13] = buf[0];
   msgbuf[14] = buf[1];
   txnwrGar = txpackGar(txnwrGar, 15);

   /* --- ID: l Pressure Altitude in Feet --- */
   msgbuf[0] = 'l'; /* Message ID */
   i = abs(*KXCBARI);
   sprintf(buf,"%05d",i);
   msgbuf[1] = buf[0];
   msgbuf[2] = buf[1];
   msgbuf[3] = buf[2];
   msgbuf[4] = buf[3];
   msgbuf[5] = buf[4];
   txnwrGar = txpackGar(txnwrGar, 6);

   /* --- ID: m - Radio frequency --- */
   msgbuf[0] = 'm'; /* Message ID */
   msgbuf[1] = '-';
   msgbuf[2] = '-';
   msgbuf[3] = '-';
   msgbuf[4] = '-';
   msgbuf[5] = '-';
   msgbuf[6] = '-';
   msgbuf[7] = '-';
   msgbuf[8] = '-';
   msgbuf[9] = '-';
   msgbuf[10] = '-';
   msgbuf[11] = '-';
   msgbuf[12] = '-';
   msgbuf[13] = '-';
   msgbuf[14] = '-';
   msgbuf[15] = '-';
   msgbuf[16] = '-';
   msgbuf[17] = '-';
   msgbuf[18] = '-';
   txnwrGar = txpackGar(txnwrGar, 19);

   /* --- ID: q - Satellite information --- */
   for (satNum='0'; satNum <= '9'; satNum++) {
      i = (int)(satNum - '0');
      msgbuf[0] = 'q'; /* Message ID */
      msgbuf[1] = satNum;
      sprintf(buf,"%03d",gpsshm->r_satsnr[i]);
      msgbuf[2] = buf[0];
      msgbuf[3] = buf[1];
      msgbuf[4] = buf[2];
      if (gpsshm->r_sathel[i] == 1)
         msgbuf[5] = 'B';
      else if (gpsshm->r_sathel[i] == 2)
         msgbuf[5] = 'W';
      else if (gpsshm->r_sathel[i] == 3)
         msgbuf[5] = 'G';
      else
         msgbuf[5] = '-';
      if (gpsshm->r_sattrk[i])
         msgbuf[6] = 'T';
      else
         msgbuf[6] = 'N';
      sprintf(buf,"%02d",gpsshm->r_satelv[i]);
      msgbuf[7] = buf[0];
      msgbuf[8] = buf[1];

      sprintf(buf,"%03d",gpsshm->r_azang[i]);
      msgbuf[9] = buf[0];
      msgbuf[10] = buf[1];
      msgbuf[11] = buf[2];
      txnwrGar = txpackGar(txnwrGar, 12);
   }

   /* --- ID: q - RAIM status --- */
   msgbuf[0] = 'r'; /* Message ID */
   if (*MBNGPSINTEG)
      msgbuf[1] = 'N'; /* RAIM status */
   else
      msgbuf[1] = 'A'; /* RAIM status */
   txnwrGar = txpackGar(txnwrGar, 2);

   return(0);
}  /* --- end packmsgGar --- */

/* -------------------------------------------------------------------- */
int txpackGar(int xmtndx, int len)
{
   int i;

   for (i=0; i<len; i++) {
      xmtbuf[xmtndx++] = msgbuf[i];
   }
   xmtbuf[xmtndx++] = CR;
   xmtbuf[xmtndx++] = LF;

   return(xmtndx);
}

/* -------------------------------------------------------------------- */
int rcvremuGar(void)
{
   /* I'm trying not to handle inputs yet so I'll just clear this buffer */
   gpsshm->rxnwr = 0;

   /* compute time */
   gpsshm->r_sec++;
   if (gpsshm->r_sec >= 60) {
      gpsshm->r_min++; gpsshm->r_sec = 0;
   }
   if (gpsshm->r_min >= 60) {
      gpsshm->r_hour++; gpsshm->r_min = 0;
   }
   if (gpsshm->r_hour >= 24) {
      gpsshm->r_hour = 0;
   }
  
   if (*MBNGPSINTEG) {
      /* Satellite Health */
      /* 0=No data, 1=Bad, 2=Weak, 3=Good, 4=DeSel */
      gpsshm->r_sathel[0] = 0;
      gpsshm->r_sathel[1] = 0;
      gpsshm->r_sathel[2] = 1;
      gpsshm->r_sathel[3] = 0;
      gpsshm->r_sathel[4] = 3;
      gpsshm->r_sathel[5] = 0;
      gpsshm->r_sathel[6] = 0;
      gpsshm->r_sathel[7] = 0;

      /* Satellite SNR (dB/Hz) (0-999)*/
      gpsshm->r_satsnr[0] = 0;
      gpsshm->r_satsnr[1] = 0;
      gpsshm->r_satsnr[2] = 36;
      gpsshm->r_satsnr[3] = 0;
      gpsshm->r_satsnr[4] = 39;
      gpsshm->r_satsnr[5] = 0;
      gpsshm->r_satsnr[6] = 0;
      gpsshm->r_satsnr[7] = 0;

      /* Satellite Tracking (1=True, 0=False) */
      gpsshm->r_sattrk[0] = 0;
      gpsshm->r_sattrk[1] = 0;
      gpsshm->r_sattrk[2] = 1;
      gpsshm->r_sattrk[3] = 0;
      gpsshm->r_sattrk[4] = 1;
      gpsshm->r_sattrk[5] = 0;
      gpsshm->r_sattrk[6] = 0;
      gpsshm->r_sattrk[7] = 0;
      return(0);
   }
   else {
      /* Satellite Health */
      /* 0=No data, 1=Bad, 2=Weak, 3=Good, 4=DeSel */
      gpsshm->r_sathel[0] = 3;
      gpsshm->r_sathel[1] = 2;
      gpsshm->r_sathel[2] = 3;
      gpsshm->r_sathel[3] = 3;
      gpsshm->r_sathel[4] = 3;
      gpsshm->r_sathel[5] = 1;
      gpsshm->r_sathel[6] = 3;
      gpsshm->r_sathel[7] = 3;

      /* Satellite SNR (dB/Hz) (0-999)*/
      gpsshm->r_satsnr[0] = 45;
      gpsshm->r_satsnr[1] = 38;
      gpsshm->r_satsnr[2] = 44;
      gpsshm->r_satsnr[3] = 46;
      gpsshm->r_satsnr[4] = 42;
      gpsshm->r_satsnr[5] = 35;
      gpsshm->r_satsnr[6] = 47;
      gpsshm->r_satsnr[7] = 49;

      /* Satellite Tracking (1=True, 0=False) */
      gpsshm->r_sattrk[0] = 1;
      gpsshm->r_sattrk[1] = 1;
      gpsshm->r_sattrk[2] = 1;
      gpsshm->r_sattrk[3] = 1;
      gpsshm->r_sattrk[4] = 1;
      gpsshm->r_sattrk[5] = 1;
      gpsshm->r_sattrk[6] = 1;
      gpsshm->r_sattrk[7] = 1;
   }
 
   /* --- Set global nav data --- */
   gpsshm->n_latr = *NGLAT * DEG2RAD;     /* Lattitude */
   gpsshm->n_lonr = *NGLON * DEG2RAD;     /* Longitude */
   gpsshm->n_gndsp = *FVPGND * KNOT2MS;    /* Groundspeed (m/s) */
   gpsshm->n_trkang = *NGTRKT * DEG2RAD;  /* Track Angle (degrees) */

   /* --- Compute nav data --- */
   gpsshm->r_altmet = *NGGA * FT2MET;  /* Altitude (meters) */
   gpsshm->r_evelms = *NGVEW * FT2MET;   /* East Vel (m/s)  */
   gpsshm->r_nvelms = *NGVNS * FT2MET;   /* North Vel (m/s)  */
   gpsshm->r_uvelms = *FDHP * FT2MET;   /* Up Vel (m/s)  */

   return(0);
}  /* === end rcvemu === */

/* -------------------------------------------------------------------- */
void npgps_gps400_close(void)
{
   /* Check for correct GPS */
   if (*NPGPSSELECT)
      return;
   tcsetattr(sfd2, TCSADRAIN, &oldmode2);
   close(sfd2);
   free(gpsshm);
}

/* -------------------------------------------------------------------- */
int gps400_initdata(void)
{
   int i;

   struct gpsshmtyp *gpsshm2;
   gpsshm2 = malloc(sizeof (struct gpsshmtyp));

   gpsshm = gpsshm2;

   /* --- init shared memory --- */
   gpsshm->done = 0;
   gpsshm->rxwrap = 0;
   gpsshm->rxbadid = 0;
   gpsshm->rxbid = 0;
   gpsshm->rxlen = 0;
   gpsshm->rxnwr = 0;
   gpsshm->rxnrd = 0;
   gpsshm->gpspwr = 1;  /* power ON */
   gpsshm->gpstmr = 0;  /* power ON timer */
   gpsshm->rcvrmode = 1;  /* cold start mode */
   gpsshm->rcvrstate = 0; /* init state */
   gpsshm->nav_valid = 0; /* sat nav data invalid */
   gpsshm->raimreq   = 0; /* no RAIM req active   */
   gpsshm->raimst[0] = 0; /*    RAIM available    */
   gpsshm->raimst[1] = 0; /*    RAIM available    */
   gpsshm->raimst[2] = 0; /*    RAIM available    */
   gpsshm->malf = 0;      /* no malfunctions      */
   gpsshm->lmalf = 0;     /* last pass malf       */
   gpsshm->ch_year = 7;
   gpsshm->ch_mon  = 3;
   gpsshm->ch_day  = 14;
   gpsshm->ch_hour = 16;
   gpsshm->ch_min  = 10;
   gpsshm->c_gs = 0.0;
   gpsshm->c_ta = 0.0;
   gpsshm->c_altf = 40.0;
   gpsshm->c_ns = 8;
   gpsshm->c_raimst[0] = 0;
   gpsshm->c_raimst[1] = 0;
   gpsshm->c_raimst[2] = 0;

   /* --- Get global nav data --- */
   gpsshm->n_latr =  0.48930;   /* Clearwater Lat 28.034667 N */
   gpsshm->n_llatr =  0.48930;  /* Clearwater Lat 28.034667 N */
   gpsshm->n_lonr = -1.44409;   /* Clearwater Lon 82.74020 W  */
   gpsshm->n_llonr = -1.44409;  /* Clearwater Lon 82.74020 W  */
   gpsshm->n_gndsp = 0.0;       /* Groundspeed (m/s)          */
   gpsshm->n_trkang = 0.0;      /* Track Angle (degrees)      */

   /* --- Compute nav data --- */
   gpsshm->r_hposerr = 100.0;  /* Hor. pos err (meters) */
   gpsshm->r_altmet = 4.1234;  /* Altitude (meters)     */
   gpsshm->r_laltmet = 4.1234; /* Altitude (meters)     */
   gpsshm->r_vposerr = 8.456;  /* Vert pos err (meters) */
   gpsshm->r_evelms = 0.0;     /* East Vel (m/s)        */
   gpsshm->r_nvelms = 0.0;     /* North Vel (m/s)       */
   gpsshm->r_uvelms = 0.0;     /* Up Vel (m/s)          */
   gpsshm->r_freqest = 99.0;   /* Rcvr osc freq error (ppm) */

   /* Satellite ID */
   gpsshm->r_satid[0] = 21;
   gpsshm->r_satid[1] =  1;
   gpsshm->r_satid[2] = 23;
   gpsshm->r_satid[3] = 25;
   gpsshm->r_satid[4] =  5;
   gpsshm->r_satid[5] = 17;
   gpsshm->r_satid[6] =  9;
   gpsshm->r_satid[7] = 19;

   /* Satellite Health */
   /* 0=No data, 1=Bad, 2=Weak, 3=Good, 4=DeSel */
   gpsshm->r_sathel[0] = 3;
   gpsshm->r_sathel[1] = 2;
   gpsshm->r_sathel[2] = 3;
   gpsshm->r_sathel[3] = 3;
   gpsshm->r_sathel[4] = 3;
   gpsshm->r_sathel[5] = 1;
   gpsshm->r_sathel[6] = 3;
   gpsshm->r_sathel[7] = 3;

   /* Satellite Elevation (5-90)*/
   gpsshm->r_satelv[0] = 72;
   gpsshm->r_satelv[1] = 55;
   gpsshm->r_satelv[2] = 49;
   gpsshm->r_satelv[3] = 37;
   gpsshm->r_satelv[4] = 22;
   gpsshm->r_satelv[5] = 18;
   gpsshm->r_satelv[6] = 15;
   gpsshm->r_satelv[7] = 41;

   /* Satellite SNR (dB/Hz) (0-999)*/
   gpsshm->r_satsnr[0] = 45;
   gpsshm->r_satsnr[1] = 38;
   gpsshm->r_satsnr[2] = 44;
   gpsshm->r_satsnr[3] = 46;
   gpsshm->r_satsnr[4] = 42;
   gpsshm->r_satsnr[5] = 35;
   gpsshm->r_satsnr[6] = 47;
   gpsshm->r_satsnr[7] = 49;

   /* Satellite Tracking (1=True, 0=False) */
   gpsshm->r_sattrk[0] = 1;
   gpsshm->r_sattrk[1] = 1;
   gpsshm->r_sattrk[2] = 1;
   gpsshm->r_sattrk[3] = 1;
   gpsshm->r_sattrk[4] = 1;
   gpsshm->r_sattrk[5] = 1;
   gpsshm->r_sattrk[6] = 1;
   gpsshm->r_sattrk[7] = 1;

   /* Satellite Azimuth Angle */
   gpsshm->r_azang[0] = 254;
   gpsshm->r_azang[1] = 163;
   gpsshm->r_azang[2] = 95;
   gpsshm->r_azang[3] = 326;
   gpsshm->r_azang[4] = 274;
   gpsshm->r_azang[5] = 137;
   gpsshm->r_azang[6] = 37;
   gpsshm->r_azang[7] = 149;

   /* --- Format date data --- */
   gpsshm->r_year = 7;
   gpsshm->r_mon = 10;
   gpsshm->r_day = 1;
   gpsshm->r_hour = 16;
   gpsshm->r_min = 0;
   gpsshm->r_sec = 0;

   /* --- Compute GPS State --- */
   /* 0=init, 1=ssky, 2=acq, 3=badcov,   */
   /* 4=nav, 5=navwdc, 6=navdeg, 7=dreck */
   gpsshm->gps_state = 4;

   /* GPS Integrity State .............. */
   gpsshm->gps_istate = 0; /* GPS Integrity State */

   /* GPS Masked Integrity Warning (1=true, 0=false) */
   gpsshm->gps_mwarn = 0;

   /* GPS Bad Coverage (1=true, 0=false) */
   gpsshm->gps_bcov = 0;

   /* GPS Altitude Aiding in use (1=true, 0=false) */
   gpsshm->gps_aaid = 0;

   /* GPS Nav Mode (0=en route, 1=terminal, 2=approach) */
   gpsshm->gps_nmode = 0;

   /* GPS Error Status  ...............*/
   gpsshm->gps_errst = 0;

   return(0);
}

/* ---============== end file npgps_gps400.c ================--- */
@
